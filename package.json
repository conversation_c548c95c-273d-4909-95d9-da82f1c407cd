{"name": "wph-coffee", "private": true, "version": "1.0.0", "type": "commonjs", "scripts": {"client-start": "vite", "client-start-host": "vite --host", "client-build": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build", "client-build:visualizer": "cross-env VITE_VISUALIZER=true NODE_OPTIONS=--max-old-space-size=4096 vite build", "client-build-askdoc-upload": "cross-env VITE_ASKDOC_SHOW_UPLOAD=true npm run client-build", "client-preview": "vite preview", "lint": "npm run check-all", "prisma-generate": "PRISMA_ENGINES_MIRROR=https://registry.npmmirror.com/-/binary/prisma prisma generate", "prisma-migrate": "PRISMA_ENGINES_MIRROR=https://registry.npmmirror.com/-/binary/prisma prisma migrate deploy", "server-build-inside": "tsc -p tsconfig.server.json", "server-build": "webpack", "server-start": "npx nodemon", "prod-start-inside": "node dist-server/server/server.js", "prod-start": "bytenode ./dist-server/server/bundle.min.jsc", "lint-staged": "lint-staged", "test": "cross-env NODE_ENV=test vitest", "test:once": "cross-env NODE_ENV=test vitest run", "test:watch": "cross-env NODE_ENV=test vitest watch", "test:coverage": "cross-env NODE_ENV=test vitest run --coverage", "check-all": "npm run prisma-generate && npm run check-ts && npm run check-eslint && npm run check-css", "check-ts": "tsc --noEmit", "check-eslint": "eslint src --ext .js,.jsx,.ts,.tsx --max-warnings 0", "check-css": "stylelint \"src/**/*.css\"", "deploy": "npm run client-build && sh ./scripts/deploy.sh", "deploy-inside": "npm run client-build && npm run server-build-inside && DEPLOY_INSIDE=true sh ./scripts/deploy.sh", "build-askbi-chrome": "vite --config vite.chrome.config.ts build", "release-askbi-chrome": "npm run build-askbi-chrome && sh ./scripts/release-askbi-chrome.sh"}, "lint-staged": {"*.{scss,less,css}": ["stylelint --fix --quiet --allow-empty-input", "prettier --write"], "*.{ts,tsx,js}": ["prettier --write", "eslint --fix --quiet"]}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@ahooksjs/use-url-state": "^3.5.1", "@ant-design/charts": "^2.1.1", "@ant-design/graphs": "^1.4.1", "@ant-design/icons": "^5.3.1", "@heroicons/react": "^2.1.1", "@prisma/client": "^5.11.0", "ahooks": "^3.8.1", "antd": "^5.20.6", "axios": "^1.7.4", "body-parser": "^1.20.2", "bytenode": "^1.5.6", "chalk": "^4.1.2", "clsx": "^2.1.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dayjs": "^1.11.12", "dotenv": "^16.4.5", "echarts": "^5.5.1", "envalid": "^8.0.0", "eventemitter3": "^5.0.1", "express": "^4.19.2", "express-rate-limit": "^7.4.0", "express-session": "^1.18.0", "file-saver": "^2.0.5", "form-data": "^4.0.0", "html2canvas": "^1.4.1", "immer": "^10.1.1", "jotai": "^2.9.3", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "module-alias": "^2.2.3", "monaco-editor": "^0.50.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.11.0", "nanoid": "^3.3.7", "node-sql-parser": "^4.18.0", "node-ssh": "^13.2.0", "nunjucks": "^3.2.4", "openai-gpt-token-counter": "^1.1.1", "query-string": "^9.1.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-markdown": "^9.1.0", "react-photo-view": "^1.2.6", "react-reflex": "^4.2.6", "react-router-dom": "^6.26.1", "react-syntax-highlighter": "^15.5.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "semver": "^7.6.3", "sharp": "^0.33.5", "sql-formatter": "^15.4.0", "ts-node": "^10.9.2", "use-immer": "^0.10.0", "vconsole": "^3.15.1", "winston": "^3.14.2", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.23.8"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-env": "^7.25.3", "@babel/preset-typescript": "^7.24.7", "@babel/runtime": "^7.25.0", "@types/chrome": "^0.0.270", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.2", "@types/dom-speech-recognition": "^0.0.4", "@types/express": "^4.17.21", "@types/express-session": "^1.18.0", "@types/file-saver": "^2.0.7", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.6", "@types/lodash": "^4.17.7", "@types/lodash-es": "^4.17.12", "@types/module-alias": "^2.0.4", "@types/multer": "^1.4.11", "@types/node": "^20.14.15", "@types/nunjucks": "^3.2.6", "@types/react": "^18.2.48", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.13", "@types/semver": "^7.5.8", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react-swc": "^3.7.0", "@vitest/coverage-v8": "^1.6.0", "autoprefixer": "^10.4.20", "babel-loader": "^9.1.3", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-compat": "^4.2.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.9", "lint-staged": "^15.2.9", "nodemon": "^3.1.4", "postcss": "^8.4.41", "postcss-nesting": "^12.1.5", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "prisma": "^5.11.0", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.77.8", "style-loader": "^3.3.4", "stylelint": "^16.8.2", "stylelint-config-standard": "^36.0.1", "stylelint-config-tailwindcss": "^0.0.7", "stylelint-order": "^6.0.4", "tailwindcss": "^3.4.10", "tsx": "^4.17.0", "typescript": "~5.5.4", "vite": "^5.4.1", "vite-plugin-compression": "^0.5.1", "vitest": "^1.4.0", "webpack": "^5.93.0", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0"}, "engines": {"node": ">=20"}, "browserslist": ["chrome 80"]}
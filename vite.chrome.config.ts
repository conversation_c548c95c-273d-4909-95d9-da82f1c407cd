import { execSync } from 'child_process'
import path, { resolve } from 'path'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'

const APP_VERSION = execSync('sh ./scripts/get-app-version.sh').toString() || 'unknown version'
// 为了安全性，vite 只会访问 VITE_ 开头的环境变量
const BRAND_NAME = process.env.VITE_BRAND_NAME || 'DIPeak'
const DISABLE_LLM_TOGGLE = process.env.VITE_DISABLE_LLM_TOGGLE === 'true'

export default defineConfig({
  // path of index.html
  root: 'src/chrome/AskBI/',
  build: {
    outDir: '../../../dist-chrome',
    rollupOptions: {
      input: {
        content: resolve(__dirname, 'src/chrome/AskBI/content.html'),
      },
      output: {
        entryFileNames: `assets/[name].js`,
        chunkFileNames: 'assets/[name].js',
        assetFileNames: 'assets/[name].[ext]',
      },
    },
    sourcemap: 'inline',
    minify: false, // TODO: enable minify
    cssMinify: false, // TODO enable css minify
  },
  define: {
    __APP_VERSION__: JSON.stringify(APP_VERSION),
    __BRAND_NAME__: JSON.stringify(BRAND_NAME),
    __DISABLE_LLM_TOGGLE__: DISABLE_LLM_TOGGLE,
  },
  resolve: {
    alias: {
      src: path.resolve(__dirname, 'src'),
      '@shared': path.resolve(__dirname, 'src/shared'),
      '@components': path.resolve(__dirname, 'src/client/components'),
      '@charts': path.resolve(__dirname, 'src/client/charts'),
      '@client/utils': path.resolve(__dirname, 'src/client/utils'),
      '@server/utils': path.resolve(__dirname, 'src/server/utils'),
    },
  },
  server: {
    port: 3000,
    proxy: {
      '^/api/.*': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
    },
  },
  optimizeDeps: {
    include: ['style-loader'], // Include the style-loader package in the optimized dependencies
  },
  plugins: [react()],
})

{
  "compilerOptions": {
    "target": "ES2022",
    "useDefineForClassFields": true,
    "forceConsistentCasingInFileNames": true,
    "noEmitOnError": false,
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "CommonJS",
    "skipLibCheck": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "baseUrl": "./",
    "paths": {
      "src": ["src"],
      "@shared/*": ["src/shared/*"],
      "@components/*": ["src/client/components/*"],
      "@charts/*": ["src/client/charts/*"],
      "@client/utils": ["src/client/utils"],
      "@server/utils": ["src/server/utils"],
      "@XEngineRouter": ["src/client/x-engine/widget/router"],
      "@XEngineRouter*": ["src/client/x-engine/widget/router*"]
    },
    // "resolveJsonModule": true,
    // "isolatedModules": true,
    // "noEmit": true,
    /* Linting */
    "strict": true /* Enable all strict type-checking options. */,
    "strictNullChecks": true /* When type checking, take into account 'null' and 'undefined'. */,
    // "noUnusedLocals": true,
    "noUnusedParameters": true,
    "esModuleInterop": true /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */,
    "noFallthroughCasesInSwitch": true,
    "outDir": "./dist-server" /* Specify an output folder for all emitted files. */
    // "noFallthroughCasesInSwitch": true
  },
  "exclude": ["node_modules", "**/*.test.ts"],
  "include": ["src/server/", "src/shared/", "src/global.d.ts"]
}

# Ask 系列产品：Ask BI、Ask Doc、MetricStore 等

AI Chat with your data instantly, create beautiful dashboard with your data.

## Features

- Use AI to chat with your data
- Connect with your data
- Create beautiful dashboard in seconds
- Chat with your data

## 项目结构

```
src
├── client  // 客户端代码
│   ├── AskBILayout.tsx  // AskBI 产品布局
│   ├── AskDocLayout.tsx // AskDoc 产品布局
│   ├── Router.tsx       // 路由组件
│   ├── components       // 所有公共组件的目录
│   ├── charts           // 所有图表的目录
│   ├── pages            // 所有页面的目录
│   ├── client.css       // 全局的客户端样式
│   ├── client.tsx       // 客户端入口
│   ├── utils.ts         // 客户端公共函数
│   └── vite-env.d.ts    // vite 环境变量
├── server // 服务端代码
│   ├── AskBI            // AskBI 产品的服务端代码
│   ├── AskDoc           // AskDoc 产品的服务端代码
│   ├── ai               // 和 AskDI、OpenAI、内部大模型通信的代码
│   ├── dao              // 数据库访问层
│   ├── express-app.ts   // Express 应用
│   ├── server.ts        // 服务端入口
│   └── utils.ts         // 服务端公共函数
├── global.d.ts  // 全局扩充的 TS 类型文件
└── shared // 客户端和服务端共享的代码
    ├── askdoc-types.ts   // AskDoc 产品的类型定义
    ├── common-types.ts   // 公共类型定义
    ├── common-utils.ts   // 公共工具函数
    ├── config.ts         // 全局配置
    ├── constants.ts      // 全局常量
    └── url-map.ts        // 路由映射
```

## 开发环境配置

### VS Code 配置

安装推荐的插件
`.vscode/extensions.json` 中推荐了 4 个插件，打开 VS Code 的插件列表会自动推荐你安装这些插件。

### 安装 Node 20

使用 Nvm 来安装 Node.js 20，并把 20 作为默认版本

```shell
brew install nvm
# 安装 v20.10.x 最新的版本
nvm install 20.10
# 把 v20 配置成默认的版本
nvm alias default 20.10
# 如果以前有 node_modules 可以先删除
rm -rf node_modules
# 安装依赖
npm install
```

### 下载代码

```shell
<NAME_EMAIL>:dipeak/ask-bi.git

cd ask-bi

npm install
```

Create `.env` from '.env.example', and update the values.

### 配置环境变量

```
cp .env.example .env
```

更新 .env 中的内容，具体内容请联系管理员。

#### ENABLED_LLM_MODELS 可用模型的白名单

使用 `ENABLED_LLM_MODELS` 可以配置要启用的模型名称，多个用逗号隔开。如果不配置，默认启用所有模型名称。

#### DEFAULT_LLM_MODEL

使用 `DEFAULT_LLM_MODEL` 可以配置默认的模型。

`.env` 中必须配置 `DEFAULT_LLM_MODEL`，如果没有配置，启动的时候会报错。

默认模型的逻辑为：

1. 如果当前用户可以使用 DEFAULT_LLM_MODEL，那么他默认的模型就是 DEFAULT_LLM_MODEL
2. 如果当前用户没有 DEFAULT_LLM_MODEL 的权限，那么他的默认模型是第 1 个可用的模型。
3. 如果当前用户没有任何模型的权限，那么他的默认模型是 null

## 开始开发

### 本地启动

```shell
# start client at port 3000
npm run client-start
# start server at port 8000
npm run server-start
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## 开发规范

地址：https://mk70znjkuv.feishu.cn/wiki/B3UMwNOJ1iFX4mkpVJRcG55inub

## 提交代码

### Lint 检查

提交 MR 前执行 `npm run lint` 检查代码格式，确保代码格式正确。

内部调用 lint-staged 的检查规则

- 只会检查 git staged 中的文件
- 对于有问题的文件会尝试自动修复
- 如果自动修复有仍有 error，则 git commit 失败；
- 如果自动修复后无 error，git commit 成功。

## 发布代码

目前有 5 套环境：

1. `**************:8000`，对应 https://askbi.dipeak.com/ ，用于外部演示
2. `**************:8000`，对应 http://**************:8000/ ，用于体验内部最新功能
3. `**************:8000`，对应 askbi3，用于开发环境，体验最新最全功能
4. `**************:8000`，对应 askbi4，用于给客户临时演示
5. `**************:8000`，对应 askbi5，用于给客户临时演示

发布脚本：

所有 `deploy-` 开头的 script 都支持指定部署环境：askbi1/askbi2/askbi3/askbi4/askbi5，不指定会使用默认的 askbi3。

- `npm run deploy-inside` 发布 askbi3，代码不加密【建议使用】
- `npm run deploy-inside askbi4` 发布 askbi4，代码不加密，askbi4 可以替换为 askbi1~5 中的任意一个
- `npm run deploy` 发布正式版本，代码加密
- `npm run deploy-inside-china-telecom` 发布中国电信 Logo 版本，代码不加密

## 服务器模式启动

git clone main 分支代码。

创建 `.env` 并配置如下内容：

```shell
ENVIRONMENT=ssy
PORT=8000

DATABASE_URL=mysql://root:123@**************:3306/askbi?schema=public
LLM_CHAT_URL_HOST=http://**************:50010
PROJECT_URL_HOST=http://***************:5500

S3_HOST=**************
S3_PORT=9000
S3_BUCKET_ID=ask-doc-dev
S3_KEY_ID=lN0C5sRi5CQ9VL1U
S3_SECRET=P1gYv83OESm7lQwzglfwmzaxDPawCwhP

ENABLED_LLM_MODELS=gpt-3.5-turbo-1106
DEFAULT_LLM_MODEL=gpt-3.5-turbo-1106
```

安装好 Node 18 并执行如下命令：

```shell
# 安装依赖
npm install -g npm@10.2.0 --registry=https://registry.npmmirror.com
npm install --registry=https://registry.npmmirror.com --canvas_binary_host_mirror=https://registry.npmmirror.com/-/binary/canvas
# 编译代码
npm run client-build
npm run server-build
# 启动服务器
npm run prod-start
# 访问服务器的 8000 端口即可
```

## .env 中 ENVIRONMENT 的说明

1. ENVIRONMENT 是环境名，比如 askbi1、askbi2、askbi3、askbi4、askbi5
2. 你可以自己起个名字，系统会使用 ENVIRONMENT 从 GlobalSettings 表中查询出当前环境的默认配置。包含默认数据源、默认表等
3. 建议最好是在 env 中配置 ENVIRONMENT，否则部分功能不可用。

## 关于默认数据源（default datasourceId）的逻辑说明

1. 默认的 `datasourceId` 获取逻辑：**先从 globalSetting 中查询 `defaultDatasourceId`。如果有就用，如果没有就使用 datasources 表的第一个**。如果 datasources 表为空，就报错无法系统系统。
1. 所有 node.js api 的返回内容 response，只要有 sql，一定有 `datasourceId`
1. 所有 node.js api 的请求内容 request，如果传了 `datasourceId` 就基于此数据源查询，如果不传就使用默认的 `datasourceId`
1. node.js 中，除 `api.ts` 之外，都必须显性的传递 `datasourceId`，不能再使用默认的 `datasourceId` 逻辑
1. 前端 axios 中，在与数据查询相关的请求参数中加上可以使用 `datasourceId`。

## 权限控制

1. 目前在 askbi1~5，对 **nginx** 登录的用户进行了权限校验，只有使用 **dipeak** 用户名登入的才是<u>管理员用户</u>，其他为普通用户。
2. 普通用户不能对 **数据源** 进行配置，需要管理员用户先对 `environment` 进行配置，普通用户才能正常使用（否则使用的是默认配置）。
3. 普通用户只能查看<u>自己创建的图表</u>，管理用户可以查看全部图表。

## 技巧

### 如何查看线上部署的代码版本信息？

打开 Chrome 控制台，执行 `__APP_VERSION__` 会返回代码最后一次提交的分支和作者的信息，如

```
> window.__APP_VERSION__
Last commit time: 2023-07-29 21:25:12 +0800, branch name: main，commit id: 7463df2, author: Shaoyin Song
```

### 快速调试 Chat 返回结果

从 Chrome DevTool 中找到 chat 接口的返回内容，copy response text，粘贴到 `src/client/components/ChatBoxMock.ts` 中，创建一个常量赋值给 `MOCK_RESPONSE`。
然后将 `ENABLE_MOCK` 设置为 `true`，即可拦截所有的请求，把返回值都设置为 Mock 的字符串。

如：

```typescript
const NEW_DATA = `{"ready":true,"rows":[{"today_date":"2023-07-05T16:00:00.000Z"}],"sql":"SELECT DATE_TRUNC('day', CURRENT_DATE) AS today_date;","sqlReason":"使用DATE_TRUNC函数将当前日期截断为天级别，得到今天的日期。","chart":"Kpi","chartTitle":"今天的日期","chartReason":"KPI 图表适用于简洁地展示关键业务指标的表现或进展。","conversationId":"QKUrxEpTwQwAAIO__Fbv-"}`

export const MOCK_RESPONSE = NEW_DATA
export const ENABLE_MOCK = true
```

> 注意：此修改如无必要，不要提交到 Git 仓库，仅用于本地调试。

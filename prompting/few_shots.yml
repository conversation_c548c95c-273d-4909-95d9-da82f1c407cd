few_shots:
  - id: 0A26335F-471C-4540-926F-36CD743E706E
    metrics:
      ysp_mer_count: 
        name: ysp_mer_count
        description: 主序恒星的数量
    dimensions:
      org_num:
        name: org_num
        values: []
        description: 星系编号、星云编号
    where_constraints: {}
    categories: [trend]
    template: |
      问题：查询每个星系在最近7个月内主序恒星数量的变化趋势
      最终的 JSON 结果如下：
      ```json
      {
        "metricNames": [
          "{{ ysp_mer_count }}"
        ],
        "notExistMetricNames": [],
        "groupBys": [
          "{{ org_num }}"
        ],
        "notExistGroupBys": [],
        "orderBys": [],
        "notExistOrderBys": []
      }
      ```
  - id: 216AFF14-8DBE-490B-9E1E-0C2075728394
    metrics:
      good_mer_count:
        name: good_mer_count
        description: 白矮星的数量
      rank_over_good_mer:
        name: good_mer_count_rank_desc
        description: 白矮星的数量降序排名
    dimensions:
      org_num:
        name: org_num
        values:
          - name: "银河星系"
            description: ""
          - name: "仙女座星系"
            description: ""
        description: "星系编号、星云编号"
    where_constraints:
      MySQL:
        final_where: "{{ primary_key_prefix }}{{ org_num }} IN ('银河星系', '仙女座星系')"
        org_num_where1: "{{ primary_key_prefix }}{{ org_num }} IN ('银河星系', '仙女座星系')"
      PostgreSQL:
        final_where: "{{ primary_key_prefix }}{{ org_num }} IN ('银河星系', '仙女座星系')"
        org_num_where1: "{{ primary_key_prefix }}{{ org_num }} IN ('银河星系', '仙女座星系')"
    categories: [simple, rank, always]
    template: |
      问题：查询银河星系、仙女座星系各星系最近6个月每个月的白矮星、中子星、超巨星的数量和排名
      最终的 JSON 结果如下：
      ```json
      {
        "metricNames": [
          "{{ good_mer_count }}",
          "{{ rank_over_good_mer }}"
        ],
        "notExistMetricNames": [
          "中子星的数量",
          "超巨星的数量",
          "中子星的排名",
          "超巨星的排名"
        ],
        "groupBys": [
        "{{ primary_key_prefix }}{{ org_num }}"
        ],
        "notExistGroupBys": [],
        "where": "{{ final_where }}",
        "orderBys": [],
        "notExistOrderBys": []
      }
      ```
  - id: CF2118F1-4341-455B-837B-5E9CE59E7CDD
    metrics:
      sdzc:
        name: sdzc
        description: "质量大小"
      sdzc_rank:
        name: sdzc_sum_rank_desc
        description: "质量大小降序排名"
    dimensions:
      org_num:
        name: org_num
        values: []
        description: "星系编号、星云编号"
    where_constraints:
      MySQL:
        rank1: "{{ sdzc_rank }} <= 8"
    categories: [simple, rank_top]
    template: |
      问题：查询每个星云中质量大小排名前8的恒星及质量大小
      最终的 JSON 结果如下：
      ```json
      {
        "metricNames": [
          "{{ sdzc_rank }}",
          "{{ sdzc }}"
        ],
        "notExistMetricNames": [],
        "groupBys": [
          "{{ org_num }}"
        ],
        "notExistGroupBys": [],
        "where": "{{ rank1 }}",
        "orderBys": [],
        "notExistOrderBys": []
      }
  #     ```
  - id: FFE96EDC-9188-458F-B656-2197216B6213
    metrics:
      good_mer_count:
        name: good_mer_count
        description: "白矮星的数量"
      good_mer_ratio:
        name: good_mer_ratio
        description: "白矮星的占比"
      huoy_mer_count:
        name: huoy_mer_count
        description: "超巨星的数量"
      huoy_mer_ratio:
        name: huoy_mer_ratio
        description: "超巨星的占比"
    dimensions:
      org_num:
        name: org_num
        values:
          - name: "银河星系"
            description: ""
          - name: "二级星系"
            description: ""
        description: "星系编号、星云编号"
    where_constraints: {}
    categories: [simple, ratio]
    template: |
      问题：查询每个星云中白矮星、超巨星的数量及其所占总恒星数量的比例
      最终的 JSON 结果如下：
      ```json
      {
        "metricNames": [
          "{{ good_mer_count }}",
          "{{ huoy_mer_count }}",
          "{{ good_mer_ratio }}",
          "{{ huoy_mer_ratio }}"
        ],
        "notExistMetricNames": [],
        "groupBys": [
          "{{ primary_key_prefix }}{{ org_num }}"
        ],
        "notExistGroupBys": [],
        "where": "",
        "orderBys": [],
        "notExistOrderBys": []
      }
      ```
  - id: A184FC28-B6FB-468B-9DA1-AE41E49332E3
    metrics:
      note2:
        name: note2_sum
        description: "闪烁次数"
      note2_rank:
        name: note2_sum_rank_asc
        description: "闪烁次数升序排名"
    dimensions:
      org_num:
        name: org_num
        description: 星系编号、星云编号
      merno:
        name: merno
        description: 恒星编号
    where_constraints:
      MySQL:
        rank1: "{{ note2_rank }} = 1"
    categories: [simple, rank_top]
    template: |
      问题：查询每个星云中闪烁次数最少的恒星及其闪烁次数
      最终的 JSON 结果如下：
      ```json
      {
        "metricNames": [
          "{{ note2 }}",
          "{{ note2_rank }}"
        ],
        "notExistMetricNames": [],
        "groupBys": [
          "{{ org_num }}",
          "{{ merno }}"
        ],
        "notExistGroupBys": [],
        "where": "{{ rank1 }}",
        "orderBys": [],
        "notExistOrderBys": []
      }
      ```
  - id: F8703EBE-9D67-4CA2-BA59-3F2BCE77D62B
    metrics:
      val_avg_6month:
        name: val_avg_6month
        description: 半年平均恒星亮度
    dimensions:
      bln_bch_nme:
        name: bln_bch_nme
        description: 星系名称
        values:
          - name: "大角座星系"
            description: ""
    where_constraints:
      MySQL:
        where1: "{{ primary_key_prefix }}{{ bln_bch_nme }} = '大角座星系'"
        final_where: "{{ primary_key_prefix }}{{ bln_bch_nme }} = '大角座星系'"
    categories: [simple]
    template: |
      问题：大角座星系2023年上半年全波段平均恒星亮度情况
      最终的 JSON 结果如下：
      ```json
      {
        "metricNames": [
          "{{ val_avg_6month }}"
        ],
        "notExistMetricNames": [],
        "groupBys": [],
        "notExistGroupBys": [],
        "where": "{{ final_where }}",
        "orderBys": [],
        "notExistOrderBys": []
      }
      ```
  - id: 6225B403-24CD-4CC1-B7FE-544E01AE4F2E
    metrics:
      cost_val_max:
        name: cost_val_max
        description: 投资占比
    dimensions:
      cost_idx:
        name: cost_idx
        description: 每种投资来源
      bln_bch_nme:
        name: bln_bch_nme
        description: 星系名称
        values:
          - name: "大角座星系"
            description: ""
    where_constraints:
      MySQL:
        where1: "{{ primary_key_prefix }}{{ bln_bch_nme }} = '大角座星系'"
        final_where: "{{ primary_key_prefix }}{{ bln_bch_nme }} = '大角座星系'"
    categories: [rank_top]
    template: |
      问题：大角座星系2023年上半年投资占比最高的项目及其占比
      最终的 JSON 结果如下：
      ```json
      {
        "metricNames": [
          "{{ cost_val_max }}",
        ],
        "notExistMetricNames": [],
        "groupBys": [
          "{{ cost_idx }}"
        ],
        "notExistGroupBys": [],
        "where": "{{ final_where }}",
        "orderBys": "[{{ cost_val_max }}]",
        "limit": 1,
        "notExistOrderBys": []
      }
      ```
  - id: 254822B3-ACB4-4649-BEDD-BD4483732ACF
    metrics:
      all_r_avg_per_user_6month:
        name: all_r_avg_per_user_6month
        description: 半年平均温度各区域明细
      all_revenue:
        name: all_revenue
        description: 各区域温度明细
      all_r_ratio:
        name: all_r_ratio
        description: 各区域温度占比
    dimensions:
      bln_bch_nme:
        name: bln_bch_nme
        description: 星系名称
        values:
          - name: "大角座星系"
            description: ""
    where_constraints:
      MySQL:
        where1: "{{ primary_key_prefix }}{{ bln_bch_nme }} = '大角座星系'"
        final_where: "{{ primary_key_prefix }}{{ bln_bch_nme }} = '大角座星系'"
    categories: [simple, ratio]
    template: |
      问题：大角座星系2023上半年户均温度各区域明细、各区域温度和各区域温度占比
      最终的 JSON 结果如下：
      ```json
      {
        "metricNames": [
          "{{ all_r_avg_per_user_6month }}",
          "{{ all_revenue }}",
          "{{ all_r_ratio }}"
        ],
        "notExistMetricNames": [],
        "groupBys": [],
        "notExistGroupBys": [],
        "where": "{{ final_where }}",
        "orderBys": [],
        "notExistOrderBys": []
      }
      ```

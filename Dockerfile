# 安装依赖阶段
FROM registry.gitlab.dipeak.com/dipeak/generic-repository/node:20-alpine as dependencies
WORKDIR /app
COPY ../package.json package-lock.json ./
COPY ../.npmrc .npmrc ./
RUN npm install -g npm@10.5.0 --registry=https://registry.npmmirror.com
RUN npm install --registry=https://registry.npmmirror.com --canvas_binary_host_mirror=https://registry.npmmirror.com/-/binary/canvas

# 构建阶段
FROM registry.gitlab.dipeak.com/dipeak/generic-repository/node:20-alpine as builder
WORKDIR /app
COPY --from=dependencies /app/node_modules ./node_modules
COPY --from=dependencies /app/package.json ./package.json
COPY . .
RUN npm run client-build
RUN npm run server-build

# 生产环境镜像
FROM registry.gitlab.dipeak.com/dipeak/generic-repository/node:20-alpine
WORKDIR /app
COPY --from=dependencies /app/package.json .
COPY --from=dependencies /app/package-lock.json .
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist-server ./dist-server
COPY --from=builder /app/dist-client ./dist-client
COPY prompting ./prompting

EXPOSE 8000

CMD ["npm", "run", "prod-start"]

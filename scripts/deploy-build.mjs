/**
 * 一键部署A100服务器 p环境
 * 如果没有SSH KEY，可以手动输入密码，或者替换SSH
 */
import dayjs from 'dayjs'
import path from 'path'
import { execSync } from 'child_process'
import { getDirname } from 'metadata-file'

const now = dayjs()
const run = (c) => {
  console.log(`\n\n[${c}]======>`)
  execSync(c, { stdio: 'inherit' })
}
const resolve = (...p) => path.resolve(getDirname(), '..', ...p)
console.log('Project Path: ', resolve())
const time = now.format('MMDD-HHmm')
const repo = `registry.cn-shanghai.aliyuncs.com/dipeak/wph-coffee:${time}`
console.log(`REPO: ${repo}`)
run(
  `docker buildx build --platform linux/amd64 -t wph-coffee ${resolve()} --output type=docker  -f ${resolve('Dockerfile')} --tag ${repo}`,
)
run(`docker push ${repo}`)

// ✅ 最后再次输出最终镜像 tag
console.log(`\n✅ 镜像已推送成功：${repo}`)

//AE3~sByGLG-.Prhwdpgb
// docker images | grep registry.gitlab.dipeak.com  | awk '{print $3}' | xargs docker rmi -f
#!/bin/bash
set -e

# 获取代码仓库的最后一次提交信息

# 检查是否是 Git 项目
if git rev-parse --is-inside-work-tree >/dev/null 2>&1; then

  # 获取最后一次提交的作者名字
  username=$(git log -1 --pretty=format:"%an")

  # 获取当前所在分支的名字
  branchName=$(git rev-parse --abbrev-ref HEAD)

  # 获取最后一次提交的 commit id
  commitId=$(git rev-parse --short HEAD)

  # 获取最后一次提交的时间
  commitTime=$(git log -1 --pretty=format:"%cd" --date=iso)
  # 输出结果
  echo "Last commit time: ${commitTime}, branch name: ${branchName}，commit id: ${commitId}, author: ${username}"
else
  echo "非 git 项目，获取不到相关信息"
fi
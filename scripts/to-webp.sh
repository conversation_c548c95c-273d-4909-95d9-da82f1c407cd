#!/bin/bash

# 把文件转换成webp的命令
# cwebp文档及下载地址
#   https://developers.google.cn/speed/webp/docs/using
# 链接到全局命令示例
#   sudo ln -s /xxx/libwebp-1.4.0-mac-arm64/bin/cwebp /usr/local/bin/cwebp

# 定义要转的文件夹
IMG_PATH=./public/img
# 定义要转的后缀
IMG_SUFFIX=("jpg" "png" "jpeg")
# 定义质量
QUALITY=80
# 定义命令行路径，如果不是全局的，需要指定路径
CWEBP_COMMAND=cwebp
# 转换后是否删除源文件，注释后关闭
NEED_DELETE_OLD_FILE=1

for suffix in "${IMG_SUFFIX[@]}"; do
    file_list=`find $IMG_PATH -type f -name "*.$suffix"`
    for old_file_path in $file_list; do
        if [ -f $old_file_path ]; then
            old_file_path=$(realpath $old_file_path)
            new_file_path="$(echo $old_file_path | cut -f1 -d ".").webp"
            echo "======================================================"
            echo "Transform : $suffix to webp"
            echo "From      : $old_file_path"
            echo "To        : $new_file_path"
            # 调用工具转译
            command="$CWEBP_COMMAND -q $QUALITY $old_file_path -o $new_file_path"
            echo "Run       : $command"
            echo "==========>"
            eval $command
            if [ -n "$NEED_DELETE_OLD_FILE" ]; then
                rm $old_file_path
                echo "Deleted $old_file_path"
            fi
        fi
    done
done
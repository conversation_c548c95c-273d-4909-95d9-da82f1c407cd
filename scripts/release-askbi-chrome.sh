#!/bin/bash
set -e

START_TIME=$(date +%s)

# 定义红色字体的 ANSI 转义序列
GREEN='\033[0;32m'
NC='\033[0m' # 重置终端颜色

VERSION="v020"
FOLDER_NAME="askbi-chrome-$VERSION"
TARGET_FILE="askbi-chrome-$VERSION.zip"

# 打包 chrome 插件的代码
rm -rf .temp_pack
mkdir .temp_pack
cp -r dist-chrome .temp_pack/"$FOLDER_NAME"
cd .temp_pack
zip -r ../"$TARGET_FILE" $FOLDER_NAME
# zip -r ../askbi-chrome-001.zip askbi-chrome
cd ..
rm -rf .temp_pack

# 提示成功
echo "${GREEN}打包成功🎉🎉"
echo "文件为当前目录下的 ${TARGET_FILE}${NC}"

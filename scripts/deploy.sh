#!/bin/bash
# set -x
set -e

START_TIME=$(date +%s)

# 定义红色字体的 ANSI 转义序列
GREEN='\033[0;32m'
RED='\033[0;31m'
GRAY='\033[0;90m'
NC='\033[0m' # 重置终端颜色

# 从命令行参数中提取出主机名
HOST="$1"

# 如果 HOST 为空，则默认为 askbi3
if [ -z "$HOST" ]; then
    HOST="askbi3"
fi

# 校验，HOST 必须是 askbi1, askbi2, askbi3, askbi4, askbi5, askbi-test, askbi-pre, askbi-prod 中的一个。否则返回错误
if [[ $HOST != "askbi1" && $HOST != "askbi2" && $HOST != "askbi3" && $HOST != "askbi4" && $HOST != "askbi5" && $HOST != "askbi-test" && $HOST != "askbi-pre" && $HOST != "askbi-prod" ]]; then
    echo "${RED}发布失败，不存在 HOST=${HOST}，必须是 askbi1, askbi2, askbi3, askbi4, askbi5 中的一个${NC}"
    exit 1
fi

# 配置 HOST 到 IP 的映射
if [[ $HOST == "askbi1" ]]; then
    SSH_HOST="**************"
elif [[ $HOST == "askbi2" ]]; then
    SSH_HOST="**************"
elif [[ $HOST == "askbi3" ]]; then
    SSH_HOST="**************"
elif [[ $HOST == "askbi4" || $HOST == "askbi-test"  || $HOST == "askbi-pre"  || $HOST == "askbi-prod" ]]; then
    SSH_HOST="**************"
elif [[ $HOST == "askbi5" ]]; then
    SSH_HOST="**************"
fi

echo "${GREEN}准备完成，开始部署 ${HOST}，IP 为 ${SSH_HOST}...${NC}"

SSH_PORT="22"

GIT_EMAIL=$(git config user.email)
# 维护一个映射表，根据 git email 设置远程服务器的 用户名
if [[ $GIT_EMAIL == "<EMAIL>" || $GIT_EMAIL == "<EMAIL>" ]]; then
    SSH_USER="ssy"
elif [[ $GIT_EMAIL == "<EMAIL>" ]]; then
    SSH_USER="kongsa1"
elif [[ $GIT_EMAIL == "<EMAIL>" ]]; then
    SSH_USER="guohao"
elif [[ $GIT_EMAIL == "<EMAIL>" ]]; then
    SSH_USER="lixudong"
elif [[ $GIT_EMAIL == "<EMAIL>" ]]; then
    SSH_USER="lijiacheng"
elif [[ $GIT_EMAIL == "<EMAIL>" ]]; then
    SSH_USER="xiejiaxin"
elif [[ $GIT_EMAIL == "<EMAIL>" ]]; then
    SSH_USER="liyigang"
else
    SSH_USER="unknown"
    echo "${RED}找不到你的 user 名，你可以自行在 scripts/deploy.sh 代码中添加。或联系 @宋邵茵 添加"
    exit 1
fi

echo "${GRAY}获取到服务器用户名为 $SSH_USER${NC}"

# 尝试 SSH 登录并检查退出状态
if ssh -p "$SSH_PORT" -o BatchMode=yes "$SSH_USER"@"$SSH_HOST" exit; then
  echo "${GREEN}登录服务器成功${NC}"
else
  echo "${RED}SSH 登录服务器失败，请在 $SSH_HOST 服务器上配置 SSH 密钥登录${NC}，有问题请联系 @宋邵茵"
  exit 1
fi

# 配置文件目录
DEPLOY_DIR="~$SSH_USER/ask-bi"
# 配置 HOST 到不同目录的映射
if [[ $HOST == "askbi-prod" ]]; then
    DEPLOY_DIR="~$SSH_USER/ask-bi-prod"
elif [[ $HOST == "askbi-pre" ]]; then
    DEPLOY_DIR="~$SSH_USER/ask-bi-pre"
elif [[ $HOST == "askbi-test" ]]; then
    DEPLOY_DIR="~$SSH_USER/ask-bi-test"
fi

# 配置image名字
IMAGE_NAME="ask-bi"
# 配置 HOST 到不同目录的映射
if [[ $HOST == "askbi-prod" ]]; then
    IMAGE_NAME="ask-bi-prod"
elif [[ $HOST == "askbi-pre" ]]; then
    IMAGE_NAME="ask-bi-pre"
elif [[ $HOST == "askbi-test" ]]; then
    IMAGE_NAME="ask-bi-test"
fi

echo "${GRAY}开始上传文件到服务器..."
if [ -n "$DEPLOY_INSIDE" ]; then
    # scp -P "$SSH_PORT" Dockerfile.inside "$SSH_USER"@"$SSH_HOST":"$DEPLOY_DIR/Dockerfile"
    rsync -avz -e "ssh -p $SSH_PORT" Dockerfile.inside "$SSH_USER"@"$SSH_HOST":"$DEPLOY_DIR"/Dockerfile
else
    # scp -P "$SSH_PORT" -r src/ webpack.config.mjs Dockerfile "$SSH_USER"@"$SSH_HOST":"$DEPLOY_DIR"
    rsync -avz -e "ssh -p $SSH_PORT" --progress src webpack.config.mjs Dockerfile "$SSH_USER"@"$SSH_HOST":"$DEPLOY_DIR"
fi
# scp -P "$SSH_PORT" -r dist-client/ dist-server/ prisma/ package.json package-lock.json "$SSH_USER"@"$SSH_HOST":"$DEPLOY_DIR"
rsync -avz -e "ssh -p $SSH_PORT" --progress dist-client dist-server prompting package.json package-lock.json static "$SSH_USER"@"$SSH_HOST":"$DEPLOY_DIR"
echo "${GREEN}成功上传文件${NC}"

echo "${GRAY}开始构建 Docker 镜像..."
# ssh -p "$SSH_PORT" "$SSH_USER"@"$SSH_HOST" "docker image rm ask-bi"
ssh -p "$SSH_PORT" "$SSH_USER"@"$SSH_HOST" "docker build -t ${IMAGE_NAME} ${DEPLOY_DIR}"
echo "${GREEN}成功构建 Docker 镜像${NC}"

# 配置在docker中暴露出的端口
WEB_PORT="8000"
if [[ $HOST == "askbi-prod" ]]; then
    WEB_PORT="30100"
elif [[ $HOST == "askbi-pre" ]]; then
    WEB_PORT="8002"
elif [[ $HOST == "askbi-test" ]]; then
    WEB_PORT="8003"
fi

# 配置container名字
CONTAINER_NAME="ask-bi-0707"
if [[ $HOST == "askbi-prod" ]]; then
    CONTAINER_NAME="ask-bi-0707-prod"
elif [[ $HOST == "askbi-pre" ]]; then
    CONTAINER_NAME="ask-bi-0707-pre"
elif [[ $HOST == "askbi-test" ]]; then
    CONTAINER_NAME="ask-bi-0707-test"
fi

ENV_FILE="ask-bi-env"
if [[ $HOST == "askbi-prod" ]]; then
    ENV_FILE="ask-bi-env-prod"
elif [[ $HOST == "askbi-pre" ]]; then
    ENV_FILE="ask-bi-env-pre"
elif [[ $HOST == "askbi-test" ]]; then
    ENV_FILE="ask-bi-env-test"
fi

LOG_DIR="frontend"
if [[ $HOST == "askbi-prod" ]]; then
    LOG_DIR="frontend-prod"
elif [[ $HOST == "askbi-pre" ]]; then
    LOG_DIR="frontend-pre"
elif [[ $HOST == "askbi-test" ]]; then
    LOG_DIR="frontend-test"
fi

# prod环境不需要执行run container
if [[ $HOST == "askbi-prod" ]]; then
    REGISTRY="registry.gitlab.dipeak.com/dipeak/generic-repository"
    TAG="$(date +%Y%m%d-%H%M)-release"
    TARGET_IMAGE="${REGISTRY}/${IMAGE_NAME}:${TAG}"
    # 打标签并推送到镜像仓库
    ssh -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" "\
        docker tag $IMAGE_NAME $TARGET_IMAGE && \
        docker push $TARGET_IMAGE \
    "
    echo "${GREEN}镜像已推送. IMAGE: $TARGET_IMAGE ${NC}"
else
echo "${GRAY}重启 Docker 镜像...${NC}"

# 在 $(...) 前添加了反斜杠 \，这是为了在本地不执行命令替换
ssh -T -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" >/dev/null << EOF
  ENV_FILE_PATH=\$(realpath "\$HOME/../ssy/${ENV_FILE}")
  ELK_LOG_PATH=\$(realpath "\$HOME/../askbot/askbot-logs/${LOG_DIR}")

  docker rm -f $CONTAINER_NAME || exit 0
  docker run -d --env-file="\$ENV_FILE_PATH" -v "\$ELK_LOG_PATH":/app/logs -p ${WEB_PORT}:8000 --restart=always --name $CONTAINER_NAME $IMAGE_NAME
EOF

echo "${GREEN}重启 Docker 镜像完成${NC}"
fi

# 清空 web server 的缓存
# ssh -p "$SSH_PORT" "$SSH_USER"@"$SSH_HOST" "docker exec $DOCKER_ID pm2 update"

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo "部署耗时：$DURATION 秒，Have a nice day! "
echo "${GREEN}${HOST} 环境发布完成🎉🎉${NC} 打开 http://${SSH_HOST}:${WEB_PORT} 试试吧"

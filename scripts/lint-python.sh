set -x

PROJECT_ROOT=$(dirname "$(realpath "$0")")/..
SUBDIRS=("nl2metric")
for SUBDIR_NAME in "${SUBDIRS[@]}"; do
    SUBDIR_PATH="$PROJECT_ROOT/python/$SUBDIR_NAME"
    
    # 检查子目录是否存在
    if [ -d "$SUBDIR_PATH" ]; then
        pushd "$SUBDIR_PATH" 
    	PYTHONPATH=${PYTHONPATH}:$(pwd) flake8 .
    	PYTHONPATH=${PYTHONPATH}:$(pwd) mypy .
        popd
    else
        echo "指定的子目录不存在: $SUBDIR_PATH"
    fi
done
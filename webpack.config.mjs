import fs from 'fs'
import path from 'path'
import bytenode from 'bytenode'
// eslint-disable-next-line import/no-extraneous-dependencies
import nodeExternals from 'webpack-node-externals'

class BytenodePlugin {
  apply(compiler) {
    compiler.hooks.afterEmit.tap('BytenodePlugin', (compilation) => {
      const outputDir = compilation.options.output.path
      const jsFile = path.join(outputDir, 'bundle.min.js')
      const jscFile = path.join(outputDir, 'server', 'bundle.min.jsc')
      // 确保 server 目录存在，注意，加一层目录结构是为了适配 express-app.ts中49、53行
      const serverDir = path.join(outputDir, 'server')
      if (!fs.existsSync(serverDir)) {
        fs.mkdirSync(serverDir, { recursive: true })
      }
      bytenode.compileFile(jsFile, jscFile)

      fs.unlinkSync(jsFile)
    })
  }
}

export default {
  entry: './src/server/server.ts',
  target: 'node',
  mode: 'production',
  externals: [nodeExternals()],
  output: {
    filename: 'bundle.min.js',
    path: path.resolve(new URL('./dist-server', import.meta.url).pathname),
    clean: true,
  },
  module: {
    rules: [
      {
        test: /\.ts$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-typescript'],
            plugins: ['@babel/plugin-transform-runtime'],
          },
        },
      },
    ],
  },
  resolve: {
    extensions: ['.ts', '.js'],
    alias: {
      src: path.resolve(new URL('./src', import.meta.url).pathname),
      '@shared': path.resolve(new URL('./src/shared', import.meta.url).pathname),
      '@components': path.resolve(new URL('./src/client/components', import.meta.url).pathname),
      '@charts': path.resolve(new URL('./src/client/charts', import.meta.url).pathname),
      '@client/utils': path.resolve(new URL('./src/client/utils', import.meta.url).pathname),
      '@server/utils': path.resolve(new URL('./src/server/utils', import.meta.url).pathname),
    },
  },
  plugins: [new BytenodePlugin()],
}

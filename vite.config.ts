/// <reference types="vitest" />
import { execSync } from 'child_process'
import path, { join } from 'path'
import { readFileSync, writeFileSync } from 'fs'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import { visualizer } from 'rollup-plugin-visualizer'
import * as dotenv from 'dotenv'
import viteCompression from 'vite-plugin-compression'

dotenv.config()

const APP_VERSION = execSync('sh ./scripts/get-app-version.sh').toString() || 'unknown version'

// 为了安全性，vite 只会访问 VITE_ 开头的环境变量
const BRAND_NAME = process.env.VITE_BRAND_NAME || 'DIPeak'
const DISABLE_LLM_TOGGLE = process.env.VITE_DISABLE_LLM_TOGGLE === 'true'
const ENABLE_APP_SWITCH = process.env.VITE_ENABLE_APP_SWITCH === 'true'
const TIANHONG_LOGIN = process.env.VITE_TIANHONG_LOGIN === 'true'
const ASKDOC_SHOW_UPLOAD = process.env.VITE_ASKDOC_SHOW_UPLOAD === 'true'
const VITE_VISUALIZER = process.env.VITE_VISUALIZER === 'true'
const BASE_URL = process.env.BASE_URL
// env中的默认配置，从package.json中获取版本号
const ASKBI_PACKAGE_VERSION = JSON.stringify(process.env.npm_package_version)

// https://vitejs.dev/config/
export default defineConfig({
  base: BASE_URL ?? '/',
  plugins: [
    react(),
    {
      name: 'brand-logo-plugin',
      apply: 'build',
      enforce: 'pre',
      writeBundle() {
        const indexFilePath = join(__dirname, 'dist-client', 'index.html')
        let content = readFileSync(indexFilePath, 'utf8')
        content = content.replace('<title>TITLE</title>', '<title>AskBot 智能问答</title>')

        // 将修改后的内容写回文件
        writeFileSync(indexFilePath, content)
      },
    },
    VITE_VISUALIZER &&
      visualizer({
        emitFile: true,
        filename: 'visualizer.html',
      }),
    // br压缩算法，比gzip优秀
    viteCompression({ algorithm: 'brotliCompress' }),
    // gzip压缩算法，作为br不支持时的兜底
    viteCompression({}),
  ],
  optimizeDeps: {
    exclude: ['node_modules'],
  },
  build: {
    outDir: 'dist-client',
  },
  define: {
    __APP_VERSION__: JSON.stringify(APP_VERSION),
    __BRAND_NAME__: JSON.stringify(BRAND_NAME),
    __DISABLE_LLM_TOGGLE__: DISABLE_LLM_TOGGLE,
    __ENABLE_APP_SWITCH: ENABLE_APP_SWITCH,
    ASKBI_VERSION: ASKBI_PACKAGE_VERSION,
    TIANHONG_LOGIN,
    ASKDOC_SHOW_UPLOAD,
    __BASE_URL__: JSON.stringify(BASE_URL ?? ''),
  },
  resolve: {
    alias: {
      src: path.resolve(__dirname, 'src'),
      '@shared': path.resolve(__dirname, 'src/shared'),
      '@components': path.resolve(__dirname, 'src/client/components'),
      '@charts': path.resolve(__dirname, 'src/client/charts'),
      '@client/utils': path.resolve(__dirname, 'src/client/utils'),
      '@server/utils': path.resolve(__dirname, 'src/server/utils'),
      '@api': path.resolve(__dirname, 'src/client/x-engine/api'),
      '@libs': path.resolve(__dirname, 'src/client/x-engine/common/libs'),
      '@XEngineRouter': path.resolve(__dirname, 'src/client/x-engine/widget/router'),
      '@ui': path.resolve(__dirname, 'src/client/x-engine/widget/ui'),
      '@constant': path.resolve(__dirname, 'src/client/x-engine/widget/constant'),
      '@model': path.resolve(__dirname, 'src/client/x-engine/widget/model'),
      '@pages': path.resolve(__dirname, 'src/client/x-engine/pages'),
      '@images': path.resolve(__dirname, 'src/client/x-engine/common/images'),
      '@text': path.resolve(__dirname, 'src/client/x-engine/common/text'),
      '@atoms': path.resolve(__dirname, 'src/client/x-engine/atoms'),
    },
  },
  server: {
    port: 3000,
    strictPort: true,
    proxy: {
      [`^${BASE_URL ?? ''}/api/.*`]: {
        target: 'http://localhost:8000',
        // target: 'https://askbi-pre.dipeak.com',
        changeOrigin: true,
        xfwd: true, // 保留原始请求的头部信息
      },
    },
  },
  test: {
    testTimeout: 10000,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
    },
  },
})

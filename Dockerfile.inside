# Tips: npm run client-build, npm run server-build-inside, 确认架构 选择基础镜像 本地打包
# docker buildx build --platform linux/amd64 -f Dockerfile.inside -t askbi-name:tag .
# FROM --platform=linux/amd64 node:20-alpine 
FROM registry.gitlab.dipeak.com/dipeak/generic-repository/node:20-alpine
WORKDIR /app
COPY package.json package-lock.json ./
COPY dist-client ./dist-client
COPY dist-server ./dist-server
COPY prompting ./prompting

# 设置代理
ENV http_proxy="http://**************:7890"
ENV https_proxy="http://**************:7890"

RUN npm install -g npm@10.8.2 --registry=https://registry.npmmirror.com
RUN npm install --registry=https://registry.npmmirror.com --canvas_binary_host_mirror=https://registry.npmmirror.com/-/binary/canvas

# 清空代理
ENV http_proxy=""
ENV https_proxy=""

EXPOSE 8000

CMD ["npm", "run", "prod-start-inside"]

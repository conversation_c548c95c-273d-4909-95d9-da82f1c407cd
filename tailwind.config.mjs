/** @type {import('tailwindcss').Config} */
// import * as colors from 'tailwindcss/colors'

function withOpacity(variableName) {
  return ({ opacityValue }) => {
    if (opacityValue !== undefined) {
      return `rgba(var(${variableName}), ${opacityValue})`
    }
    return `rgb(var(${variableName}))`
  }
}

export default {
  content: ['./src/client/**/*.{html,js,jsx,tsx,ts}', './index.html'],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: withOpacity('--color-primary'),
        primaryHoverColor: withOpacity('--color-primary-hover'),
        link: withOpacity('--color-link'),
        borderColor: withOpacity('--color-border-color'),
        metricUnselectedBorderColor: withOpacity('--color-metric-unselected-border-color'),
        metricSelectedBorderColor: withOpacity('--color-metric-selected-border-color'),
        metricUnselectedBgColor: withOpacity('--color-metric-unselected-bg-color'),
        metricSelectedBgColor: withOpacity('--color-metric-selected-bg-color'),
        metricColor: withOpacity('--color-metric-selected-bg-color'),
        dimensionColor: withOpacity('--color-dimension-selected-bg-color'),
        dimensionLightColor: withOpacity('--color-dimension-light-color'),
        dimensionUnselectedBorderColor: withOpacity('--color-dimension-unselected-border-color'),
        dimensionSelectedBorderColor: withOpacity('--color-dimension-selected-border-color'),
        dimensionUnselectedBgColor: withOpacity('--color-dimension-unselected-bg-color'),
        dimensionSelectedBgColor: withOpacity('--color-dimension-selected-bg-color'),
      },
      rotate: {
        12: '12deg',
        24: '24deg',
        36: '36deg',
        48: '48deg',
        60: '60deg',
        72: '72deg',
        84: '84deg',
        96: '96deg',
        108: '108deg',
        120: '120deg',
      },
      screens: {
        xs: '375', // 小于 640px 的屏幕尺寸
      },
    },
  },
  plugins: [],
}

build_node:
	DOCKER_BUILDKIT=1 docker build . -t ask-bi-node:latest

build_node_amd64_in_mac:
	DOCKER_BUILDKIT=1 docker buildx build . --platform linux/amd64 -t ask-bi-node:latest

build_python:
	rm -rf python/resources
	python python/copy-resources.py
	bash python/generate_git_version.sh
	DOCKER_BUILDKIT=1 docker build python -f deploy/python.Dockerfile -t ask-bi-python:latest

build_python_stage2:
	DOCKER_BUILDKIT=1 docker build python -f deploy/python-stage2.Dockerfile -t ask-bi-python:latest

build_jiaohang_tool:
	DOCKER_BUILDKIT=1 docker build python -f deploy/jiaohang-tool.Dockerfile -t ask-bi-jiaohang-tool:latest

build_vllm:
	DOCKER_BUILDKIT=1 docker build . -f deploy/vllm.Dockerfile -t ask-bi-vllm:latest

run_mixtral_origin:
	docker run -d --restart=always --name ask-bi-vllm-origin --rm --gpus=all -e CUDA_VISIBLE_DEVICES="4,5" --shm-size=10gb --ipc=host   -v /data2/public_file2/LLM_model/Mixtral-8x7B-Instruct-v0.1/awq:/model/mixtral-8-7b -p 30011:20010 ask-bi-vllm:latest  python -m vllm.entrypoints.openai.api_server --max-model-len=16384  --host 0.0.0.0 --model /model/mixtral-8-7b --tensor-parallel-size 2 --load-format safetensors --port 20010 --gpu-memory-utilization 0.7


run_mixtral:
	docker run -d --restart=always --name ask-bi-vllm  --gpus=all -e CUDA_VISIBLE_DEVICES="0,1" --shm-size=10gb --ipc=host   -v /data2/public_file2/LLM_model/merged_jiaohang_mixtral_7b8_750_0223_awq:/model/mixtral-8-7b -p 30010:20010 ask-bi-vllm:latest  python -m vllm.entrypoints.openai.api_server  --max-model-len=16384   --host 0.0.0.0 --model /model/mixtral-8-7b --tensor-parallel-size 2 --load-format safetensors --port 20010 --gpu-memory-utilization 0.7

run_docker_node:
	docker rm -fv ask-bi-node
	docker run --env-file=$(shell pwd)/.env --restart=always --name ask-bi-node -d -p 8000:8000 ask-bi-node:latest 

run_docker_python:
	docker rm -fv ask-bi-python
	docker run --env-file=$(shell pwd)/.env --restart=always --dns ********* --name ask-bi-python -d -p 9099:9099 -v /home/<USER>/docker/python/logs:/ask-bi/python/nl2metric/logs -w /ask-bi/python/nl2metric ask-bi-python:latest  python main.py

run_docker_python_gpu:
	docker rm -fv ask-bi-python
	docker run --env-file=$(shell pwd)/.env --restart=always --dns ********* --name ask-bi-python --gpus '"device=0"' -e rank_model_device=cuda -d -p 9099:9099 -v /home/<USER>/docker/python/logs:/ask-bi/python/nl2metric/logs -w /ask-bi/python/nl2metric ask-bi-python:latest  python main.py

run_docker_python_local:
	docker rm -fv ask-bi-python
	docker run --env-file=$(shell pwd)/.env --restart=always --dns ********* --name ask-bi-python -d -p 9099:9099 -v $(shell pwd):/ask-bi -w /ask-bi/python/nl2metric ask-bi-python:latest  uvicorn fastapi_server:app --host 0.0.0.0 --port 9099 --workers 1

run_docker_python_debug:
	docker rm -fv ask-bi-python-debug
	docker run --env-file=$(shell pwd)/.env --restart=always --dns ********* --name ask-bi-python-debug -d -p 10099:9099    -w /ask-bi/python/nl2metric ask-bi-python:latest  python main.py

run_docker_document_builder:
	docker rm -fv ask-bi-document-builder
	docker run --env-file=$(shell pwd)/.env --restart=always --dns ********* --name ask-bi-document-builder -d  -w /ask-bi/python/nl2metric ask-bi-python:latest  python builder_server.py

run_docker_report_generate:
	docker rm -fv ask-bi-report-generate
	docker run --env-file=$(shell pwd)/.env --restart=always --dns ********* --name ask-bi-report-generate -d -p 30099:9099  -w /ask-bi/python/nl2metric ask-doc-report-generate:latest  python main.py
    
run_python:
	cd python/nl2metric && CUDA_VISIBLE_DEVICES='' python main.py --listen 0.0.0.0:9099

run_jiaohang_node:
	docker rm -fv ask-bi-node-jiaohang
	docker run -it -d --name ask-bi-node-jiaohang -p 8000:8000 --network jiaohang_test --env-file=.env --entrypoint sh ask-bi-node

run_jiaohang_python:
	docker rm -fv ask-bi-python-jiaohang
	docker run -it -d --name ask-bi-python-jiaohang -p 9099:9099 --env-file=.env --network jiaohang_test --entrypoint sh ask-bi-python

test_nl_tasks:
	@cd python/nl2metric/scripts && bash test-nl-tasks.sh $(filter-out $@,$(MAKECMDGOALS))

lint:
	sh ./scripts/lint-python.sh

insert_few_shot:
	cd python/nl2metric && PYTHONPATH=${PYTHONPATH}:$(shell pwd) python scripts/insert_few_shot.py

flake:
	cd python/nl2metric && PYTHONPATH=${PYTHONPATH}:$(shell pwd) flake8 .

ff: format flake

format:
	cd python/nl2metric && black .
	cd python/nl2metric && isort .
	cd python/nl2metric && black .

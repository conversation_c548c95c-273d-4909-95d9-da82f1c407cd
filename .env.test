PORT=8000
DATABASE_URL=test
LLM_CHAT_URL_HOST=http://test.com
PROJECT_URL_HOST=http://test.com

DEFAULT_LLM_MODEL=yi_1.5_34b
ASK_BI_HOST=http://test.com
PARAMS_EXTRACT_URL_HOST=http://test.com
mixtral_vllm_address=http://test.com

# 默认di代理地址
XENGINE_PROXY_URL=http://test.com
# di接口地址
DI_PROXY_URI=http://test.com
# di k8s ditest接口,用于首页代理登录
DI_DITEST_URI=http://test.com
# 嵌套的xengine 地址
VITE_XENGINE_ORIGIN=https://staging.dipeak.com
# doc接口地址
ASKDOC_API_FILE_URL= http://test.com
# doc文件地址
ASKDOC_FILE_PREVIEW_URL=http://test.com

# ranger接口地址
RANGER_HOST=http://test.com
AUTH_LOGIN_HOST=http://test.com
RANGER_LOGIN_ENABLE=true

DATABASE_URL=test

DISABLE_SEMANTIC_MODEL_BUILD=true

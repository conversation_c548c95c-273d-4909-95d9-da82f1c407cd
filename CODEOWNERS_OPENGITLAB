# Note, if one file matches multiple rules, the last one takes effect.

# DEFAULT
# Default will only take effect when no other rule matches.
# So make sure this is the first rule in the file.
* 1 @tongsuo.ts @ssy @zhangwenyu

# type script
/src/ 1 @ssy
/public/ 1 @ssy @zhangwenyu

# python
/python/ 1 @tongsuo.ts @zhangwenyu

# other public
/deploy/ 1 @tongsuo.ts @ssy

# OWNER_OF_OWNER
/CODEOWNERS_OPENGITLAB 1 @cche

branch-release 1 @tongsuo.ts @ssy @zhangwenyu

# Why not use group owners?
# Group owners will make approver list organized in many different places,
# not as obvious as a single file. Will try when we have more staffs.

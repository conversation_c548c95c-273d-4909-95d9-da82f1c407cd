version: '3.3'

services:
  db:
    image: mysql:8.0-bullseye
    volumes:
      - ./mysql_data:/var/lib/mysql
      - ./initdb:/docker-entrypoint-initdb.d
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: "123"
      MYSQL_DATABASE: askbi
      MYSQL_PASSWORD: "123"
    ports:
     - "3306:3306"
     - "9099:9099" # nl2metric
     - "8000:8000" # nl2metric

  nl2metric:
    image: ask-bi-python:latest 
    command:
    - python
    - main.py
    restart: always
    working_dir: /ask-bi/python/nl2metric 
    network_mode: "service:db"

  nodejs:
    image: ask-bi-node:latest
    volumes:
     - /data2/lixudong/logs/ask-bi:/app/logs 
    restart: always
    network_mode: "service:db"
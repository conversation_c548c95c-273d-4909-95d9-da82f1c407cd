module.exports = {
  root: true,
  env: { browser: true, es2020: true, webextensions: true, node: true },
  settings: {
    react: {
      version: 'detect',
    },
    'import/resolver': {
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx', '.eslintrc'],
      },
      typescript: {
        project: './tsconfig.json',
      },
    },
    polyfills: [
      'Promise',
      'fetch',
      'IntersectionObserver',
      'ResizeObserver',
      'Set',
      'WeakSet',
      'Map',
      'WeakMap',
      'URLSearchParams',
      'URL',
    ],
  },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:compat/recommended',
    'prettier',
    'plugin:prettier/recommended',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: { ecmaVersion: 'latest', sourceType: 'module' },
  plugins: ['@typescript-eslint', 'react-refresh', 'react-hooks', 'import', 'compat', 'prettier'],
  globals: {
    module: true,
    exports: true,
    require: true,
    window: true,
    process: true,
    location: true,
    __locals: true,
    __dirname: true,
    ENV: true,
  },
  rules: {
    eqeqeq: ['error', 'always', { null: 'ignore' }],
    'prefer-const': [
      'error',
      {
        destructuring: 'all',
        ignoreReadBeforeAssign: false,
      },
    ],
    'new-cap': ['error', { capIsNewExceptions: ['express.Router'] }],
    'max-len': [
      1,
      {
        code: 120,
        ignoreStrings: true,
        ignoreUrls: true,
        ignoreComments: true,
        ignoreTemplateLiterals: true,
        ignoreRegExpLiterals: true,
      },
    ],
    'consistent-return': 'off',
    'default-case': 2,
    'prefer-rest-params': 2,
    'eol-last': 2,
    'import/no-duplicates': 2,
    'import/no-dynamic-require': 2,
    'import/order': [
      'error',
      {
        pathGroups: [
          {
            pattern: '@*/**',
            group: 'external',
            position: 'after',
          },
          {
            pattern: 'src/**',
            group: 'external',
            position: 'after',
          },
        ],
      },
    ],
    'import/no-extraneous-dependencies': 2,
    'no-extra-semi': 'error',
    'no-console': [
      1,
      {
        allow: ['info', 'error', 'warn'],
      },
    ],
    'no-underscore-dangle': [
      'error',
      {
        allow: [
          '__APP_VERSION__',
          '__BRAND_NAME__',
          '__BASE_URL__',
          '__DISABLE_LLM_TOGGLE__',
          '__ENABLE_APP_SWITCH',
          '__DISABLE_TIANHONG_LOGIN',
          'ASKDOC_SHOW_UPLOAD',
        ],
      },
    ],
    'no-useless-rename': 2,
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'error',
    '@typescript-eslint/no-unused-vars': [
      1,
      {
        args: 'after-used',
        ignoreRestSiblings: true,
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
        caughtErrors: 'none',
      },
    ],
    'react/self-closing-comp': 2,
    'react/jsx-uses-vars': 2,
    'react/no-multi-comp': [
      2,
      {
        ignoreStateless: true,
      },
    ],
    'react/sort-comp': 2,
    'react/jsx-tag-spacing': 2,
    'react/jsx-closing-bracket-location': 2,
    '@typescript-eslint/no-var-requires': 2,
    '@typescript-eslint/no-use-before-define': [
      'error',
      {
        functions: false,
        variables: false,
        classes: false,
      },
    ],
    '@typescript-eslint/ban-types': [
      'error',
      {
        types: {
          Function: false,
          object: false,
          '{}': false,
        },
      },
    ],
    '@typescript-eslint/naming-convention': [
      'error',
      {
        selector: 'default',
        format: ['camelCase', 'PascalCase'],
      },
      {
        selector: ['variableLike', 'property'],
        format: ['camelCase', 'UPPER_CASE', 'PascalCase'],
        leadingUnderscore: 'allowSingleOrDouble',
        trailingUnderscore: 'allowSingleOrDouble',
      },
      {
        selector: ['method'],
        format: ['camelCase'],
        leadingUnderscore: 'allow',
      },
      {
        selector: 'typeLike',
        format: ['PascalCase', 'UPPER_CASE'],
      },
      {
        selector: ['enum'],
        format: ['PascalCase', 'UPPER_CASE'],
      },
      {
        selector: ['objectLiteralProperty', 'enumMember'],
        format: null,
      },
    ],
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/no-extra-semi': 'error',
    '@typescript-eslint/no-non-null-assertion': 'off',
  },
}

{
  "words": [
    "achat",
    "acomplete",
    "addoption",
    "ahooks",
    "ahooksjs",
    "alicdn",
    "allm",
    "antd",
    "antv",
    "Appstore",
    "askbi",
    "askbot",
    "ASKDI",
    "askdoc",
    "astify",
    "astream",
    "astype",
    "Automations",
    "autouse",
    "axios",
    "BAAI",
    "baichuan",
    "baowu",
    "bbwsy",
    "bbwsytest",
    "bytenode",
    "Cascader",
    "cghttz",
    "chatbi",
    "chatdoc",
    "chatglm",
    "classname",
    "clsx",
    "cogview",
    "compat",
    "concat",
    "conver",
    "convers",
    "curdate",
    "dagre",
    "dataframe",
    "DATAMODEL",
    "datasource",
    "datasources",
    "DATAZOOM",
    "datetime",
    "dateutil",
    "decomp",
    "dianxin",
    "dipeak",
    "distore",
    "ditest",
    "dotenv",
    "dropna",
    "Dtype",
    "dyft",
    "echarts",
    "envalid",
    "excinfo",
    "executemany",
    "fanwei",
    "fieldname",
    "fkey",
    "FOUC",
    "fullstr",
    "gonghang",
    "hanlp",
    "hengsheng",
    "heroicons",
    "hljs",
    "huggingface",
    "HUOY",
    "icbc",
    "iconfont",
    "immer",
    "Instrumentor",
    "invoicedate",
    "invoiceno",
    "iterrows",
    "JDBC",
    "JIAO",
    "jiaohang",
    "JINGFEN",
    "JSESSIONID",
    "jsonlogger",
    "keyof",
    "keypoint",
    "Langchain",
    "levelname",
    "levelno",
    "llm",
    "llmbase",
    "llms",
    "lmdi",
    "LTWH",
    "LTWHP",
    "makereport",
    "mediumint",
    "memberof",
    "mercht",
    "MERNO",
    "metastore",
    "metricmodel",
    "minio",
    "mistralai",
    "mixtral",
    "modifyitems",
    "msword",
    "mytest",
    "nocheck",
    "nodeid",
    "nodesep",
    "nofilter",
    "npmignore",
    "Nums",
    "odbc",
    "officedocument",
    "OLAP",
    "openai",
    "opentelemetry",
    "openxmlformats",
    "pagesinit",
    "pannable",
    "Parens",
    "pfundabbr",
    "Popconfirm",
    "postprocessor",
    "prefiltering",
    "preprompt",
    "ptable",
    "pydantic",
    "pyenv",
    "pyhive",
    "pymysql",
    "pymysqlpool",
    "pytest",
    "pythonjsonlogger",
    "qwen",
    "rankdir",
    "ranksep",
    "relativedelta",
    "rerank",
    "reranker",
    "resave",
    "RRGGBBAA",
    "rwatermark",
    "SAST",
    "sdfh",
    "semconv",
    "sessionfinish",
    "sessionstart",
    "setdt",
    "SFBK",
    "shaoyin",
    "shibing",
    "Sider",
    "SMALLINT",
    "smartx",
    "Snapline",
    "songshaoyin",
    "sparkline",
    "spreadsheetml",
    "sqlalchemy",
    "sqlify",
    "SQLJSON",
    "starfire",
    "strify",
    "stylelint",
    "subquery",
    "tailwindcss",
    "textlayerrendered",
    "thfund",
    "thsso",
    "TIAN",
    "tianhong",
    "tinyint",
    "tqdm",
    "traceid",
    "Treemap",
    "treenode",
    "typeof",
    "UNION_UPSERT",
    "unitprice",
    "unthrowable",
    "Usergroup",
    "userid",
    "varchar",
    "vconsole",
    "VLLM",
    "vtable",
    "wanx",
    "webextensions",
    "wordprocessingml",
    "XENGINE",
    "xflow",
    "xfwd",
    "xusers",
    "XYKXF",
    "yinlian",
    "ywjhz",
    "YYMM",
    "zhipu",
    "ZHONG",
    "zhonghua",
    "zhongou",
    "zhongyuan",
    "zhuoxue",
    "zoomable",
    "zrender",
    "zyhl",
    "zyhltest"
  ],
  "ignorePaths": [
    "src/client/components/ChatBoxMock.ts",
    // "python/",
    "src/server/MetricStore/data/"
  ]
}

/**
 * @description 所有产品的 URL 地址的映射表
 */

import { concatBaseUrl } from './common-utils'

/**
 * Ask BI 相关
 */
export const ASK_BI_BASE = ''
/** Ask BI 页面地址汇总 */
export const askBIPageUrlsWithoutBaseUrl = {
  home: `${ASK_BI_BASE}/home`,
  login: `${ASK_BI_BASE}/login`,
  loginError: `${ASK_BI_BASE}/login-error`,
  chatNew: `${ASK_BI_BASE}/chat/new`,
} as const

export const aiPlatformPageUrls = concatBaseUrl(askBIPageUrlsWithoutBaseUrl)

/** Ask BI API 地址汇总 */
export const askBIApiUrlsWithoutBaseUrl = {
  chat: {
    flows: '/api/chats/flows',
    sessions: '/api/chats/sessions',
    processMessage: '/api/chats/processMessage',
    getSessionList: '/api/chats/sessions',
    getSessionDetail: (sessionId: string) => `/api/chats/sessions/${sessionId}`,
    deleteSessionById: (sessionId: string) => `/api/chats/sessions/${sessionId}`,
    deleteFile: (sessionId: string, fileId: number) => `/api/chats/delete-file/${sessionId}/${fileId}`,
    uploadFile: `/api/chats/upload-file`,
  },
} as const

export const chatAIApiUrls = concatBaseUrl(askBIApiUrlsWithoutBaseUrl)

// 不用校验登录的页面
export const loginWhitelist: string[] = [askBIPageUrlsWithoutBaseUrl.login]

/**
 * client 和 server 共享的一些工具函数
 * 注意：不要依赖 DOM 和 Node.js 的 API
 */
import dayjs from 'dayjs'
import { customAlphabet } from 'nanoid'
import React, { lazy, Suspense } from 'react'
import { BASE_URL } from './constants'
import { ConcatBaseUrlReturnType } from './common-types'

/** 检查 switch case 分支是否对所有 union type 或 enum 的值做了穷举，用在 default 分支中 */
export function assertExhaustive(_value: never, message: string = 'Exhaustive check failed'): never {
  const error = new Error(message)
  console.error('assertExhaustive error', _value, error)
  throw new Error(`${message}: ${JSON.stringify(_value)}`)
}

/** 按照和旭东约定的生成traceId的规则 生成 traceId https://mk70znjkuv.feishu.cn/wiki/F97gwygjIi4VJqkyUIJcEefIn2b*/
export function createTraceId() {
  const alphabet = '0123456789'
  const generate = customAlphabet(alphabet, 5)
  return `frontend_${dayjs(new Date()).format('YYYYMMDDHHmmss')}_${generate()}`
}

/** 判断当前是否是node环境，前端环境不支持env配置 */
export function isNodeEnv() {
  // 在Node.js环境中，process对象是全局可用的
  return typeof process !== 'undefined' && process.versions && process.versions.node
}

// 判断是否是数字并且不为null并且是整数
export function isPositiveInteger(value: number | string | string[]) {
  return typeof value === 'number' && !isNaN(value) && Number.isInteger(value)
}

/**
 * 把毫秒转成可读的字符串，如 1天 2小时 3分钟 4秒
 * @param ms 毫秒
 * @returns 可读的字符串
 */
export function secondsToReadableTime(ms: number) {
  const seconds = ms / 1000
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = Math.floor(seconds % 60)

  let result = ''

  if (days > 0) {
    result += `${days}天 `
  }
  if (hours > 0) {
    result += `${hours}小时 `
  }
  if (minutes > 0) {
    result += `${minutes}分钟 `
  }
  if (remainingSeconds > 0) {
    result += `${remainingSeconds}秒`
  }

  return result.trim()
}

// 是数字类型的列
export const isColumnTypeBelongsNumber = (type: string) => {
  return ['INT', 'BIGINT', 'DECIMAL', 'NUMERIC', 'FLOAT', 'DOUBLE'].some((i) => {
    return new RegExp(i, 'i').test(type)
  })
}

export function padZero(number: number, totalLength: number): string {
  return number.toString().padStart(totalLength, '0')
}

type FormatOptions = {
  prefix?: string
  decimals?: number
  suffix?: string
  isPercentage?: boolean
  useThousandSeparator?: boolean
}

function parseTemplate(template: string): FormatOptions {
  const options: FormatOptions = {}
  const regex = /^(?<prefix>[^,.\d]*)(?<integer>,)?(?<decimals>\.\df)?(?<suffix>[^,.\d]*)$/
  const match = template.match(regex)

  if (match && match.groups) {
    options.prefix = match.groups.prefix || ''
    options.decimals = match.groups.decimals !== undefined ? parseInt(match.groups.decimals[1]) : undefined
    options.suffix = match.groups.suffix || ''
    options.isPercentage = options.suffix === '%'
    options.useThousandSeparator = !!match.groups.integer
  }

  return options
}

/**
 * @param num number 类型的数值
 * @param decimals 保留小数位数
 * @returns 带有单位的数值 万 千万 亿
 */
export function formatNumberWithChineseUnit(originNum: number, decimals?: number, suffix?: string): string {
  if (typeof originNum !== 'number' || Number.isNaN(originNum)) {
    return originNum + ''
  }
  const flag = originNum >= 0 ? 1 : -1
  const num = Math.abs(originNum)
  const newSuffix = suffix || ''
  const magnitude = ['十', '百', '千', '万', '亿']

  const format = (num: number): string => {
    if (Number.isInteger(num)) {
      return (flag * num).toString()
    }
    if (decimals !== undefined) {
      return (flag * num).toFixed(decimals)
    }
    return flag * num + ''
  }

  const isMagnitudeSuffix = newSuffix && magnitude.some((item) => newSuffix.indexOf(item) > -1)
  if (isMagnitudeSuffix) {
    // 如果有量级,则直接带上后缀返回
    return format(num) + newSuffix
  }

  const units = [
    { value: 1e8, unit: '亿' + newSuffix },
    { value: 1e4, unit: '万' + newSuffix },
  ]

  for (const { value, unit } of units) {
    if (num >= value) {
      return (flag * (num / value)).toFixed(decimals ?? 2) + unit
    }
  }
  return format(num) + newSuffix
}

export function formatNumber(number: number | string, template: string | undefined): string {
  if ((typeof number === 'string' && !IS_VALID_STRING2NUMBER_REGEXP.test(number)) || !template) {
    return number.toString()
  }

  const { prefix, decimals, suffix, useThousandSeparator, isPercentage } = parseTemplate(template)

  // If formatting as percentage, multiply the number by 100.
  const formattedNumber = isPercentage ? +number * 100 : +number

  let result = formatNumberWithChineseUnit(formattedNumber, decimals, suffix)

  if (useThousandSeparator) {
    result = thousandSeparateNum(result)
  }

  return `${prefix || ''}${result}`
}

function thousandSeparateNum(num: string) {
  const parts = num.split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return parts.join('.')
}

export function isValidFormatTemplate(formatTemplate: string): boolean {
  if (formatTemplate === '%') {
    return false
  }
  const regex = /^[^\d]*(,?)(\.\df)?(%.*)?$/
  return regex.test(formatTemplate)
}

export const IS_VALID_STRING2NUMBER_REGEXP = /^[+-]?(\d+(\.\d*)?|\.\d+)([eE][+-]?\d+)?$/

/** 2位小数的百分比 */
export const DEFAULT_FORMAT_RATIO = ',.2f%'
/** 千分位2位整数 */
export const DEFAULT_FORMAT_DECIMAL = ',.2f'
/** 千分位整数 */
export const DEFAULT_FORMAT_INT = ',.0f'

/**
 * 将object对象转变成FormData对象
 */

export function objectToFormData(obj: Record<string, string | Blob>) {
  const formData = new FormData()
  Object.keys(obj).forEach((key) => {
    formData.append(key, obj[key])
  })
  return formData
}

/**
 * 匹配字符串是否是由下划线、字母、数字组成，并且是由字母为开头
 */

export function isAlphaNumberAndAlphaStart(str: string) {
  if (!str) {
    return true
  }
  const reg = /^[a-zA-Z]+\w*$/
  const valid = reg.test(str)
  return valid
}

/**
 * 获取当前北京时间的ISO字符串表示
 *
 * @returns 返回当前北京时间的ISO字符串表示
 */
export function getCurrentBeijingTimeIsoString(): string {
  const now = new Date() // 获取当前时间
  const beijingOffset = 8 * 60 * 60 * 1000 // 东八区相对于UTC的毫秒偏移量
  const beijingTime = new Date(now.getTime() + beijingOffset)
  return beijingTime.toISOString()
}

/**
 * 判断是否是宝武场景
 * @param metricTableName string
 * @returns boolean
 */
export function isBaoWu(metricTableName?: string) {
  if (!metricTableName) {
    const isNode = typeof process !== 'undefined' && process.versions != null && process.versions.node != null
    // doc场景下会有问题, 仅适用于宝武内部场景
    if (!isNode && window?.location?.search) {
      const search = window.location.search
      if (search.indexOf('isBaoWu=1')) {
        return true
      }
    }
    return false
  }
  return metricTableName.indexOf('T_ADS_FACT_WSSJ_TOTAL_INDEX') > -1
}

/**
 * 仓库中原有的Fallback组件
 */
function Fallback() {
  return React.createElement('div', { className: 'flex items-center justify-center' }, 'Loading...')
}

/**
 * 缓存动态导入的组件，后续增加路由探测时会增加node端读取路由文件，此时该函数也会被node所读取到
 * 考虑到utils是以ts为结尾，替换文件后缀后会使commit信息变化，使用React.createElement的方式创建组件
 * 在首次加载完后，会把组件存在CachedComponent中，第二次不会再次调用import函数，直接发回CachedComponent
 * 该函数会在引入在路由的时候直接被调用，每一个路由只会调用一次该函数
 * @param load 动态加载组件的函数
 */
export function cachedDynamicImportComponent(load: () => Promise<{ default: React.ComponentType<any> }>) {
  // 如果不为null，表明请求过一次，已经缓存了
  let CachedComponent: React.ComponentType | null = null
  // 封装一次load函数，获取到实际的组件用于缓存
  function dynamicImport() {
    return load().then((mod) => {
      CachedComponent = mod.default
      return mod
    })
  }
  // 仅在浏览器端执行，该函数会因为node端需要路由猜测，所以该文件在node端也会被执行
  // 暂时先禁用
  // globalThis.window?.addEventListener('load', () => {
  //   const pathname = globalThis.window?.location.pathname
  //   if (pathname !== aiPlatformPageUrls.login && pathname !== aiPlatformPageUrls.loginError) {
  //     idleConcurrencyQueue.addTaskAndRun(dynamicImport)
  //   }
  // })
  // 实际被渲染的组件
  function DynamicImportComponent(props: any) {
    if (CachedComponent) return React.createElement(CachedComponent, { ...props })
    const Component = lazy(dynamicImport)
    return React.createElement(
      Suspense,
      {
        fallback: React.createElement(Fallback),
      },
      React.createElement(Component, { ...props }),
    )
  }
  // 挂载一下本身的import函数方便后期处理import
  DynamicImportComponent.load = load
  return DynamicImportComponent
}

/**
 * @param arr
 * @returns length of array
 */
export const length = (arr?: any[]) => {
  return arr?.length || 0
}
/**
 *
 * @param num
 * @returns chinese number
 */
export const numberToChineseNumber = (num: number): string => {
  if (num === 0) return '零'

  const chineseNumbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九']
  const unit = ['', '十', '百', '千', '万', '亿']

  let result = ''
  let unitIndex = 0

  while (num > 0) {
    const digit = num % 10
    if (digit > 0) {
      result = chineseNumbers[digit - 1] + unit[unitIndex] + result
    } else if (result && !result.startsWith('零')) {
      result = '零' + result
    }

    num = Math.floor(num / 10)
    unitIndex++
  }

  // Remove leading zero if present
  return result.replace(/^零+/, '')
}

/**
 * 递归处理每一条url，如果它是字符串，且没有BASE_URL前缀，就加上
 * 如果是对象就遍历后递归加上
 */
export function concatBaseUrl<T>(value: T): ConcatBaseUrlReturnType<T> {
  let res!: any
  if (!value) res = value
  else if (typeof value === 'string') {
    if (!value.startsWith(BASE_URL)) {
      res = BASE_URL + value
    } else {
      res = value
    }
  } else if (typeof value === 'function') {
    res = (...args: any[]) => concatBaseUrl(value(...args))
  } else if (typeof value === 'object') {
    res = Object.fromEntries(Object.entries(value).map(([k, v]) => [k, concatBaseUrl(v)]))
  } else {
    res = value
  }
  return res
}

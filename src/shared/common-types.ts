/* eslint-disable @typescript-eslint/naming-convention */
/**
 * @description 客户端和服务端共用的类型定义
 */

import { UploadFile } from 'antd'

/** 产品名称 */
export const AppNames = ['ask-bi', 'ask-doc'] as const
export type AppName = (typeof AppNames)[number]

export const AskbiNavNames = ['chat-data', 'ask-doc', 'chart-manage', 'metric-store'] as const
export type AskbiNavName = (typeof AskbiNavNames)[number]

export type ThemeType = 'light' | 'dark'

export interface BlobWithRatio extends Blob {
  ratio: number
}

/** 多任务的类型  Gpt必须作为Backend的子集 */

export interface UserBasicInfo {
  id: string
  username: string
}

/** chat接口 传递给后端的参数 */
export interface ChatRequestProps {
  session_id: string | null
  chatId: string
  text: string
}

export interface LlmListByUsernameResponse {
  llmList: Llm[]
  defaultLlmType: LlmType
}

/**提问输入框json格式 */
export type JsonContentItem = {
  type?: string
  'data-type'?: string
  'data-content'?: string
  'data-id'?: string
  'data-name'?: string
  children?: JsonContentItem[]
  [key: string]: string | JsonContentItem[] | number | undefined
}

export enum STRUCTURED_MESSAGE_DATA_TYPE {
  TEXT = 'text',
}

export type MessageInputEditorRef = {
  setHtml: (data: JsonContentItem[] | string) => void
  updateCursorPosition?: () => void
}

// 错误类型(包括后端返回的及前端处理的)
export const ChatResponseErrorTypes = {
  E_UNKNOWN: 'E_UNKNOWN', // 其他错误
  QUESTION_NOT_SUPPORTED: 'QUESTION_NOT_SUPPORTED', // 问题不支持
} as const

/** 聊天中一条信息的状态 */
export const ChatStatus = {
  /** 已发出请求，还没有收到结果 */
  pending: 'pending',
  /** 成功 */
  success: 'success',
  /** 失败 */
  failure: 'failure',
} as const
export type ChatStatusType = keyof typeof ChatStatus

export interface APIResponse<T> {
  code: number
  data?: T
  msg?: string
}

/** 用户输入的会话类型，内容只有 string */
export interface AskChatItem {
  role: 'user'
  content: string
  jsonContent: string
  parentId?: string | null
  fileList?: UploadFile[]
}

export interface Message {
  role: 'system' | 'assistant' | 'user' | 'function'
  content: string
  extra_info?: string
}

export interface Conversation {
  id: string
  /**
   * 用户的问题列表，方便其他地方使用。
   * 注意这个 asks 和 text2SqlMessages 中的 users 并不一定完全对应
   */
  asks: string[]
  /** text2TaskType 的会话历史，ChatGPT 支持多任务使用 */
  /** TODO: 让多任务支持多轮会话 */
  // text2TaskTypeMessages: Message[]
  /**
   * 当前选中的场景，目前只支持1个，以后需要扩展成多个
   * 修改后，需要清空当前的会话历史，也就是清空 asks，text2SqlMessages
   */
  sceneId: string
  /** text2sql 的会话历史，table 变更后会被清空 */
  text2SqlMessages: Message[]
  // TODO: 添加 text2ChartGroupMessages 让图表大类支持多轮会话
}

export const ConverChatErrorTypes = {
  NO_METRICS: 'NO_METRICS',
  NO_DATA_AUTHORITY: 'NO_DATA_AUTHORITY',
  OTHER_ERROR: 'OTHER_ERROR',
  CHIT_CHAT: 'CHIT_CHAT',
  LLM_ERROR: 'LLM_ERROR',
  // NOT_ANALYSIS: 'NOT_ANALYSIS',
  LATEST_DATA_NOT_EXIST: 'LATEST_DATA_NOT_EXIST',
  FUTURE_DATA_NOT_EXIST: 'FUTURE_DATA_NOT_EXIST',
  DATA_ERROR_FEEDBACK: 'DATA_ERROR_FEEDBACK',
  QUESTION_NOT_SUPPORTED: 'QUESTION_NOT_SUPPORTED',
} as const

export const ConverChatErrorTypesMap = {
  [ConverChatErrorTypes.NO_METRICS]: '指标未录入',
  [ConverChatErrorTypes.NO_DATA_AUTHORITY]: '没有数据权限',
  [ConverChatErrorTypes.OTHER_ERROR]: '其他错误',
  [ConverChatErrorTypes.CHIT_CHAT]: '非数据分析问题',
  [ConverChatErrorTypes.LLM_ERROR]: '大模型网络断开',
  // NOT_ANALYSIS: '非数据分析类问题',
  [ConverChatErrorTypes.LATEST_DATA_NOT_EXIST]: '时间段下无数据-暂未更新',
  [ConverChatErrorTypes.FUTURE_DATA_NOT_EXIST]: '时间段下无数据-未来时间',
  [ConverChatErrorTypes.DATA_ERROR_FEEDBACK]: '反馈数据问题',
  [ConverChatErrorTypes.QUESTION_NOT_SUPPORTED]: '问题类型不支持',
} as const

export interface AnsChatItemBase {
  type: 'text' | 'hello-text' | 'chitchat' | 'doc' | 'chat-error' | 'apply-result'
}

// export interface AssistantChartChatItem extends AnsChatItemBase {
//   type: 'doc'
//   /** 警告的文案 */
//   infoText?: string
//   rows: OlapRow[]
//   /** 查询数据使用的 SQL */
//   sql?: string
//   /** 标识图表的taskType */
//   taskType: any
// }

export interface AssistantTextChatItem extends AnsChatItemBase {
  type: 'text'
  text: string
}

export interface AssistantHelloTextChatItem extends AnsChatItemBase {
  type: 'hello-text'
  text: string
}

export interface AssistantChitchat extends AnsChatItemBase {
  type: 'chitchat'
  text: string
}

export interface AssistantApplyResultChat extends AnsChatItemBase {
  type: 'apply-result'
  text: string
}

export interface AssistantChatError extends AnsChatItemBase {
  type: 'chat-error'
  unreadyReason?: string
  errType: ChatResponseError['errType']
  subUnreadyReason?: string
  metricNames?: string[]
  sql?: string
  whereCompany?: string[]
}

export type AssistantChatItem =
  | AssistantTextChatItem
  | AssistantHelloTextChatItem
  | AssistantChitchat
  | AssistantApplyResultChat
  | AssistantChatError

/** 机器人回复的会话类型，内容有多种：文本，图表，SQL */
export interface AnsChatItem {
  role: 'assistant'
  content: AssistantChatItem[]
  sceneId?: string
  status: ChatStatusType
  ansTime?: Date
}

export type ChatItem = AskChatItem | AnsChatItem

/** 一次聊天的内容，包含问题和答案的所有信息 */
export interface Chat {
  /** use nanoid */
  id: string
  /** 问题 */
  ask: AskChatItem
  /** 回答 */
  ans: AnsChatItem[]
  /** 是否为系统提示的信息，不会发给 chatgpt，第一条系统打招呼文案为 true */
  isSystemPrompt?: boolean
  askTime: Date

  /** Human Feedback: 喜欢、不喜欢、无 */
  feedback?: 'like' | 'dislike' | 'none'
  /** 反馈的时间 */
  feedbackTime?: Date
  /** feedback 不喜欢的原因： inaccurate 结果不准确，sql 错误，chart 图表错误，其他 */
  feedbackDislikeType?: 'inaccurate' | 'wrong-sql' | 'wrong-chart' | 'other'
  /** feedback 不喜欢的具体原因，feedbackDislikeType 为 other 的时候填写  */
  feedbackDislikeReason?: string
}

export interface BaseChatResponse {
  ready: true
  sessionId: string
  sceneId?: string
  traceId?: string
  isPartialRow?: boolean
  partialRowMsg?: string
  chatId?: string
}

export interface ChatResponseChitchat extends BaseChatResponse {
  taskType: 'chitchat'
  rows: any
}

export interface ChatResponseApplyResultChat extends BaseChatResponse {
  taskType: 'apply-result'
  text: any
}

export interface ChatResponseError extends Omit<BaseChatResponse, 'ready'> {
  taskType: 'chat-error'
  ready: false
  errType: any
  // 部分错误原因由前端写死的, 所以unreadyReason可以改为可选
  unreadyReason?: string
  sql?: string
  originResponse?: any
}

export type ChatResponse = ChatResponseChitchat | ChatResponseApplyResultChat | ChatResponseError

export type LlmTypeGPT = 'cogview-3-plus' | 'wanx-v1' | 'sd-llm'

/** 图表推荐使用纯规则实现 */
export type MockChartRecommendType = 'chart-type-use-rule'

export type LlmType = LlmTypeGPT | 'vllm-mixtral-8x7b-chat' | MockChartRecommendType

export interface Llm {
  type: LlmType
  name: string
  abbrName: string
  tokenLimit: number
  logo: string
  /** 是否禁用模型 */
  disable: boolean
}
/* ChatLog 日志的类型，对应包括 src/server/ai/prompts.ts 中的函数名 + 一些外部大模型的直接调用 */
export const ChatLogTypes = {
  nl2Sql: 'nl2Sql',
  text2ChartGroup: 'text2ChartGroup',
  questionSuggestions: 'questionSuggestions',
  chartInsight: 'chartInsight',
} as const
export type ChatLogType = keyof typeof ChatLogTypes

/** Winston日志级别，项目中仅用info和error */
export const WinstonLogLevels = {
  error: 'error', // 0
  warn: 'warn', // 1
  info: 'info', // 2
  http: 'http', // 3
  verbose: 'verbose', // 4 用于提供详细的信息，通常用于调试和诊断目的
  debug: 'debug', // 5 用于提供详细的调试信息，通常用于调试应用程序中的问题。
  silly: 'silly', // 6 用于提供冗长或无关紧要的信息，通常用于开发阶段的详细日志记录。
} as const
export type WinstonLogLevel = keyof typeof WinstonLogLevels

export type SystemInfoResponse = {
  startTime: string
}

export type UserPermissionDatasource = {
  id: string
  username: string
  datasourceId: string
}
export type UserPermissionLlmType = {
  id: string
  username: string
  llmType: string
}
export type UserPermissionProject = {
  id: string
  username: string
  semanticProjectId: string
}

/** 后端返回的一行数据，和 RowDataPacket 区别是，OlapRow 的 key 只能为 string，不支持 number */
export interface OlapRow {
  constructor: {
    name: 'OlapRow'
  }
  [column: string]: any
}

export interface MenuItem {
  key: string
  label: React.ReactNode
  path?: string
  icon?: React.ReactNode
  children?: MenuItem[]
  type?: 'group' // 当type为group时，会作为分组处理
}

export interface UserInfoType {
  admin: boolean
  token: string
  email: string
  pageAccessResultList: string[] | null
  groups: string[]
  register: boolean
  username: string
  lastLoginAt: string
}

export type ConcatString<T, S extends string = ''> = T extends string
  ? `${S}${T}`
  : T extends (...args: infer P) => infer R
    ? (...args: P) => ConcatString<R, S>
    : T extends object
      ? {
          [key in keyof T]: ConcatString<T[key], S>
        }
      : T

export type ConcatBaseUrlReturnType<T> = ConcatString<T, '$BASE_URL'>

export interface FlowType {
  flow_name: string
  description: string
  version: string
  initial_state: string
  final_states: string[]
  total_states: number
}

export interface FlowSessionType {
  session_id: string
  flow_name: string
  current_state: string
  prompt: string
  input_type: 'text' | 'file' | 'select' | string // 可以根据实际情况扩展
  options: string[] | null
  requires: string[] | null
}

export interface SessionItemType {
  session_id: string
  user_id: string | null
  flow_name: string
  current_state: string
  status: 'active' | 'inactive' | string // 可根据实际枚举值调整
  progress_percentage: number
  created_at: string
  updated_at: string
}

export interface UploadedFileResultType {
  filename: string
  file_path: string
  file_size: number
  content_type: string
  upload_time: string // ISO 格式的时间字符串，可以用 Date 处理
  validation_result: string | null
  file_upload_hint: string
  file_id: number
}

export interface CustomUploadFile extends UploadFile {
  fileId?: number
}

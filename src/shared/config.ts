/**
 * @description 通用的配置代码
 */

import axios from 'axios'
import { message } from 'antd'
import { DEFAULT_TIMEOUT } from './constants'
import { createTraceId } from './common-utils'
import { aiPlatformPageUrls } from './url-map'
import { APIResponse } from './common-types'

let isShowErrorMessage = false

// 所有请求将会有10秒的超时时间
axios.defaults.timeout = DEFAULT_TIMEOUT
axios.interceptors.request.use(function (config) {
  if (config.headers.traceId) {
    return config
  }
  config.headers.traceId = createTraceId()
  return config
})

// 响应拦截器  axios 接口做一些通用的处理，返回值有 code 的时候，判断 code 如果 !== 0，抛出错误
axios.interceptors.response.use(async function (response) {
  const { data } = response

  // code 不存在，说明非标准格式，不做检查，直接放过
  if (data?.code == null) {
    return response
  }

  // 如果是 401 错误，表示是cookie失效，执行重定向操作 339971为ranger后台登录失效的code
  if (!isShowErrorMessage) {
    if (data.code === 401) {
      await handleLoginRedirect(data?.msg)
    } else if (data.code === 403) {
      isShowErrorMessage = true
      throw new Error(data?.msg || '暂无权限，请联系管理员开通权限')
    }
  }

  if (data.code !== 0) {
    throw new APIError(response.data.msg, response.data)
  }

  // 其他情况，直接返回 response
  return response
})

// 设置全局的 Axios 默认配置
export function setAxiosDefaults(traceId: string) {
  axios.defaults.headers.common['traceId'] = traceId
}

/**
 * 处理登录重定向
 *
 * @param msg 提示信息，默认为“登录已过期，请重新登录”
 * @returns 无返回值
 */
async function handleLoginRedirect(msg: string) {
  isShowErrorMessage = true

  if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
    message.error(msg || '登录已过期，请重新登录')
    await new Promise((resolve) => setTimeout(resolve, 800))

    const brandInfo = localStorage.getItem('brandInfo')
    if (!brandInfo || brandInfo === 'null') {
      window.location.href = aiPlatformPageUrls.login
    } else {
      const appId = brandInfo ? JSON.parse(brandInfo).appId : null
      const loginUrl = appId ? `${aiPlatformPageUrls.login}?appId=${appId}` : aiPlatformPageUrls.login
      localStorage.clear()
      window.location.href = loginUrl
    }
  } else {
    // Node.js 环境中的处理逻辑
    console.error(msg || '登录已过期，请重新登录')
  }
}

export class APIError extends Error {
  response: APIResponse<any>

  constructor(message: string, response: APIResponse<any>) {
    super(message)
    this.name = this.constructor.name
    this.response = response
  }
}

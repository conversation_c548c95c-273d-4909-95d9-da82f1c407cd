/**
 * @description 存放所有共享的常量
 */

/** Input输入框的默认长度 */
export const DEFAULT_INPUT_MESSAGE_LENGTH = 1000

/** 优先展示表格数据 */
export const SHOW_TABLE_FIRST = false

/** 饼图最多显示的饼块数量，超过的合并为“其他” */
export const PIE_CHART_DATA_LIMIT = 15

/** 判断数值类型的这一列是否属于维度（年份）[数值类型的值去重后 与 原始数据数量的比例]  */
export const IS_DIMENSION_PROPORTION = 0

/** 散点图最多支持的分类个数  */
export const SCATTER_CHART_CLASSIFIED_NUM = 5

/** 散点图图例显示精度的界限，低于这个数值，才会显示精度 */
export const SCATTER_LEGEND_PRECISION_BOUND = 1

/** 柱状图最多展示条数 */
export const BAR_CHART_NUM = 20

/** echarts x轴最多展示label数 */
export const X_LABEL_LIMIT = 5

/** echarts 缩放条出现x轴数组最大长度 */
export const DATAZOOM_SHOW_LIMIT = 20

/** 散点气泡图不同类型的气泡颜色 */
export const SCATTER_CHART_CLASSIFIED_BUBBLE_COLOR = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']

/** GPT 上下文中保留的对话数（一问一答) */
export const GPT_MESSAGE_REMAIN_NUM = 3

/** GPT 超时 10分钟 */
export const GPT_TIMEOUT = 30 * 60 * 1000

/** 报告生成 超时 20 分钟 */
export const DOC_REPORT_TIMEOUT = 20 * 60 * 1000

/** 默认接口超时 1 分钟 */
export const DEFAULT_TIMEOUT = 30 * 60 * 1000

/** Xengine query 超时20s */
export const XENGINE_QUERY_TIMEOUT = 20 * 1000

/** Ask Doc 默认知识库名字 */
export const DEFAULT_ASK_DOC_FOLDER_ID = 'DEFAULT_FOLDER'

/** mock的多场景 scene Id */
export const MULTI_SCENE_CHAT_MOCK_SCENE_ID = 'MULTI_SCENE_CHAT_MOCK_SCENE_ID'

/** 如果 select 没有 limit，则添加默认的 limit 数量 */
export const DEFAULT_SELECT_LIMIT = 100

export const ECHARTS_CHART_TYPES = [
  'LineChart',
  'ColumnChart',
  'PieChart',
  'ScatterChart',
  'TreemapChart',
  'GroupColumnChart',
  'StackedColumnChart',
  'MultiLineChart',
  'RankBarChart',
] as const
export const LOCAL_CHART_TYPES = ['Kpi', 'SimpleTable', 'AttrAnalysis', 'AttrMetricAnalysis'] as const
export const CHART_TYPES = [...ECHARTS_CHART_TYPES, ...LOCAL_CHART_TYPES] as const

/** 最多上传10个文件 */
export const MAX_COUNT_UPLOAD_LIMIT = 10
/** 文件大小10M */
export const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10M

// 根据 url 中的 chrome=true 来判断是否在 chrome 插件中
export const IS_CHROME_EXTENSION =
  typeof window === 'object' ? window?.location?.search?.includes('chrome=true') : false

/** chrome 插件支持的 action type */
export const CHROME_ACTIONS = {
  closeDisplay: 'closeDisplay',
  toggleDisplay: 'toggleDisplay',
  changeTheme: 'changeTheme',
} as const

/** askbi 中 文档问答使用的知识库 id */
export const ASKBI_INTEGRATED_ASKDOC_FOLDER_ID: string = 'rzQaALgXZYpxXHPQjWTzM'

/** chrome 插件内 iframe 使用的 URL */
// 发布使用
export const CHROME_IFRAME_URL = 'https://askbi-pre.dipeak.com/chat/new?chrome=true'
// 本地开发使用
// export const CHROME_IFRAME_URL = 'http://localhost:3000/chat/new?chrome=true'

/** 判断是否浏览器环境 */
export const IS_BROWSER = typeof window === 'object'
/** 判断是否是H5移动端 */
export const IS_H5 = typeof window === 'object' ? /Mobi|Android|iPhone/i.test(window.navigator.userAgent) : false

/** 展示移动端首页HEADER */
const SHOW_HEADER = false
export const DISPLAY_MOBILE_HEADER = !IS_H5 || (IS_H5 && SHOW_HEADER)

/** 展示前端[数据解读] */
export const DISPLAY_INSIGHT = true
export const DISABLE_INSIGHT_PROJECT_NAME_LIST = ['baowu', '宝武']

export const IS_DARK =
  typeof window === 'object'
    ? !IS_H5 && // 移动端禁止dark
      (localStorage.theme === 'dark' ||
        (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches))
    : false

/** 图表色系列表 */
export const CHART_THEMES = [
  {
    type: 'default',
    name: '默认',
    colors: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'],
  },
  {
    type: 'purple',
    name: '紫色',
    colors: ['#b6a2de', '#2ec7c9', '#5ab1ef', '#ffb980', '#d87a80', '#8d98b3', '#e5cf0d', '#97b552'],
  },
  {
    type: 'yellow',
    name: '蓝色',
    colors: ['#001852', '#0098d9', '#2b821d', '#005eaa', '#339ca8', '#f7f494', '#e6b600', '#fcce10'],
  },
  {
    type: 'grey',
    name: '灰色',
    colors: ['#6e7074', '#d87c7f', '#919e8b', '#d7ab82', '#61a0a8', '#efa18d', '#fe8463', '#c92a1f'],
  },
  {
    type: 'red',
    name: '红色',
    colors: ['#c92a1f', '#e01f54', '#e87c25', '#f5bf44', '#f58db2', '#d0648a', '#fe8463', '#d87c7c'],
  },
] as const

/** 图表色系类型 */
export type ChartThemeType = (typeof CHART_THEMES)[number]['type']

export const CompanyName: string = `©2022-${new Date().getFullYear()} 某某科技有限公司`
export const RecordNumber: string = '粤ICP备2024'

/** 用于 mysql2 查询元数据库，设置一个唯一的 datasourceId */
export const PRISMA_DATASOURCE_ID = Symbol.for('@@PRISMA_DATASOURCE_ID')

export const messageSafeAreaClass = 'message-input-click-safe-area'
export const dimensionsSafeAreaClass = 'dimension-click-safe-area'
export const recommendedSafeAreaClass = 'recommended-click-safe-area'
export const selectedDimensionClass = 'border border-[#8BECD4] bg-[#8BECD4]'
export const unselectedDimensionClass = 'border border-[#A8DED1] bg-[#F7FFFD]'

export const selectedMetricClass = 'border border-[#C8C3F0] bg-[#C8C3F0]'
export const unselectedMetricClass = 'border border-[#C8C3F0] bg-[#F3F2FB]'

/** 特殊标记符，用于在prompt-studio中测试时，会携带该信息访问 */
export const TAG_PROMPT_TEST = 'prompt-test'

/** 全局通用前缀 */
export const BASE_URL =
  typeof __BASE_URL__ !== 'undefined'
    ? __BASE_URL__ // 浏览器端注入变量
    : (globalThis.process?.env?.BASE_URL ?? '') // node 端注入变量

export const XEngineHomePath = BASE_URL + '/manage/external-datasource/catalog-list'

export const dataErrorFeedbackCodes = ['指标不对', '公司名不对', '数据值不对', '其他']

export const codeValueSplitChar = '\x01\x01\x01'

export const DEFAULT_FLOWS = [
  {
    flow_name: 'cafe_license_application',
    description: '新加坡咖啡店营业执照申请完整流程',
    version: '1.0.0',
    initial_state: 'welcome_user',
    final_states: ['done'],
    total_states: 40,
  },
  {
    flow_name: 'simple_test_flow',
    description: 'xxx公司服务xxx流程',
    version: '1.0.0',
    initial_state: 'welcome_user',
    final_states: ['done'],
    total_states: 10,
  },
]

/**
 * @description client 侧的工具函数集合
 */
import React, { useEffect, useRef } from 'react'
import { debounce } from 'lodash-es'
import { theme as antdTheme } from 'antd'
import qs from 'query-string'
import {
  SCATTER_CHART_CLASSIFIED_NUM,
  SCATTER_LEGEND_PRECISION_BOUND,
  SCATTER_CHART_CLASSIFIED_BUBBLE_COLOR,
  BASE_URL,
} from '../shared/constants'
import { MenuItem, OlapRow, ThemeType } from '../shared/common-types'

export function typewriteInterval(
  str: string,
  callbackfn: React.Dispatch<React.SetStateAction<string>>,
  onComplete: () => void,
) {
  let gapTimeoutId
  const characters = str.split('')
  const interval = 1000 / characters.length
  const intervalId = setInterval(() => {
    if (characters.length < 1) {
      // 当前的循环结束，清空一下值，等待2秒，开始调用下一个
      clearInterval(intervalId)
      gapTimeoutId = setTimeout(() => {
        callbackfn('')
        onComplete()
      }, 2000)
    } else {
      const char = characters.shift()
      callbackfn((val) => val + char)
    }
  }, interval)
  return [intervalId, gapTimeoutId]
}

const echartsLeftMarginList = [
  { length: 10, left: 92 },
  { length: 9, left: 82 },
  { length: 8, left: 75 },
  { length: 7, left: 67 },
  { length: 6, left: 58 },
  { length: 5, left: 54 },
  { length: 4, left: 50 },
  { length: 3, left: 45 },
  { length: 2, left: 40 },
  { length: 1, left: 40 },
]

function getLeftByLength(length: number): number | undefined {
  const enumItem = echartsLeftMarginList.find((item) => item.length === length)
  return enumItem?.left
}

const ua = typeof window === 'object' ? window.navigator.userAgent : ''
export const isIOS = /iPhone|iPod|iPad/i.test(ua) ? 1 : 0
export const isAndroid = /(Android)/i.test(ua) ? 1 : 0

/**
 * 找到 data 指定列的最大值，根据最大值的位数，返回 echarts 的 left 值
 * @param data
 * @param columnName 度量在 data 中的列名
 * @returns
 */
export function getEchartsLeftMargin(data: OlapRow[], columnName: string): number | string {
  const maxSecondaryValue = Math.max(...data.map((obj) => obj[columnName]))
  if (typeof maxSecondaryValue !== 'number') {
    return 50
  }
  const salesValue = Number(maxSecondaryValue)
  let length = Math.floor(Math.log10(salesValue)) + 1

  const firstDigit = Math.floor(salesValue / Math.pow(10, length - 1))
  if (firstDigit === 8 || firstDigit === 9) {
    length = length + 1
  }
  return getLeftByLength(length) || '20%'
}

/** 获取某一列的最大值和最小值 */
export function getMaxAndMinValues(data: OlapRow[], columnName: string) {
  const salesFrequencies = data.map((row) => Number(row[columnName]))
  const maxValue = Math.max(...salesFrequencies)
  const minValue = Math.min(...salesFrequencies)
  return [maxValue, minValue]
}

/** 获取散点图的气泡大小 */
export function mapValueToRange(value: number, max: number, min: number) {
  // 设置映射范围
  const minValue = 10
  const maxValue = 30
  // 计算原始值与最小值的差值和最大值与最小值的差值
  const valueRange = value - min
  const maxRange = max - min
  return (valueRange / maxRange) * (maxValue - minValue) + minValue
}

/** 获取string列的分类列 可能是空值 */
export function getColumnNamesForCategoricalColumns(
  // FIXME: 把 data 的 类型定义放到 common-types 中
  data: Array<{ [key: string]: string | number }>,
  columnIndex: string,
) {
  const uniqueValues = new Set(
    data.map((row) => {
      return row[columnIndex]
    }),
  )

  return [...uniqueValues].slice(0, SCATTER_CHART_CLASSIFIED_NUM)
}

/**
 * 四个通道的散点图 获取series配置
 * 数据被转化成了二维数组 可以对index作Number()处理
 */
export function getSeriesOptions(
  classifiedColumn: (string | number)[],
  data: (string | number)[][],
  xLabel: string,
  yLabel: string,
  classifiedColumnIndex: string,
  maxAndMinValue: number[],
  bubbleLabelIndex: string,
): Array<{ [key: string]: any }> {
  const [maxValue, minValue] = maxAndMinValue
  return classifiedColumn.map((column, index) => ({
    name: column,
    type: 'scatter',
    encode: {
      x: xLabel,
      y: yLabel,
    },
    data: data.filter((item) => item[Number(classifiedColumnIndex)] === column),
    itemStyle: {
      normal: {
        color: SCATTER_CHART_CLASSIFIED_BUBBLE_COLOR[index],
        opacity: 0.8,
      },
    },
    symbolSize: function (data: Array<any>) {
      const convertData = data[Number(bubbleLabelIndex)]
      if (!isNaN(convertData)) return mapValueToRange(convertData, maxValue, minValue)
      return 10
    },
  }))
}

/** 对数值进行向上或向下取整 */
function transformAndRoundValues(maxValue: number, minValue: number) {
  const processValue = (value: number, roundUp: boolean) => {
    // TODO: 对负数进行处理
    if (value <= 0) {
      return 0
    }
    const numberOfDigits = Math.floor(Math.log10(value)) + 1
    const powerOfTen = Math.pow(10, numberOfDigits - 1)

    const firstDigit = Math.floor(value / powerOfTen)
    const newValue = roundUp ? (firstDigit + 1) * powerOfTen : firstDigit * powerOfTen
    return newValue
  }

  const transformedMaxValue = processValue(maxValue, true)
  const transformedMinValue = processValue(minValue, false)
  return [transformedMaxValue, transformedMinValue]
}

/** 获取散点图的 visualMap 配置 */
export function getVisualMapOptions(maxValue: number, minValue: number, dimension: number | string) {
  const transformResult = transformAndRoundValues(maxValue, minValue)
  const isSmallValue = maxValue < SCATTER_LEGEND_PRECISION_BOUND

  const [max, min] = isSmallValue ? [maxValue, minValue] : transformResult
  let precision = 0
  if (isSmallValue) {
    const decimalPart = maxValue.toString().split('.')[1]
    precision = decimalPart ? decimalPart.length : 0
  }
  return [
    {
      left: 'right',
      top: 15,
      dimension: dimension, // 气泡图才有 与气泡大小属于一个通道
      min,
      max,
      itemWidth: 20,
      itemHeight: 80,
      calculable: true,
      precision,
      inRange: {
        symbolSize: [10, 50],
      },
      outOfRange: {
        symbolSize: [10, 50],
        color: ['rgba(255,255,255,0.4)'],
      },
    },
  ]
}

/**
 * 获取 url 上的参数信息
 * @param url 兼容 AskDoc 中链接格式："###askDocPdfLink###?folderId=Xxx&docId=Xxx&hrefPage=3&highlight=Xxx"
 * @returns
 */
export const getParamsFromURL = (url: string) => {
  const queryString = url.split('?')[1]

  if (!queryString) {
    return {}
  }

  const urlParams = new URLSearchParams(queryString)
  const paramsObject = Object.fromEntries(urlParams.entries())

  return paramsObject
}

const antdDeignToken = {
  components: {
    Table: {
      cellPaddingInlineSM: 6,
      cellPaddingBlockSM: 5,
    },
  },
}
/** 返回 Antd ConfigProvider theme 中的配置 */
export function getAntdConfigProviderTheme(theme: ThemeType) {
  const config =
    theme === 'dark'
      ? {
          token: {
            colorBgBase: 'rgb(51 65 85 1)',
            colorTextBase: '#fff',
          },
          ...antdDeignToken,
        }
      : {
          algorithm: antdTheme.defaultAlgorithm,
          ...antdDeignToken,
        }

  return config
}

/**
 * 格式化展示文本，超出展示maxShowNumber则进行阶段省略展示
 * @param text
 * @param maxShowNumber
 *
 */
export function formatShowText(text: string, maxShowNumber?: number) {
  const ellipsis = '...'
  if (typeof text !== 'string') {
    return ''
  }
  if (typeof maxShowNumber !== 'number') {
    return text
  }
  const length = text.length
  if (length > maxShowNumber) {
    return text.slice(0, maxShowNumber) + ellipsis
  }
  return text
}

/**
 * menu菜单 - 根据当前页面路径找到对应的菜单项的 key
 * @param path
 * @param items
 * @returns
 */
export const getKeyFromPath = (path: string, menuItems: MenuItem[]): string | null => {
  const menuItem = menuItems.find((item) => item.path === path)
  if (menuItem) {
    return menuItem.key
  }
  for (const item of menuItems) {
    if (item.children) {
      const foundKey = getKeyFromPath(path, item.children)
      if (foundKey) {
        return foundKey
      }
    }
  }
  return null
}

/**
 * menu菜单 - 根据path寻找上一级的key
 * @param path
 * @param menuItems
 * @returns
 */
export const findParentKey = (path: string, menuItems: MenuItem[]): string | null => {
  for (const item of menuItems) {
    if ((item.children || []).some((childItem) => childItem.path === path)) {
      return item.key // 找到了匹配的路径，返回父级的 key
    }
  }
  return null // 如果未找到匹配的菜单项，返回 null
}

/**
 * 遍历多层结构，并查找匹配的menuItem
 * @param menuItems
 * @param key
 * @returns
 */
export const findMenuItem = (menuItems: MenuItem[], key: string): MenuItem | null => {
  for (const item of menuItems) {
    if (item.key === key) {
      return item
    }
    if (item.children) {
      const found = findMenuItem(item.children, key)
      if (found) {
        return found
      }
    }
  }
  return null
}

export const chatHistoryBoxId = 'chat-history-box'
/** 会话历史页面 滚动到底部 */
export const scrollToBottom = (behavior?: ScrollBehavior) => {
  const divElement = document.getElementById(chatHistoryBoxId)
  if (divElement) {
    const scrollHeight = divElement.scrollHeight
    divElement.scrollTo({
      behavior: behavior ?? 'smooth',
      top: scrollHeight,
    })
  }
}

/**
 * 将path的路径分割，将匹配中的menuItems的key
 */

export function traversePathToGetKeys(path: string, menuItems: MenuItem[]) {
  const pathItems = (path || '').split('/')
  const keys: string[] = []
  pathItems.forEach((pathStr) => {
    const menuItem = menuItems.find((menu) => menu.key === pathStr)
    if (menuItem) {
      keys.push(pathStr)
      menuItems = menuItem.children || []
    }
  })
  return keys
}

// 文档状态
export const docStatusMap: { [key: string]: { label: string; colorClass: string } } = {
  Pending: { label: '异常', colorClass: 'text-orange-500' },
  Ready: { label: '解析中', colorClass: 'text-blue-500' },
  Done: { label: '解析完成', colorClass: 'text-green-500' },
  Fail: { label: '失败', colorClass: 'text-red-500' },
}

export function isAutofocus() {
  return qs.parse(location.search).autofocus === '1'
}
/**
 * 全局的ac管理，便于在一些特殊的场景下（比如登出）终止所有的请求
 */
export const abortControllerManager = new Set<AbortController>()

/**
 * abortController 的hook
 * 用于在组件卸载的时候终止掉请求，防止阻塞页面
 */
export function useAbortController({ debounceTimeout = 200 }: { debounceTimeout?: number } = {}) {
  const abortControllerRef = useRef(new AbortController())
  // 防止react18调用两次useEffect
  // mount -> unmount(abort) -> mount，此时就会被终止
  // 增加一个字段判断触发abort时没有被再次挂载
  const needAbort = useRef(false)
  const abort = useRef(
    debounce((force = false) => {
      if (force || needAbort.current) {
        abortControllerRef.current.abort()
      }
    }, debounceTimeout),
  )
  useEffect(() => {
    const ac = abortControllerRef.current
    const abortFn = abort.current
    function beforeUnload() {
      abortFn(true)
    }
    needAbort.current = false
    window.addEventListener('beforeunload', beforeUnload)
    abortControllerManager.add(ac)
    return () => {
      needAbort.current = true
      window.removeEventListener('beforeunload', beforeUnload)
      abortFn()
      abortControllerManager.delete(ac)
    }
  }, [])
  return abortControllerRef.current
}

/**
 * 如果它没有BASE_URL前缀就加上
 */
export function formatPathWithBaseUrl<T>(path: T): T {
  if (typeof path === 'string' && path.startsWith('/') && !path.startsWith(BASE_URL)) {
    return (BASE_URL + path) as T
  }
  return path
}

export const copyToClipboard = (textToCopy: string) => {
  // navigator clipboard api needs a secure context (https)
  if (navigator.clipboard && window.isSecureContext) {
    // navigator clipboard api method'
    return navigator.clipboard.writeText(textToCopy)
  } else {
    // text area method
    const textArea = document.createElement('textarea')
    textArea.value = textToCopy
    // make the textarea out of viewport
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    return new Promise((res, rej) => {
      // here the magic happens
      document.execCommand('copy') ? res(0) : rej()
      textArea.remove()
    })
  }
}

export function formatFileSize(size: number): string {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}

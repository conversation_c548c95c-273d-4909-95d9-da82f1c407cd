export const BASE_FONTSIZE = 16

export const getRem = (BASE_WIDTH: number) => {
  return Math.ceil((document.documentElement.getBoundingClientRect().width / BASE_WIDTH) * BASE_FONTSIZE)
}

/**
 * 刷新 font-size 以适配当前视口
 * @param zoomFactor
 */
export const refreshRem = (BASE_WIDTH: number) => {
  if (BASE_WIDTH) {
    const rem = getRem(BASE_WIDTH)
    document.documentElement.style.fontSize = `${rem}px`
  }
}

// 处理element-ui内联样式px2rem,全局挂载
export function px2rem(px: number) {
  return parseFloat(String(px)) / BASE_FONTSIZE + 'rem'
}

//找到px转换为rem
export function findPxToRem(text: string) {
  const pattern = /\b\d+px\b/g
  function replacePx(match: string) {
    const value = match.replace('px', '')
    return px2rem(parseInt(value))
  }
  const result = text.replace(pattern, replacePx)
  return result
}

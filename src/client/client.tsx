import React, { useEffect } from 'react'
import ReactDOM from 'react-dom/client'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import '../shared/config'
import { ConfigProvider } from 'antd'
import { IS_DARK } from 'src/shared/constants'
import Router from './Router'
import './client.css'
import './markdown.css'
import { ErrorBoundary, FallbackComponent } from './ErrorBoundary'
import 'react-reflex/styles.css'
import { isIOS } from './utils'

// 设置语言包
dayjs.locale('zh-cn')

window.__APP_VERSION__ = __APP_VERSION__

const App = () => {
  // 用于处理输入框失去焦点事件，解决输入框被弹起下不来的问题
  const handleBlur = (event: any) => {
    if (
      document.documentElement.offsetHeight <= document.documentElement.clientHeight &&
      ['input', 'textarea'].includes(event.target.localName)
    ) {
      document.body.scrollIntoView() // 回到顶部
    }
  }

  useEffect(() => {
    if (isIOS) {
      document.addEventListener('blur', handleBlur, true)
    }

    return () => {
      if (isIOS) {
        document.removeEventListener('blur', handleBlur, true)
      }
    }
  }, [])

  return (
    <React.StrictMode>
      <ErrorBoundary fallbackComponent={FallbackComponent}>
        <ConfigProvider>
          <Router />
        </ConfigProvider>
      </ErrorBoundary>
    </React.StrictMode>
  )
}

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(<App />)

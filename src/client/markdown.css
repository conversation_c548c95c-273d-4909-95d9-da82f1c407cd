/* 此处为markdown的css样式 */
.askbi-markdown {
  a {
    color: #0c1b29;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    position: relative;
    margin-top: 1rem;
    margin-bottom: 1rem;
    font-weight: bold;
    line-height: 1.4;
    cursor: text;
  }

  h1:hover a.anchor,
  h2:hover a.anchor,
  h3:hover a.anchor,
  h4:hover a.anchor,
  h5:hover a.anchor,
  h6:hover a.anchor {
    text-decoration: none;
  }

  h1 tt,
  h1 code {
    font-size: inherit;
  }

  h2 tt,
  h2 code {
    font-size: inherit;
  }

  h3 tt,
  h3 code {
    font-size: inherit;
  }

  h4 tt,
  h4 code {
    font-size: inherit;
  }

  h5 tt,
  h5 code {
    font-size: inherit;
  }

  h6 tt,
  h6 code {
    font-size: inherit;
  }

  h1 {
    font-size: 1.7em;
    line-height: 1.2;
  }

  h2 {
    font-size: 1.4em;
    line-height: 1.225;
  }

  h3 {
    font-size: 1.3em;
    line-height: 1.43;
  }

  h4 {
    font-size: 1.2em;
  }

  h5 {
    font-size: 1em;
  }

  h6 {
    color: #777;
    font-size: 1em;
  }

  li {
    list-style-type: disc;
  }

  p,
  blockquote,
  ul,
  ol,
  dl,
  table {
    margin: 0.8em 0;
  }

  ul,
  ol {
    padding-left: 30px;
  }

  li > ol,
  li > ul {
    margin: 0;
  }

  hr {
    overflow: hßidden;
    box-sizing: content-box;
    height: 2px;
    margin: 16px 0;
    padding: 0;
    border: 0 none;
    background-color: #e7e7e7;
  }

  li p.first {
    display: inline-block;
  }

  ul:first-child,
  ol:first-child {
    margin-top: 0;
  }

  ul:last-child,
  ol:last-child {
    margin-bottom: 0;
  }

  blockquote {
    padding: 0 15px;
    border-left: 4px solid #dfe2e5;
    color: #777;
  }

  blockquote blockquote {
    padding-right: 0;
  }
}

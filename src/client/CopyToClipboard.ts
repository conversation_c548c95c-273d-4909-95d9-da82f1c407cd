/**
 * 用于在复制前，清除掉selection状态，并在复制之后还原之前的选择和焦点状态
 */
const deselectCurrent = () => {
  const selection = document.getSelection()

  if (selection) {
    if (selection.rangeCount === 0) {
      return () => {}
    }

    let activeElement = document.activeElement

    const ranges: Range[] = []
    for (let i = 0; i < selection.rangeCount; i++) {
      ranges.push(selection.getRangeAt(i))
    }

    switch (activeElement?.tagName.toUpperCase()) {
      case 'INPUT':
      case 'TEXTAREA':
        {
          const element = activeElement
          ;(element as HTMLInputElement | HTMLTextAreaElement).blur()
        }
        break
      default:
        activeElement = null
    }

    selection.removeAllRanges()

    return () => {
      if (selection.type === 'Caret') {
        selection.removeAllRanges()
      }

      if (selection.rangeCount === 0) {
        ranges.forEach((range) => {
          selection.addRange(range)
        })
      }

      if (activeElement) {
        const element = activeElement
        ;(element as HTMLInputElement | HTMLTextAreaElement).focus()
      }
    }
  }
  return () => {}
}

const updateMarkStyles = (mark: HTMLSpanElement) => {
  mark.style.all = 'unset'
  mark.style.position = 'fixed'
  mark.style.top = '0'
  mark.style.clip = 'rect(0, 0, 0, 0)'
  mark.style.whiteSpace = 'pre'
  mark.style.userSelect = 'text'
}

export const copyToClipboard = (text: string) => {
  let success = false

  const reselectPrevious = deselectCurrent()
  const range = document.createRange()
  const selection = document.getSelection()
  const mark = document.createElement('span')
  mark.textContent = text

  updateMarkStyles(mark)

  mark.addEventListener('copy', (e) => {
    e.stopPropagation()
  })

  document.body.appendChild(mark)
  range.selectNodeContents(mark)
  selection && selection.addRange(range)

  try {
    success = document.execCommand('copy')

    if (!success) {
      throw new Error('document execCommand function failed')
    }
    success = true
  } catch (error) {
    console.error('copy to clipboard failed', error)
  } finally {
    if (selection) {
      if (selection.removeRange) {
        selection.removeRange(range)
      } else {
        selection.removeAllRanges()
      }
    }
    if (mark) {
      document.body.removeChild(mark)
    }
    reselectPrevious()
  }

  return success
}

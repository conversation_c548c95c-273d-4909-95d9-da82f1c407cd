import html2canvas from 'html2canvas'
import { BlobWithRatio } from 'src/shared/common-types'

/**
 * 获取Blob 为图表导出为图片做准备
 * @param dom 需要被复制的DOM节点
 */
export async function generateBlobForCopy(dom: HTMLDivElement): Promise<string | BlobWithRatio> {
  if (dom == null) {
    return Promise.reject('Dom is null or undefined')
  }
  try {
    const canvas = await html2canvas(dom)
    const blob: Blob | null = await new Promise((resolve) => {
      canvas.toBlob((b) => resolve(b), 'image/png', 1.0)
    })
    const ratio = canvas.width / canvas.height

    if (blob) {
      return Object.assign(blob, { ratio })
    }
    return Promise.reject('Blob does not exist or IS_H5 is true')
  } catch (error) {
    console.error('Error generating blob:', error)
    return Promise.reject(error)
  }
}

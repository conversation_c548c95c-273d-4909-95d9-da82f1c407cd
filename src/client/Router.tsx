/**
 * @description 路由文件
 */
import React from 'react'
import { createBrowserRouter, RouteObject, RouterProvider } from 'react-router-dom'
import { cachedDynamicImportComponent } from 'src/shared/common-utils'

const AskBILayout = cachedDynamicImportComponent(() =>
  import('./AskBILayout').then((mod) => ({ default: mod.AskBILayout })),
)

const Page404 = cachedDynamicImportComponent(() => import('./pages/Page404'))
const LoginError = cachedDynamicImportComponent(() => import('./pages/LoginError'))
import { formatPathWithBaseUrl } from './utils'
import ChatPageWrapper from './pages/ChatPageWrapper/ChatPageWrapper'

/**
 * 递归处理每一个路由，如果它没有BASE_URL前缀，就加上，然后遍历子路由
 */
function formatRoutesWithBaseUrl(routes: RouteObject[]) {
  for (const route of routes) {
    route.path = formatPathWithBaseUrl(route.path)
    formatRoutesWithBaseUrl(route?.children ?? [])
  }
  return routes
}

// const router = createBrowserRouter([
const getRouter = () => {
  return createBrowserRouter(
    formatRoutesWithBaseUrl([
      {
        path: '/',
        element: <AskBILayout />,
        children: [
          {
            path: '/',
            element: <ChatPageWrapper />,
          },
          {
            path: 'chat/:sessionId',
            element: <ChatPageWrapper />,
          },
        ],
      },
      {
        path: '/login-error',
        element: <LoginError />,
      },

      {
        path: '*',
        element: <Page404 />,
      },
    ]),
    {
      future: {
        v7_fetcherPersist: true,
        v7_normalizeFormMethod: true,
        v7_partialHydration: true,
        v7_relativeSplatPath: true,
        v7_skipActionErrorRevalidation: true,
      },
    },
  )
}

export default function Root() {
  const router = getRouter()
  return <RouterProvider router={router} />
}

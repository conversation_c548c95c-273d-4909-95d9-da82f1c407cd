@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  a {
    @apply text-link;
  }

  a:hover {
    color: rgb(var(--color-link));
  }

  .dipeak-light {
    --color-primary: 106, 88, 236; /* #6a58ec in RGB */
    --color-primary-hover: 85, 66, 225; /* #5542e1 in RGB */
    --color-link: 85, 66, 225; /* #5542e1 in RGB */
    --color-border-color: 219, 219, 219; /* #dbdbdb in RGB */
    --color-metric-unselected-border-color: 200, 195, 240; /* #c8c3f0 in RGB */
    --color-metric-selected-border-color: 200, 195, 240; /* #c8c3f0 in RGB */
    --color-metric-unselected-bg-color: 243, 242, 251; /* #F3F2FB in RGB */
    --color-metric-selected-bg-color: 200, 195, 240; /* #F3F2FB in RGB */
    --color-dimension-unselected-border-color: 168, 222, 209; /* #a8ded1 in RGB */
    --color-dimension-selected-border-color: 139, 236, 212; /* #8becd4 in RGB */
    --color-dimension-unselected-bg-color: 247,255,253; /* #F7FFFD in RGB */
    --color-dimension-selected-bg-color: 139, 236, 212; /* #8becd4 in RGB */
    --radius: 8px;
  }

  .baowu-light {
    --color-primary: 44, 106, 221; /* #2c6add in RGB */
    --color-primary-hover: 44, 106, 221; /* #2c6add in RGB */
    --color-link: 44, 106, 221; /* #2c6add in RGB */
    --color-border-color: 240, 247, 255; /* #f0f7ff in RGB */
    --color-metric-unselected-border-color: 209, 221, 251; /* #D1DDFB in RGB */
    --color-metric-selected-border-color: 202, 218, 249; /* #CADAF9 in RGB */
    --color-metric-unselected-bg-color: 245, 248, 255; /* #F5F8FF in RGB */
    --color-metric-selected-bg-color: 202, 218, 249; /* #CADAF9 in RGB */
    --color-dimension-unselected-border-color: 168, 222, 209; /* #a8ded1 in RGB */
    --color-dimension-selected-border-color: 139, 236, 212; /* #8becd4 in RGB */
    --color-dimension-unselected-bg-color: 247, 255, 253; /* #F7FFFD in RGB */
    --color-dimension-selected-bg-color: 139, 236, 212; /* #8becd4 in RGB */
    --radius: 4px;
  }
}

*,
::after,
::before {
  box-sizing: border-box;
}

html.dark {
  color-scheme: dark;
}

#root {
  width: 100%;
}

:root {
  font-weight: 400;
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-synthesis: none;
  text-rendering: optimizelegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  border-radius: 3px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: hsl(0deg 0% 0% / 0.1);
}

html.dark ::-webkit-scrollbar-thumb {
  background-color: hsl(0deg 0% 100% / 0.2);
}

html.dark .ant-table,
html.dark .ant-table-thead > tr > th {
  border-radius: 0;
}

html.dark .ant-tooltip {
  --antd-arrow-background-color: rgb(51 65 85);
}

html.dark .ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: rgb(30 41 59 / 0.5);
}

.dark .ant-tooltip-inner {
  background-color: rgb(51 65 85 / 1);
}

.ant-btn-primary {
  background-color: rgb(var(--color-primary));
  box-shadow: none;
}

html.dark .ant-btn-primary {
  box-shadow: 1px 1px 4px 0 #334155;
}

button.ant-switch {
  background-color: rgb(0 0 0 / 0.25);
}

/* tabs 组件内 icon 和文字不要换行的样式 */
.ant-tabs .ant-tabs-nav .ant-tabs-tab-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.ant-btn.ant-btn-sm {
  font-size: 12px;
}

.ant-spin .ant-spin-dot-item {
  background-color: rgb(var(--color-primary));
}

.docx-wrapper {
  background-color: #fff !important;
}

/* 设置归因分析的minimap */
.g6-minimap {
  position: absolute;
  right: 0;
  bottom: 4px;
  border: 0.5px solid #aaa;
}

.grey-modal-footer .ant-modal-content {
  padding: 16px 0 0;
  border-radius: 4px;
}

/* 设置消息提示弹窗距离 - 防止盖住header */
.ant-message-top {
  margin-top: 70px !important; /* 设置距离顶部 100px */
}

.special-label-width-full label {
  width: 100%;
}

.special-label-width-full label::after {
  width: 0 !important;
  margin: 0 !important;
}

.x-spreadsheet-sheet {
  width: 100% !important;
}

.x-spreadsheet-table {
  width: 100% !important;
}

.x-spreadsheet-overlayer {
  width: 100% !important;
}

.ant-list-item .ant-list-item-action{
  margin-inline-start:0 !important
}

.ant-list-item{
  border-block-end: none !important
}
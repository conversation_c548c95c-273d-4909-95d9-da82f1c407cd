import React, {
  useRef,
  useCallback,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useLayoutEffect,
  useState,
} from 'react'
import { useAtom, useAtomValue, useSetAtom } from 'jotai/react'
import clsx from 'clsx'
import qs from 'query-string'
import { JsonContentItem, STRUCTURED_MESSAGE_DATA_TYPE } from 'src/shared/common-types'
import {
  agentStructuredMessageAtom,
  flowAtom,
  latestSelectedMessageAtom,
  messageInputEditorRefAtom,
  updateMessageAndHtmlAtom,
} from 'src/client/pages/AIAtoms'
import './MessageInputEditable.css'
import useSubmitAsk from 'src/client/hooks/useSubmitAsk'

type Props = React.AllHTMLAttributes<HTMLElement> & {
  maxLength?: number
  isMiniMode: boolean
  onFocus?: (event: React.FocusEvent<HTMLElement>) => void
  onBlur?: (event: React.FocusEvent<HTMLElement>) => void
  onKeyDown: (e: React.KeyboardEvent<Element>) => void
}

interface ElementNode extends ChildNode {
  data?: string
  innerText?: string
  attributes?: { name: string; value: string }[]
  dataset?: { [key: string]: string }
}

const keyBlackList = ['tagName', 'content', 'type', 'children', 'class']
const genAttributes = (node: JsonContentItem) => {
  let res = ''
  for (const key in node) {
    if (keyBlackList.includes(key)) {
      continue
    } else {
      res += `${key}='${node[key]}' `
    }
  }
  return res
}

const invisibleChar = '\ufeff'

export const convertToHtml = (json: JsonContentItem[] | string) => {
  if (json) {
    if (typeof json === 'string') {
      return json
    }
    let result = ''
    json.forEach((item) => {
      let res = ''
      if (item.type === '#text') {
        result += item['data-content']
        return
      }
      if (item['data-type'] !== STRUCTURED_MESSAGE_DATA_TYPE.TEXT) {
        res = `<span class='${clsx('inline underline px-1 underline-offset-4 decoration leading-6')}' ${genAttributes(item) || ''} contenteditable='false'>${item['data-content']}</span>${invisibleChar}`
      } else {
        res = `<span ${genAttributes(item) || ''}>${item['data-content']}</span>`
      }
      result += res
    })
    return result ? result : ''
  }
  return json
}

function convertToJson(node: ElementNode) {
  // 默认data-type是text
  const obj: JsonContentItem = {
    'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT,
  }
  obj.type = node.nodeName
  if (!node.data && !node.innerText) {
    return
  }
  if (node.data && node.data === invisibleChar) {
    // 碰到特定字符时不转化为json, 保证删除字符时操作正常
    return
  }
  if (node.nodeType === 3) {
    if (node.nodeName === '#text') {
      obj['data-content'] = node.data
    }
    return obj
  }
  if (node.attributes) {
    // 添加属性
    for (let i = 0; i < node.attributes.length; i++) {
      const attr = node.attributes[i]
      obj[attr.name] = attr.value
    }
  }
  if (node.dataset) {
    // 添加数据
    for (const key in node.dataset) {
      const value = node.dataset[key]
      obj['data-' + key] = value
    }
  }
  obj['data-content'] = node.innerText

  if (!obj['data-type']) {
    obj['data-type'] = STRUCTURED_MESSAGE_DATA_TYPE.TEXT
  }
  // 添加子元素
  obj.children = []
  for (let i = 0; i < node.childNodes.length; i++) {
    const child = node.childNodes[i]
    if (child.nodeType === Node.ELEMENT_NODE) {
      const item = convertToJson(child)
      if (item) {
        obj.children.push(item)
      }
    }
  }

  return obj
}

const getJson = (element: HTMLDivElement | null) => {
  if (element) {
    const htmlString = element.innerHTML
    const parser = new DOMParser()
    const doc = parser.parseFromString(htmlString, 'text/html')

    // filter后jsonArr不会有undefined
    const jsonArr = Array.from(doc.body?.childNodes).map?.(convertToJson).filter(Boolean) as JsonContentItem[]
    return jsonArr
  }
  return []
}

export default forwardRef(function MessageInputEditable(
  { maxLength = 0, onFocus, onBlur, onKeyDown, isMiniMode, ...props }: Props,
  ref,
) {
  const [contents, setContents] = useAtom(agentStructuredMessageAtom)
  const updateMessageAndHtml = useSetAtom(updateMessageAndHtmlAtom)
  const setMessageInputEditorRefAtom = useSetAtom(messageInputEditorRefAtom)
  const setLatestSelectedMessage = useSetAtom(latestSelectedMessageAtom)

  const flowData = useAtomValue(flowAtom)
  const [onSubmit] = useSubmitAsk({ isMiniMode })
  const [asked, setAsked] = useState(false)
  const [focused, setFocused] = useState(false)

  const editableRef = useRef<HTMLDivElement>(null)

  useImperativeHandle(ref, () => ({
    focus: () => {
      editableRef.current?.focus()
    },
    blur: () => {
      editableRef.current?.blur()
    },
  }))

  // 输入时更新json
  const updateJson = () => {
    const newContents = getJson(editableRef.current)
    setContents(newContents)
    updateCursorPosition()
  }

  useEffect(() => {
    const pasteHandler = (event: ClipboardEvent) => {
      // 阻止默认的粘贴行为
      event.preventDefault()

      // 获取粘贴的文本内容
      const text = event?.clipboardData?.getData('text/plain')
      // 插入文本内容
      document.execCommand('insertText', false, text)
    }

    const editableElement = editableRef.current
    if (editableElement) {
      editableElement.addEventListener('paste', pasteHandler)
    }

    return () => {
      if (editableElement) {
        editableElement.removeEventListener('paste', pasteHandler)
      }
    }
  }, [editableRef])

  const handleInput = () => {
    // 获取最新输入的内容
    const textContent = editableRef.current?.textContent
    if (textContent && textContent.length > maxLength) {
      editableRef.current.textContent = textContent.slice(0, maxLength)
      updateJson()
      updateCursorPosition()
      return
    }

    if (editableRef.current) {
      const children = Array.from(editableRef.current.childNodes)
      children.forEach((child) => {
        if (child.nodeName === 'DIV') {
          const fragment = document.createDocumentFragment()
          while (child.firstChild) {
            fragment.appendChild(child.firstChild)
          }
          child.replaceWith(fragment)
        }
      })
    }

    // 只在输入为空时才将isInputting置为false, 而不是在structuredMessageAtom里处理, 因为点击热门指标时仍需显示热门指标标签
    // setIsInputting(bool)

    // 手动输入内容后就把选择过得数据去掉
    setLatestSelectedMessage({})
    updateJson()
  }

  const updateCursorPosition = useCallback(() => {
    if (editableRef.current) {
      const selection = window.getSelection()
      if (selection) {
        const range = document.createRange()
        const lastChild = editableRef.current.lastChild
        if (lastChild) {
          if (lastChild.nodeName === 'BR') {
            range.setStartAfter(lastChild)
            range.setEndAfter(lastChild)
          } else if (lastChild.nodeType === Node.TEXT_NODE) {
            range.setStart(lastChild, lastChild.textContent?.length || 0)
            range.setEnd(lastChild, lastChild.textContent?.length || 0)
          } else {
            range.selectNodeContents(editableRef.current)
            range.collapse(false)
          }
        } else {
          range.selectNodeContents(editableRef.current)
          range.collapse(false)
        }
        selection.removeAllRanges()
        selection.addRange(range)
      }
    }
  }, [editableRef])

  useLayoutEffect(() => {
    const { autofocus, ask }: { autofocus?: string; ask?: string } = qs.parse(location.search)
    if (editableRef.current) {
      if (ask) {
        if (flowData && !asked) {
          setAsked(true)
          setTimeout(() => {
            // 前置的参数太多了, 加个定时器等参数设置完再请求吧
            onSubmit(ask)
          }, 0)
        }
        return
      }
      if (autofocus === '1' && !focused) {
        setFocused(true)
        editableRef.current.focus()
      }
    }
  }, [editableRef, flowData, asked, focused, onSubmit])

  const resetHtml = useCallback(
    (data: JsonContentItem[] | string) => {
      const html = convertToHtml(data)
      if (editableRef.current) {
        editableRef.current.innerHTML = html
      }
    },
    [editableRef],
  )

  const handleFocus = (e: React.FocusEvent<HTMLElement>) => {
    onFocus?.(e)
    updateCursorPosition()
  }

  const handleBlur = (e: React.FocusEvent<HTMLElement>) => {
    onBlur?.(e)
    resetHtml(contents)
  }

  useEffect(() => {
    setMessageInputEditorRefAtom({
      updateCursorPosition,
      setHtml: resetHtml,
    })

    // 清除引用
    return () => {
      setMessageInputEditorRefAtom({ setHtml: () => {} })
    }
  }, [editableRef, setMessageInputEditorRefAtom, resetHtml, updateCursorPosition])

  const handleDelete = (e: React.KeyboardEvent<Element>) => {
    const text = editableRef.current?.innerText
    const isAtEnd = checkCursorPosition(text)
    // 光标在最后时,再去判断 要删除的字符是不是预期的不可见字符
    if (isAtEnd && text) {
      const lastChar = text.slice(-1)
      if (lastChar === invisibleChar) {
        // 碰到\ufeff字符时,直接删除最后一个json对象(能确定为指标/维度区块) ,防止删除字符后, 不可编辑元素光标显示异常,
        // 暂时不处理中间的指标删除情况, 比较麻烦
        e.preventDefault()
        const newContents = contents.slice(0, contents.length - 1)
        updateMessageAndHtml(newContents)
      }
    }
  }

  function checkCursorPosition(text?: string) {
    let caretOffset = 0
    let isAtEnd = true
    if (!text) {
      return isAtEnd
    }
    const target = editableRef.current as HTMLInputElement
    const range = window.getSelection()?.getRangeAt(0)
    if (range) {
      // 克隆一个选中区域
      const preCaretRange = range.cloneRange()
      // 设置选中区域的节点内容为当前节点
      preCaretRange.selectNodeContents(target)
      // 重置选中区域的结束位置
      preCaretRange.setEnd(range.endContainer, range.endOffset)
      caretOffset = preCaretRange.toString().length
      isAtEnd = text.length === caretOffset
    }
    return isAtEnd
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' && e.shiftKey) {
      e.preventDefault()
      e.stopPropagation()
      const selection = window.getSelection()
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        range.deleteContents() // 删除选中的内容（如果有）

        // 插入 <br> 标签
        const br = document.createElement('br')
        range.insertNode(br)

        // 在 <br> 后插入零宽字符（\u200B），确保光标可以定位
        const zeroWidthSpace = document.createTextNode('\u200B')
        range.insertNode(zeroWidthSpace)

        // 将光标移到零宽字符后
        range.setStartAfter(zeroWidthSpace)
        range.setEndAfter(zeroWidthSpace)
        selection.removeAllRanges()
        selection.addRange(range)
      }
      updateJson()
      console.info('Shift+Enter pressed')
    } else if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      onKeyDown?.(e)
    } else if (e.key === 'Backspace') {
      handleDelete(e)
    }
  }

  // 修正 updateJson，确保更新光标

  const handleClick = () => {
    setTimeout(() => {
      editableRef.current?.focus()
    }, 0)
  }

  return (
    <div className={clsx('message-input-editable max-w-full py-1')}>
      <div
        ref={editableRef}
        data-placeholder="Start asking questions to the AI ..."
        suppressContentEditableWarning
        onInput={handleInput}
        className="placeholder relative min-h-[16px] break-all py-1.5 leading-[16px] outline-none"
        contentEditable
        tabIndex={0}
        data-tap-disabled="false"
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        onClick={handleClick}
        {...props}
      />
    </div>
  )
})

.admin-sidebar-menu{
  .ant-menu-inline{
    padding-left: 0 !important;
    background-color: #F8F8FC !important;
    border-inline-end: none !important;
  }

  .ant-menu-vertical{
    border-inline-end: none !important;
  }

  .ant-menu-submenu-title{
    height: 56px !important;
    padding-left: 0 !important;
    border-radius: 0 !important;
    color: #171717 !important;
  }

  .ant-menu-submenu-title:hover{
    background-color: #EBE4F4 !important;
  }

  .ant-menu-item{
    height: 56px !important;
    padding-left: 0 !important;
    border-radius: 0 !important;
  }

  .ant-menu-inline .ant-menu-item{
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .ant-menu-sub .ant-menu-item{
    padding-left: 42px !important;
    border-radius: 0 !important;
  }

  .ant-menu-submenu{
    border-radius: 0 !important;
  }

  .ant-menu-submenu-active{
    border-radius: 0 !important;
  }

  .ant-menu-light .ant-menu-item-selected{
    border-radius: 2px !important;
    background-color: #EBE4F4 !important;
    color: #171717 !important;
    font-weight: 600 !important;
  }

  .ant-menu-vertical .ant-menu-item{
    margin-block: 0;
    margin-right: 4px !important;
    padding-top: 12px !important;
  }

  .ant-menu-item-icon +span{
    margin-inline-start: 6px !important;
  }

  .ant-menu-item-icon{
    margin-left: 10px !important;
  }

  .ant-menu-item:hover {
    border-radius: 0 !important;
    background-color: #EBE4F4 !important;
  }

  .ant-menu-submenu-title:active{
    background-color: #EBE4F4 !important;
  }

  .ant-menu-inline .ant-menu-submenu-title{
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .ant-menu-light{
    background-color: #F8F8FC !important;
  }

  .ant-menu-submenu-arrow{
    color: #858585 !important;
  }

  .ant-menu-item-group-title{
    padding-top: 24px;
    padding-left: 14px;
    color: #4E5969;
    font-size: 13px;
  }

  .ant-menu-submenu-vertical .ant-menu-submenu-title .ant-menu-item-icon{
    padding-top: 28px;
  }
}

html.dark .admin-sidebar-menu {
  .ant-menu-inline {
    background-color: rgb(30 41 59) !important;
  }

  .ant-menu-item-icon {
    color: white !important;
  }

  .ant-menu-dark .ant-menu-item-selected,
  .ant-menu-item:hover {
    background-color: rgb(15 23 42) !important;
  }

  .ant-menu-title-content {
    color: white;
  }
}

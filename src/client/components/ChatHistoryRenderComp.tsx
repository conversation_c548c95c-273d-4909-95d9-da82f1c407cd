/**
 * @description 聊天历史中的一次聊天记录，包含一问一答
 */
import React from 'react'
import { SyncOutlined } from '@ant-design/icons'
import Markdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeSanitize from 'rehype-sanitize'
import '/src/client/markdown.css'
import { AssistantTextChatItem, ChatStatus, AssistantChitchat, AnsChatItem } from '@shared/common-types'
import { assertExhaustive } from '@shared/common-utils'

const renderChatAnsHelloTextItem = (text: string, idx: number) => {
  return (
    <div key={idx} className="flex w-full flex-col">
      {/* <React.Fragment key={idx}>{text}</React.Fragment> */}
      <Markdown
        key={idx}
        className="askbi-markdown prose prose-sm max-w-full px-2 py-1 leading-[2] text-gray-800"
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeSanitize]}
      >
        {text}
      </Markdown>
    </div>
  )
}

const renderDocMarkdownItem = (text: string, idx: number) => {
  // 清理多余的换行符和反斜杠
  const cleanedText = text.replace(/\\\n/g, '\n').replace(/\n{3,}/g, '\n\n')
  return (
    <div key={idx} className="flex w-full flex-col">
      <Markdown
        className="askbi-markdown prose prose-sm max-w-full px-2 py-1 leading-[2] text-gray-800"
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeSanitize]}
      >
        {cleanedText}
      </Markdown>
    </div>
  )
}

// 用于普通text展示
const renderChatAnsTextItem = (
  chatAnsItem: AnsChatItem | AnsChatItem,
  content: AssistantChitchat | AssistantTextChatItem,
  idx: number,
) => {
  const { text } = content
  const lines = text.split('\n')
  switch (chatAnsItem.status) {
    case ChatStatus.success:
      return (
        <div key={idx}>
          {lines.map((line, index) => (
            <React.Fragment key={index}>
              {line}
              <br />
            </React.Fragment>
          ))}
        </div>
      )
    case ChatStatus.pending:
      return (
        <div key={idx} className="flex">
          {text}
          <SyncOutlined spin className="ml-4 flex flex-none text-xl text-gray-400 dark:text-gray-500" />
        </div>
      )
    case ChatStatus.failure:
      return (
        <div key={idx} className="text-red-400 dark:text-red-500">
          {text}
        </div>
      )
    default:
      return assertExhaustive(chatAnsItem.status)
  }
}

export { renderChatAnsTextItem, renderChatAnsHelloTextItem, renderDocMarkdownItem }

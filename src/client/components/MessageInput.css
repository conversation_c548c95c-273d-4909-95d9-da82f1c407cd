/* 
 * 纯 css 实现 textarea 根据内容自动伸缩高度的效果
 * 参考 https://css-tricks.com/the-cleanest-trick-for-autogrowing-textareas/
 */
.message-input .textarea-wrapper {
  display: grid;

}

.message-input .textarea-wrapper::after {
  content: attr(data-replicated-value) ' ';
  white-space: pre-wrap;
  visibility: hidden;
  overflow: hidden;
}

.message-input .textarea-wrapper > textarea::placeholder {
  font-size: 14px;
}

.message-input .textarea-wrapper > textarea {
  overflow: hidden auto;
}

.message-input .textarea-wrapper > textarea,
.message-input .textarea-wrapper::after {
  grid-area: 1 / 1 / 2 / 2;
  background-color: transparent;
  font-size: 1rem;
  line-height: 1.25rem;
  border: none;
  padding: 0;
  margin: 0;
  max-height: 5rem;
}

.safe-area-inset-bottom {
  height: env(safe-area-inset-bottom);
}

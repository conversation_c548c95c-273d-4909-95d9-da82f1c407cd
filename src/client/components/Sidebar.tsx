import React, { useState, useRef, useEffect } from 'react'
import { Menu, Dropdown, Modal, Input, Spin, message } from 'antd'
import { EllipsisOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { useNavigate, useParams } from 'react-router-dom'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { useInfiniteScroll } from 'ahooks'
import axios from 'axios'
import dayjs from 'dayjs'
import { chatAIApiUrls } from 'src/shared/url-map'
import { SessionItemType } from 'src/shared/common-types'
import { currentSessionIdAtom, isSidebarOpenAtom } from '../pages/AIAtoms'
import { CollapsedIcon, SparkleIcon, SvgIcon } from './SvgIcon'
import { useInitChats } from '../hooks/useInitChats'

interface ChatItemProps {
  chat: SessionItemType
  onDeleted?: () => void //  添加回调
}

// ChatItem with antd components
const ChatItem = (props: ChatItemProps) => {
  const { chat } = props
  const navigate = useNavigate()
  const [isRenaming, setIsRenaming] = useState(false)
  const [value, setValue] = useState<string>('')
  const [isDeleting, setIsDeleting] = useState(false)
  const inputRef = useRef<any>(null)
  const { sessionId } = useParams()
  const setCurrentSessionId = useSetAtom(currentSessionIdAtom)

  useEffect(() => {
    if (isRenaming) {
      inputRef.current?.focus()
      inputRef.current?.select()
    }
  }, [isRenaming])

  useEffect(() => {
    sessionId && setCurrentSessionId(sessionId)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionId])

  const handleRename = () => setIsRenaming(true)
  const submitRename = async () => {
    // if (value && value.trim() && value.trim() !== chat.title) {

    // }
    // setIsRenaming(false)
    message.warning('Rename feature is not implemented yet.')
  }
  const confirmDelete = () => {
    Modal.confirm({
      title: 'Delete chat?',
      content: `This will permanently delete. Proceed?`,
      okType: 'danger',
      okText: 'Confirm',
      cancelText: 'Cancel',
      onOk: async () => {
        setIsDeleting(true)
        const response = await axios.delete(chatAIApiUrls.chat.deleteSessionById(chat.session_id))
        console.info('response', response.data)
        setIsDeleting(false)
        props.onDeleted?.() // 删除成功后刷新列表
        message.success('Chat deleted successfully.')
      },
    })
  }

  const menu = (
    <Menu>
      {/* <Menu.Item key="rename" icon={<EditOutlined />} onClick={handleRename}>
        Rename
      </Menu.Item> */}
      <Menu.Item key="delete" icon={<DeleteOutlined />} danger onClick={confirmDelete}>
        Delete
      </Menu.Item>
    </Menu>
  )

  const isSelected = chat.session_id === sessionId

  const slideItemClick = () => {
    // navigate(`/chat/${chat.session_id}`)
    // setCurrentSessionId(chat.session_id)
    message.warning('Feature is not implemented yet.')
  }

  return (
    <div
      className={`mx-2 my-1 flex items-center justify-between rounded px-3 py-2 text-sm transition-colors ${
        isSelected ? 'bg-blue-500' : 'hover:bg-gray-300'
      }`}
    >
      {isRenaming ? (
        <Input
          ref={inputRef}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onBlur={submitRename}
          onPressEnter={submitRename}
          onKeyDown={(e) => e.key === 'Escape' && setIsRenaming(false)}
          style={{ maxWidth: '90%' }}
        />
      ) : (
        <span
          onClick={slideItemClick}
          className="inline-block max-w-[200px] cursor-pointer overflow-hidden text-ellipsis whitespace-nowrap"
        >
          {dayjs(chat.created_at).format('YYYY-MM-DD HH:mm:ss')}
        </span>
      )}

      <Dropdown overlay={menu} trigger={['click']}>
        <EllipsisOutlined className="cursor-pointer opacity-70" />
      </Dropdown>
    </div>
  )
}

const PAGE_SIZE = 50

// Sidebar component
const Sidebar = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useAtom(isSidebarOpenAtom)
  const containerRef = useRef<HTMLDivElement>(null)
  const { initChatSessions } = useInitChats()

  const {
    data: sessionList,
    noMore,
    loadingMore,
    reload,
  } = useInfiniteScroll(
    async (d) => {
      const response = await axios.get(chatAIApiUrls.chat.getSessionList, {
        params: {
          page: d ? 1 + d.list.length / PAGE_SIZE : 1,
          size: PAGE_SIZE,
        },
      })
      const { items, total } = response.data.data
      return {
        list: items,
        total,
      }
    },
    {
      target: containerRef.current,
      isNoMore: (d) => !!(d && d.list.length >= d.total),
      reloadDeps: [isSidebarOpen],
    },
  )

  const handleNewChat = () => {
    initChatSessions()
  }

  return (
    <div
      className={`fixed left-0 top-0 z-10 flex h-full flex-col gap-y-8 overflow-hidden border-r border-r-[#EBEBEB] bg-[#F6F6F6] pb-4 shadow-[1px_0px_5px_0px_#00000014] transition-all duration-300 ${
        isSidebarOpen ? 'block w-[244px]' : 'w-0'
      }`}
    >
      <div className="mx-4 mt-[22px] flex items-center justify-between whitespace-nowrap">
        <SvgIcon icon={SparkleIcon} className={''} />

        <div onClick={() => setIsSidebarOpen((prev) => !prev)} className="cursor-pointer">
          <SvgIcon icon={CollapsedIcon} className={''} />
        </div>
      </div>

      <div className="flex w-full flex-col">
        <div className="flex w-full flex-col gap-y-4 px-4">
          <p className="text-[#888888]">Navigation</p>
          <button className="flex items-center gap-x-2 whitespace-nowrap px-1" onClick={() => handleNewChat()}>
            New chat
          </button>
        </div>
      </div>

      <div className="h-full">
        <p className="px-4 text-gray-400">Chat</p>
        <div ref={containerRef} style={{ overflowY: 'auto', height: 'calc(100% - 220px)' }}>
          {sessionList?.list.map((item: SessionItemType) => (
            <ChatItem key={item.session_id} chat={item} onDeleted={reload} />
          ))}
          <div className="mt-3 text-center">
            {loadingMore && <Spin />}
            {noMore && sessionList && sessionList?.list.length > 10 && <span className="text-xs">All data loaded</span>}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Sidebar

'use client'
import React from 'react'
import { Tooltip, Avatar } from 'antd'
import { useAtom } from 'jotai'
import { isSidebarOpenAtom } from '../pages/AIAtoms'
import { CollapsedIcon, SparkleIcon, SvgIcon } from './SvgIcon'

const Navbar = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useAtom(isSidebarOpenAtom)

  return (
    <nav
      style={{
        height: 64,
        minHeight: 64,
        borderBottom: '1px solid #EBEBEB',
        paddingLeft: 36,
        paddingRight: 36,
      }}
      className="flex w-full items-center justify-between"
    >
      {/* 左侧图标区 */}
      <div className="flex items-center gap-x-6">
        {!isSidebarOpen ? (
          <div
            className="flex items-center gap-x-6 opacity-100 transition-all duration-300 ease-in-out"
            style={{ width: 'auto' }}
          >
            <Tooltip title="Expand Sidebar">
              <div onClick={() => setIsSidebarOpen((prev: any) => !prev)} className="cursor-pointer">
                <SvgIcon icon={CollapsedIcon} className="" />
              </div>
            </Tooltip>
          </div>
        ) : null}

        <div
          className="flex cursor-pointer items-center gap-x-1 font-medium text-black"
          onClick={() => {
            location.href = '/'
          }}
        >
          <SvgIcon icon={SparkleIcon} className="" />
          <span>Concierge</span>
        </div>
      </div>

      {/* 右侧用户区 */}
      <div className="flex items-center gap-x-2.5">
        <span className="text-black">Admin</span>
        <Avatar size={40} src="#" style={{ backgroundColor: '#000', color: '#fff', fontWeight: 500 }}>
          AD
        </Avatar>
      </div>
    </nav>
  )
}

export default Navbar

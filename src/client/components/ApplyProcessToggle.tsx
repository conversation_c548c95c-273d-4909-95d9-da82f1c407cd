/**
 * @description 申请流程切换组件
 */
import { AppstoreOutlined, ExclamationCircleFilled } from '@ant-design/icons'
import { Dropdown, MenuProps, Modal } from 'antd'
import clsx from 'clsx'
import React, { useEffect } from 'react'
import { useAtom, useAtomValue } from 'jotai'
import { FlowType } from '@shared/common-types'
import { SvgIcon, newLlmIcon } from '@components/SvgIcon'
import './ApplyProcessToggle.css'
import { isSubmittingAtom, flowAtom, flowsDataAtom } from '../pages/AIAtoms'
import { useInitChats } from '../hooks/useInitChats'

interface Props {
  /** 切换模型的时候，是否弹窗确认框 */
  needConfirm: boolean
  /** 显示模型简称 移动端不需要展示模型简称*/
  showLlmAbbrName?: boolean
}

function ApplyProcessToggle({ needConfirm }: Props) {
  const [flow, setFlow] = useAtom(flowAtom)
  const flowsData = useAtomValue(flowsDataAtom)
  const [_isSubmitting, setIsSubmitting] = useAtom(isSubmittingAtom)

  const { getFlows, initChatSessions } = useInitChats()

  useEffect(() => {
    getFlows()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleFlowChange = (data: FlowType) => {
    if (!needConfirm) {
      setFlow(data)
      initChatSessions(data.flow_name)
      setIsSubmitting(false)
    } else {
      Modal.confirm({
        title: `Switching the application process will clear the current chat history. Are you sure?`,
        icon: <ExclamationCircleFilled />,
        okText: 'Confirm',
        cancelText: 'Cancel',
        centered: true,
        className: 'llm-toggle-confirm bg-white dark:bg-slate-900 dark:text-slate-100',
        onOk() {
          initChatSessions(data.flow_name)
          setFlow(data)
          setIsSubmitting(false)
        },
      })
    }
  }

  const items: MenuProps['items'] = (flowsData || []).map((o, index) => {
    const selected = flow?.flow_name === o.flow_name
    return {
      key: index,
      label: (
        <div
          className={clsx(
            'flex cursor-pointer items-center px-2 py-1',
            selected && 'text-sky-500',
            'hover:bg-slate-50 hover:dark:bg-slate-600/30',
          )}
          onClick={() => {
            handleFlowChange(o)
          }}
        >
          <AppstoreOutlined className="flex h-6 w-6 flex-none text-xl dark:text-gray-500" />
          {o.flow_name}
        </div>
      ),
    }
  })

  const renderCurrentFlow = () => {
    return (
      <div className="flex shrink-0 flex-row items-center gap-2 text-xl dark:text-gray-500">
        <SvgIcon icon={newLlmIcon} className="flex h-6 w-6 flex-none text-xl dark:text-gray-500" />
        <div className="whitespace-no-wrap cursor-pointer text-xs dark:border-gray-500 md:text-sm">
          {flow?.flow_name}
        </div>
      </div>
    )
  }

  return (
    <>
      {flowsData.length === 1 ? (
        renderCurrentFlow()
      ) : (
        <Dropdown menu={{ items }} placement="bottom">
          <div className="flex items-center">{renderCurrentFlow()}</div>
        </Dropdown>
      )}
    </>
  )
}

export default ApplyProcessToggle

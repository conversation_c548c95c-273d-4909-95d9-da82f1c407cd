/**
 * @description 消息输入框
 */
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react'
import { CloseOutlined, DeleteOutlined, SyncOutlined } from '@ant-design/icons'
import { Tooltip, UploadFile } from 'antd'
import clsx from 'clsx'
import { use<PERSON>tom, useSet<PERSON>tom } from 'jotai'
import { useNavigate } from 'react-router-dom'
import { nanoid } from 'nanoid'
import { chatAIApiUrls } from 'src/shared/url-map'
import { Chat, ChatStatus, UploadedFileResultType } from 'src/shared/common-types'
import {
  agentMessage<PERSON>tom,
  appendChatAtom,
  selectFileAtom,
  updateChatAnsAtom,
  updateMessageAndHtmlAtom,
} from '../pages/AIAtoms'
import { IS_H5, DEFAULT_INPUT_MESSAGE_LENGTH } from '../../shared/constants'
import { isAndroid } from '../utils'
import { SvgIcon, h5SendMessageIcon, sendMessageIcon } from './SvgIcon'
import './MessageInput.css'
import MessageInputEditable from './MessageInputEditable/MessageInputEditable'
import ApplyProcessToggle from './ApplyProcessToggle'
import FileUploader, { FileUploaderRef } from './FileUploader'
import UploadFileList from './UploadFileList'

let recognition: SpeechRecognition | null
const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
// 语音识别只支持ios
const supportSpeechRecognition = isAndroid ? false : SpeechRecognition != null
if (supportSpeechRecognition) {
  recognition = new SpeechRecognition()
  recognition.lang = 'zh-CN'
  recognition.interimResults = true
  recognition.continuous = true
} else {
  recognition = null
}

interface MessageInputProps {
  // message: string | JsonContentItem[]
  /** 是否为第一个消息，如果是的时候，切换模型不需要确认 */
  isFirstMessage: boolean
  isSubmitting: boolean
  /** 是否支持 LlmToggle */
  enableLlmToggle: boolean
  /** 目前首页 isMiniMode=true，聊天页 isMiniMode=false */
  isMiniMode: boolean
  defaultPlaceholder?: string
  pressEnterToSubmit: (e: React.KeyboardEvent<Element>) => void
  onSubmit: (message?: string) => void
}

const MessageInput = forwardRef<{ focus: () => void }, MessageInputProps>(function MessageInput(
  { isMiniMode, isFirstMessage, isSubmitting, pressEnterToSubmit, onSubmit },
  ref,
) {
  const [message] = useAtom(agentMessageAtom)
  const setMessage = useSetAtom(updateMessageAndHtmlAtom)
  const uploaderRef = useRef<FileUploaderRef>(null)
  const [selectFile, setSelectFile] = useAtom(selectFileAtom)

  const inputRef = useRef<{ focus: () => void; blur: () => void }>(null)

  useImperativeHandle(ref, () => ({
    focus: () => {
      inputRef.current?.focus()
    },
    blur: () => {
      inputRef.current?.blur()
    },
  }))

  // 提交按钮
  const renderSubmit = (
    <>
      {isSubmitting ? (
        <SyncOutlined spin className="flex flex-none text-xl text-black dark:text-gray-600" />
      ) : (
        <Tooltip placement="top" title="发送">
          <div
            className="flex h-9 w-9 shrink-0 cursor-pointer items-center justify-center rounded-full bg-black"
            onClick={(e) => {
              e && e.preventDefault()
              inputRef.current?.blur()
              onSubmit()
            }}
          >
            <SvgIcon
              icon={IS_H5 ? h5SendMessageIcon : sendMessageIcon}
              className="h-5 w-5 text-white dark:text-slate-400 md:h-6 md:w-6"
            />
          </div>
        </Tooltip>
      )}
    </>
  )

  // 处理文件上传成功
  const handleUploadSuccess = (data: UploadedFileResultType) => {
    const uploadedFileId = data.file_id
    const uploadedFileName = data.filename

    setSelectFile((prevFiles) =>
      prevFiles.map((file) => {
        if (file.name === uploadedFileName) {
          return {
            ...file,
            fileId: uploadedFileId, // 动态添加 fileId 字段
          }
        }
        return file
      }),
    )
  }

  // 处理文件上传失败
  const handleUploadError = (error: UploadedFileResultType) => {
    console.info('上传失败:', error)
  }

  const handleSelectFileList = async (fileList: UploadFile[]) => {
    // 更新文件列表，保留已有文件
    setSelectFile((prev) => {
      // 去重，防止重复添加相同文件
      const existingFileNames = new Set(prev.map((file) => file.name))
      const newFiles = fileList.filter((file) => !existingFileNames.has(file.name))
      return [...prev, ...newFiles]
    })
  }

  // 在PC端 使用此组件 渲染 模型
  const renderInputFeatures = (
    <div className="mb-2 hidden h-9 w-full items-center justify-between md:flex">
      <div className="flex cursor-pointer items-center gap-2">
        <FileUploader
          ref={uploaderRef}
          onUploadSuccess={handleUploadSuccess}
          onSelectFileList={handleSelectFileList}
          onUploadError={handleUploadError}
          uploadFileUrl={chatAIApiUrls.chat.uploadFile}
        />

        <ApplyProcessToggle needConfirm={!isMiniMode && !isFirstMessage} />
      </div>

      <div className="flex items-center gap-4">
        {message.length > 0 && (
          <Tooltip placement="bottom" title="清空">
            <span
              onClick={() => {
                setMessage('')
              }}
            >
              <CloseOutlined className="text-xl" />
            </span>
          </Tooltip>
        )}
        {renderSubmit}
      </div>
    </div>
  )

  return (
    <div className={clsx('message-input-comp relative w-full overscroll-contain px-5 pt-5 md:pl-[4px] md:pr-2')}>
      {/** 注意！布局padding 适配ChatHistoryItem */}
      <div className="flex items-center">
        {/* 输入框 */}
        <div
          id="message-input"
          className={clsx(
            'message-input flex w-full flex-col rounded-xl bg-white px-4 focus-within:ring-black hover:ring-1 hover:ring-black dark:bg-slate-800 dark:ring-gray-600 dark:focus-within:ring-primary/80 dark:hover:ring-primary/80 md:ring-1 md:ring-gray-300',
            {
              'ring-red-300 focus-within:ring-red-400 hover:ring-red-400 dark:ring-red-400/90 dark:focus-within:ring-red-500/80 dark:hover:ring-red-500/80':
                message.length >= DEFAULT_INPUT_MESSAGE_LENGTH,
              // 去掉固定高度，改用minHeight保证输入框高度底线
              'min-h-[120px]': selectFile.length === 0,
            },
          )}
        >
          {selectFile.length > 0 && (
            <div className="mb-2 max-h-28 overflow-y-auto">
              <UploadFileList className="mt-4 max-h-60" />
            </div>
          )}
          <div className="flex flex-auto items-center">
            <div className="w-full">
              <div className="py-3 md:min-h-14 md:pt-1" data-replicated-value={message}>
                <MessageInputEditable
                  maxLength={DEFAULT_INPUT_MESSAGE_LENGTH}
                  isMiniMode={isMiniMode}
                  ref={inputRef}
                  onKeyDown={(e) => {
                    !e.nativeEvent.isComposing && pressEnterToSubmit(e)
                  }}
                />
              </div>
            </div>
          </div>
          {renderInputFeatures}
        </div>
      </div>
      {/* 适配iphone底部导航栏高度 */}
      <div className="safe-area-inset-bottom" />
    </div>
  )
})

export default MessageInput

import React from 'react'
import { FileImageOutlined, FileTextOutlined } from '@ant-design/icons'

export interface UploadFileSvgProps {
  width?: number
  height?: number
}

export function PptSvg({ width = 40, height = 40 }: UploadFileSvgProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 40 40" fill="none">
      <path
        d="M5.73752 0C5.21304 0.0144705 4.71419 0.23004 4.34424 0.602091C3.97428 0.974143 3.76153 1.47419 3.75002 1.99875V38.0006C3.74884 38.2621 3.80118 38.521 3.90383 38.7614C4.00648 39.0019 4.15726 39.2188 4.34689 39.3987C4.52667 39.5867 4.7423 39.7368 4.98103 39.8401C5.21977 39.9433 5.47679 39.9977 5.73689 40H34.8888C35.1491 40.0007 35.4069 39.9478 35.6459 39.8446C35.8849 39.7413 36.1001 39.59 36.2781 39.4C36.4649 39.2176 36.6138 39 36.7162 38.7599C36.8187 38.5197 36.8726 38.2617 36.875 38.0006V11.3287L25.6125 0H5.73627H5.73689H5.73752Z"
        fill="#E86235"
      />
      <path
        d="M36.875 11.25H27.6113C27.0888 11.2373 26.5912 11.0243 26.2214 10.6549C25.8517 10.2855 25.6382 9.78809 25.625 9.26562V0L36.8731 11.25H36.875Z"
        fill="white"
        fillOpacity="0.7"
      />
      <path
        d="M8.00732 29.375H9.79732V26.905H10.6673C12.2473 26.905 13.6073 26.125 13.6073 24.355C13.6073 22.515 12.2573 21.925 10.6273 21.925H8.00732V29.375ZM9.79732 25.495V23.345H10.5273C11.3873 23.345 11.8673 23.605 11.8673 24.355C11.8673 25.085 11.4473 25.495 10.5773 25.495H9.79732ZM14.8773 29.375H16.6673V26.905H17.5373C19.1173 26.905 20.4773 26.125 20.4773 24.355C20.4773 22.515 19.1273 21.925 17.4973 21.925H14.8773V29.375ZM16.6673 25.495V23.345H17.3973C18.2573 23.345 18.7373 23.605 18.7373 24.355C18.7373 25.085 18.3173 25.495 17.4473 25.495H16.6673ZM23.0373 29.375H24.8273V23.415H26.8473V21.925H21.0273V23.415H23.0373V29.375ZM27.0373 29.375H28.9373L29.5773 27.925C29.7473 27.535 29.9173 27.145 30.0873 26.685H30.1273C30.3273 27.145 30.4973 27.535 30.6773 27.925L31.3673 29.375H33.3573L31.3073 25.625L33.2273 21.925H31.3373L30.7973 23.265C30.6473 23.615 30.4873 24.005 30.3173 24.485H30.2773C30.0673 24.005 29.9073 23.615 29.7373 23.265L29.1373 21.925H27.1473L29.0773 25.545L27.0373 29.375Z"
        fill="white"
      />
    </svg>
  )
}

export function PdfSvg({ width = 40, height = 40 }: UploadFileSvgProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 40 40" fill="none">
      <path
        d="M5.73703 0C5.21255 0.0144705 4.71371 0.23004 4.34375 0.602091C3.97379 0.974143 3.76104 1.47419 3.74953 1.99875V38.0006C3.74835 38.2621 3.80069 38.521 3.90334 38.7614C4.00599 39.0019 4.15678 39.2188 4.34641 39.3987C4.52618 39.5867 4.74181 39.7368 4.98055 39.8401C5.21929 39.9433 5.4763 39.9977 5.73641 40H34.8883C35.1486 40.0007 35.4064 39.9478 35.6454 39.8446C35.8844 39.7413 36.0996 39.59 36.2776 39.4C36.4644 39.2176 36.6133 39 36.7158 38.7599C36.8182 38.5197 36.8721 38.2617 36.8745 38.0006V11.3287L25.612 0H5.73578H5.73641H5.73703Z"
        fill="#F56839"
      />
      <path
        d="M36.8745 11.25H27.6108C27.0883 11.2373 26.5907 11.0243 26.221 10.6549C25.8512 10.2855 25.6377 9.78809 25.6245 9.26562V0L36.8726 11.25H36.8745Z"
        fill="white"
        fillOpacity="0.7"
      />
      <path
        d="M11.0723 29.375H12.8623V26.905H13.7323C15.3123 26.905 16.6723 26.125 16.6723 24.355C16.6723 22.515 15.3223 21.925 13.6923 21.925H11.0723V29.375ZM12.8623 25.495V23.345H13.5923C14.4523 23.345 14.9323 23.605 14.9323 24.355C14.9323 25.085 14.5123 25.495 13.6423 25.495H12.8623ZM17.9423 29.375H20.1823C22.3523 29.375 23.8523 28.205 23.8523 25.615C23.8523 23.025 22.3523 21.925 20.0823 21.925H17.9423V29.375ZM19.7323 27.935V23.355H19.9723C21.1723 23.355 22.0223 23.845 22.0223 25.615C22.0223 27.385 21.1723 27.935 19.9723 27.935H19.7323ZM25.2323 29.375H27.0223V26.535H29.5623V25.045H27.0223V23.415H29.9823V21.925H25.2323V29.375Z"
        fill="white"
      />
    </svg>
  )
}

export function DocSvg({ width = 40, height = 40 }: UploadFileSvgProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 40 40" fill="none">
      <path
        d="M5.73752 0C5.21304 0.0144705 4.71419 0.23004 4.34424 0.602091C3.97428 0.974143 3.76153 1.47419 3.75002 1.99875V38.0006C3.74884 38.2621 3.80118 38.521 3.90383 38.7614C4.00648 39.0019 4.15726 39.2188 4.34689 39.3987C4.52667 39.5867 4.7423 39.7368 4.98103 39.8401C5.21977 39.9433 5.47679 39.9977 5.73689 40H34.8888C35.1491 40.0007 35.4069 39.9478 35.6459 39.8446C35.8849 39.7413 36.1001 39.59 36.2781 39.4C36.4649 39.2176 36.6138 39 36.7162 38.7599C36.8187 38.5197 36.8726 38.2617 36.875 38.0006V11.3287L25.6125 0H5.73627H5.73689H5.73752Z"
        fill="#1476F1"
      />
      <path
        d="M36.875 11.25H27.6113C27.0888 11.2373 26.5912 11.0243 26.2214 10.6549C25.8517 10.2855 25.6382 9.78809 25.625 9.26562V0L36.8731 11.25H36.875Z"
        fill="white"
        fillOpacity="0.7"
      />
      <path
        d="M7.03271 29.3749H9.27271C11.4427 29.3749 12.9427 28.2049 12.9427 25.6149C12.9427 23.0249 11.4427 21.9249 9.17271 21.9249H7.03271V29.3749ZM8.82271 27.9349V23.3549H9.06271C10.2627 23.3549 11.1127 23.8449 11.1127 25.6149C11.1127 27.3849 10.2627 27.9349 9.06271 27.9349H8.82271ZM17.3927 29.5149C19.4227 29.5149 20.8027 28.0549 20.8027 25.6149C20.8027 23.1849 19.4227 21.7949 17.3927 21.7949C15.3627 21.7949 13.9827 23.1749 13.9827 25.6149C13.9827 28.0549 15.3627 29.5149 17.3927 29.5149ZM17.3927 27.9749C16.4127 27.9749 15.8127 27.0549 15.8127 25.6149C15.8127 24.1749 16.4127 23.3249 17.3927 23.3249C18.3727 23.3249 18.9827 24.1749 18.9827 25.6149C18.9827 27.0549 18.3727 27.9749 17.3927 27.9749ZM25.3327 29.5149C26.3027 29.5149 27.1327 29.1449 27.7627 28.4149L26.8227 27.2949C26.4727 27.6749 26.0027 27.9749 25.4027 27.9749C24.3527 27.9749 23.6727 27.1149 23.6727 25.6349C23.6727 24.1849 24.4627 23.3249 25.4227 23.3249C25.9527 23.3249 26.3427 23.5649 26.7227 23.9049L27.6527 22.7649C27.1427 22.2449 26.3627 21.7949 25.3927 21.7949C23.5027 21.7949 21.8427 23.2149 21.8427 25.6949C21.8427 28.2149 23.4427 29.5149 25.3327 29.5149ZM28.0127 29.3749H29.9127L30.5527 27.9249C30.7227 27.5349 30.8927 27.1449 31.0627 26.6849H31.1027C31.3027 27.1449 31.4727 27.5349 31.6527 27.9249L32.3427 29.3749H34.3327L32.2827 25.6249L34.2027 21.9249H32.3127L31.7727 23.2649C31.6227 23.6149 31.4627 24.0049 31.2927 24.4849H31.2527C31.0427 24.0049 30.8827 23.6149 30.7127 23.2649L30.1127 21.9249H28.1227L30.0527 25.5449L28.0127 29.3749Z"
        fill="white"
      />
    </svg>
  )
}

export function XlsxSvg({ width = 40, height = 40 }: UploadFileSvgProps) {
  return (
    <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width={width} height={height}>
      <path
        d="M146.879977 0A52.335992 52.335992 0 0 0 95.999985 51.167992v921.647856a49.039992 49.039992 0 0 0 15.279998 35.791994A49.839992 49.839992 0 0 0 146.863977 1023.99984h746.287883a48.559992 48.559992 0 0 0 35.567995-15.359998A50.719992 50.719992 0 0 0 943.999853 972.815848V290.015955L655.679898 0H146.847977h0.016z"
        fill="#1AC380"
      />
      <path
        d="M943.999853 287.999955H706.84789A52.127992 52.127992 0 0 1 655.999898 237.199963V0l287.951955 287.999955H943.999853z"
        fill="#FFFFFF"
        fillOpacity=".7"
      />
      <path
        d="M201.023969 751.999883h48.639992l16.383997-37.119995c4.351999-9.983998 8.703999-19.967997 13.055998-31.743995h1.024c5.119999 11.775998 9.471999 21.759997 14.079998 31.743995L311.871951 751.999883h50.943992l-52.479991-95.999985 49.151992-94.719986h-48.383993l-13.823997 34.303995c-3.839999 8.959999-7.935999 18.943997-12.287999 31.231995h-1.023999c-5.375999-12.287998-9.471999-22.271997-13.823998-31.231995l-15.359998-34.303995H203.839968l49.407992 92.671986L201.023969 751.999883z m186.87997 0h120.063982v-38.399994h-74.239989v-152.319977h-45.823993V751.999883z m203.775969 3.583999c46.079993 0 72.703989-27.903996 72.703988-59.647991 0-26.879996-14.079998-42.751993-37.631994-52.223992l-23.807996-9.471998c-16.895997-6.655999-28.671996-10.751998-28.671996-21.503997 0-9.983998 8.447999-15.615998 22.527997-15.615997 14.847998 0 26.623996 5.119999 39.167994 14.591997l22.783996-28.671995c-16.639997-16.639997-39.935994-25.087996-61.95199-25.087996-40.447994 0-69.119989 25.599996-69.119989 57.599991 0 27.647996 18.687997 44.287993 38.655994 52.223992l24.319996 10.239998c16.383997 6.911999 26.879996 10.495998 26.879996 21.503997 0 10.239998-7.935999 16.639997-24.831997 16.639997-15.103998 0-32.767995-7.935999-46.079992-19.455997l-26.111996 31.487995c19.711997 18.175997 46.335993 27.391996 71.167989 27.391996zM677.439894 751.999883h48.639993l16.383997-37.119995c4.351999-9.983998 8.703999-19.967997 13.055998-31.743995h1.024c5.119999 11.775998 9.471999 21.759997 14.079998 31.743995L788.287877 751.999883h50.943992l-52.479992-95.999985 49.151992-94.719986H787.519877l-13.823998 34.303995c-3.839999 8.959999-7.935999 18.943997-12.287998 31.231995h-1.024c-5.375999-12.287998-9.471999-22.271997-13.823998-31.231995l-15.359997-34.303995h-50.943992l49.407992 92.671986L677.439894 751.999883z"
        fill="#FFFFFF"
      />
    </svg>
  )
}

export function TxtSvg({ width = 40, height = 40 }: UploadFileSvgProps) {
  return (
    <svg
      className="icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
    >
      <path
        d="M192 384h640a42.666667 42.666667 0 0 1 42.666667 42.666667v362.666666a42.666667 42.666667 0 0 1-42.666667 42.666667H192v106.666667a21.333333 21.333333 0 0 0 21.333333 21.333333h725.333334a21.333333 21.333333 0 0 0 21.333333-21.333333V308.821333L949.909333 298.666667h-126.528A98.048 98.048 0 0 1 725.333333 200.618667V72.661333L716.714667 64H213.333333a21.333333 21.333333 0 0 0-21.333333 21.333333v298.666667zM128 832H42.666667a42.666667 42.666667 0 0 1-42.666667-42.666667V426.666667a42.666667 42.666667 0 0 1 42.666667-42.666667h85.333333V85.333333a85.333333 85.333333 0 0 1 85.333333-85.333333h530.026667L1024 282.453333V938.666667a85.333333 85.333333 0 0 1-85.333333 85.333333H213.333333a85.333333 85.333333 0 0 1-85.333333-85.333333v-106.666667zM98.624 472.490667v38.4h85.994667V746.666667h43.008V510.890667h86.016v-38.4H98.602667z m228.245333 0l87.850667 132.48L320.981333 746.666667h52.565334l67.626666-105.984L508.8 746.666667h52.565333l-94.464-141.696 88.576-132.48h-52.544l-61.76 96.768-61.738666-96.768h-52.565334z m241.856 0v38.4h85.994667V746.666667h43.008V510.890667h86.016v-38.4h-215.04z"
        fill="#55D7E0"
      />
    </svg>
  )
}

export function UnKnowSvg({ width = 40, height = 40 }: UploadFileSvgProps) {
  return (
    <svg
      className="icon"
      viewBox="0 0 1031 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
    >
      <path
        d="M323.166237 0a156.541149 156.541149 0 0 0-156.541149 156.541149v648.253224a156.541149 156.541149 0 0 0 156.541149 156.541149h488.110668a156.541149 156.541149 0 0 0 156.541149-156.541149v-482.588511A59.303165 59.303165 0 0 0 950.531301 279.709261L687.868699 17.526846a59.543259 59.543259 0 0 0-42.016413-17.526846z"
        fill="#3F70FF"
      />
      <path
        d="M950.531301 279.709261L687.868699 17.526846a58.102696 58.102696 0 0 0-30.491911-16.086283 5.762251 5.762251 0 0 0-6.722626 6.002344V135.652989a182.951465 182.951465 0 0 0 182.951465 182.951466H960.375147a6.002345 6.002345 0 0 0 5.76225-6.96272A56.902227 56.902227 0 0 0 950.531301 279.709261z"
        fill="#2A58D8"
      />
      <path
        d="M840.328253 288.112544h116.925674a51.860258 51.860258 0 0 0-6.722626-8.403283L687.868699 17.526846a66.746073 66.746073 0 0 0-7.923095-6.482532v116.68558A160.382649 160.382649 0 0 0 840.328253 288.112544z"
        fill="#C6E1FF"
      />
      <path
        d="M412.481125 459.779601A160.142556 160.142556 0 0 1 456.178195 341.893552a155.820868 155.820868 0 0 1 116.205392-43.216881 152.939742 152.939742 0 0 1 105.161079 35.773974 120.046893 120.046893 0 0 1 40.095662 93.636577q0 58.822978-78.750762 123.408206a111.163423 111.163423 0 0 0-48.018757 87.634232v12.244783H535.409144v-6.482532a195.676436 195.676436 0 0 1 9.123564-69.867292 164.944431 164.944431 0 0 1 42.016413-50.899883l22.328722-19.68769A112.363892 112.363892 0 0 0 654.255569 423.765533a70.347479 70.347479 0 0 0-24.009379-55.461664 88.594607 88.594607 0 0 0-61.464009-21.608441q-96.037515 0-97.237984 112.363892z m196.876905 283.070575a41.296131 41.296131 0 0 1-40.095662 42.736694h-12.965064a41.296131 41.296131 0 0 1-39.855569-42.736694 41.056038 41.056038 0 0 1 39.855569-42.4966h12.965064a41.296131 41.296131 0 0 1 40.095662 42.4966z"
        fill="#F5F6FA"
      />
    </svg>
  )
}

export function ImageSvg({ width = 40, height = 40 }: UploadFileSvgProps) {
  return (
    <svg
      className="icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
    >
      <path
        d="M938.752 1024H85.248A85.290667 85.290667 0 0 1 0 938.752V85.248C0 38.186667 38.186667 0 85.248 0h853.504C985.856 0 1024 38.186667 1024 85.248v853.504A85.248 85.248 0 0 1 938.752 1024"
        fill="#783EFA"
      />
      <path
        d="M574.421333 798.976H233.770667c-37.12 0-61.056-39.808-43.349334-72.533333l169.813334-319.402667c18.645333-34.517333 68.650667-34.517333 87.168 0l93.824 175.616 76.544 143.786667c17.237333 32.725333-6.229333 72.533333-43.349334 72.533333"
        fill="#FFFFFF"
      />
      <path
        d="M800.938667 798.976h-173.909334c37.12 0 52.522667-43.306667 34.56-72.533333l-76.544-143.786667 48.213334-90.666667a38.784 38.784 0 0 1 68.138666 0L834.986667 742.4a38.656 38.656 0 0 1-34.048 56.618667M796.330667 303.104a78.08 78.08 0 1 1-156.16 0 78.08 78.08 0 0 1 156.16 0"
        fill="#FFFFFF"
      />
    </svg>
  )
}

/** 文件图标根据类型 */
export const getFileIcon = (type: string) => {
  if (type.includes('pdf')) return <PdfSvg />
  if (type.includes('doc')) return <DocSvg />
  if (type.includes('xls')) return <XlsxSvg />
  if (type.includes('ppt')) return <PptSvg />
  if (type.includes('txt')) return <TxtSvg />
  if (type === 'image') return <ImageSvg />
  return <UnKnowSvg />
}

/** 文件类型识别函数（优先使用 MIME） */
export function getFileType({ fileName, fileType, file }: { fileName?: string; fileType?: string; file?: File }) {
  if (file) {
    fileName = file.name
    fileType = file.type
  }

  const mime = fileType?.toLowerCase()
  const ext = fileName?.split('.').pop()?.toLowerCase()

  if (mime) {
    if (mime.includes('word')) return 'doc'
    if (mime.includes('excel')) return 'xlsx'
    if (mime.includes('presentation')) return 'ppt'
    if (mime.includes('pdf')) return 'pdf'
    if (mime.startsWith('image/')) return 'image'
    if (mime === 'text/plain') return 'txt'
  }

  if (ext) {
    if (['doc', 'docx'].includes(ext)) return 'doc'
    if (['xls', 'xlsx'].includes(ext)) return 'xlsx'
    if (['ppt', 'pptx'].includes(ext)) return 'ppt'
    if (ext === 'pdf') return 'pdf'
    if (ext === 'txt') return 'txt'
    if (['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'].includes(ext)) return 'image'
  }

  return 'doc' // fallback
}

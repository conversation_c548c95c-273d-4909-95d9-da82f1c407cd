import { Tooltip, Upload, UploadFile, message } from 'antd'
import React, { forwardRef, useImperativeHandle, useState } from 'react'
import axios from 'axios'
import {
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FileImageOutlined,
  FileUnknownOutlined,
} from '@ant-design/icons'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import type { RcFile, UploadProps } from 'antd/es/upload'
import { flowSessionAtom, isSubmittingAtom, selectFileAtom, uploadSelectFileAtom } from '../pages/AIAtoms'
import { SvgIcon, UploadIcon } from './SvgIcon'

export interface FileUploaderRef {
  fileList: UploadFile[] // 暴露 fileList
}

interface FileUploaderProps {
  /** 上传文件的URL */
  uploadFileUrl?: string
  /** 选择文件后的回调 */
  onSelectFileList?: (fileList: UploadFile[]) => void
  /** 上传成功的回调函数 */
  onUploadSuccess?: (fileInfo: any) => void
  /** 上传失败的回调函数 */
  onUploadError?: (error: any) => void
  /** 文件移除的回调函数 */
  onFileRemove?: () => void
  /** 选择了文件的回调函数 */
  onSelectFile?: () => void
  /** 自定义样式类名 */
  className?: string
  /** 是否禁用上传 */
  disabled?: boolean
  /** 最大文件数量，默认为1 */
  maxCount?: number
  /** 接受的文件类型，默认为 .doc,.docx,.pdf */
  accept?: string
  /** 最大文件大小（MB），默认为50 */
  maxSize?: number
}

/**
 * 根据文件扩展名选择对应的图标
 * @param fileName 文件名，如 'document.pdf'
 * @returns 对应的图标字符串
 */
export function getIconByFileName(fileName: string) {
  if (!fileName) return <FileUnknownOutlined />

  const extension = fileName.toLowerCase().split('.').pop()

  switch (extension) {
    case 'pdf':
      return <FilePdfOutlined style={{ color: '#e25555' }} />
    case 'doc':
    case 'docx':
      return <FileWordOutlined style={{ color: '#2a5699' }} />
    case 'xls':
    case 'xlsx':
      return <FileExcelOutlined style={{ color: '#1c8f3a' }} />
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'bmp':
    case 'webp':
      return <FileImageOutlined style={{ color: '#8b8b8b' }} />
    default:
      return <FileUnknownOutlined />
  }
}

const FileUploader = forwardRef<FileUploaderRef, FileUploaderProps>(
  (
    {
      uploadFileUrl,
      onUploadSuccess,
      onUploadError,
      onSelectFileList,
      onFileRemove,
      onSelectFile,
      className = 'h-51 w-115 bg-white',
      maxCount = 10,
      maxSize = 100,
      disabled = false,
      accept = '.doc,.docx,.pdf,.xls,.xlsx,.png,.jpg,.jpeg,.gif,.bmp,.webp,.txt',
    },
    ref,
  ) => {
    const flowSession = useAtomValue(flowSessionAtom)
    const setIsSubmitting = useSetAtom(isSubmittingAtom)
    const [fileList, setFileList] = useAtom(uploadSelectFileAtom)

    // 暴露给外部
    useImperativeHandle(ref, () => ({
      fileList,
    }))

    const handleBeforeUpload = (file: UploadFile) => {
      // 检查文件大小
      const isLt = file.size && file.size / 1024 / 1024 < maxSize
      if (!isLt) {
        message.error(`文件 ${file.name} 大小超出 ${maxSize}MB 限制`)
        return Upload.LIST_IGNORE
      }

      // 检查文件数量
      if (fileList.length >= maxCount) {
        message.warning(`最多只能上传 ${maxCount} 个文件`)
        return Upload.LIST_IGNORE
      }

      return true // 允许文件上传
    }

    // 自定义上传逻辑
    const customRequest: UploadProps['customRequest'] = async ({ file, onSuccess, onError }) => {
      const rcFile = file as RcFile
      try {
        const formData = new FormData()
        formData.append('file', rcFile)
        formData.append('sessionId', flowSession?.session_id as string)
        formData.append('fileName', rcFile.name)

        const response = await axios.post(uploadFileUrl!, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        if (response.data?.code === 0) {
          message.success(`${rcFile.name} 上传成功`)
          onUploadSuccess?.(response.data.data)
          onSuccess?.(response.data.data, rcFile)
        } else {
          message.error(`${rcFile.name} 上传失败: ${response.data.msg || '未知错误'}`)
          onUploadError?.(response.data.data)
          onError?.(new Error(response.data.msg || '上传失败'), response.data)
        }
      } catch (error) {
        message.error(`${rcFile.name} 上传失败，请重试`)
        onUploadError?.(error)
        onError?.(error instanceof Error ? error : new Error('上传失败'))
      }
    }

    const uploadProps: UploadProps = {
      name: 'file',
      maxCount,
      fileList,
      showUploadList: false,
      className,
      multiple: true,
      disabled,
      accept,
      customRequest, // 使用 customRequest 控制上传
      onChange: ({ fileList: newFileList }) => {
        setIsSubmitting(true)
        // 更新 fileList
        setFileList(newFileList)
        onSelectFileList?.(newFileList)
        onSelectFile?.()
        // 检查是否所有文件都上传完成
        const allUploaded = newFileList.length > 0 && newFileList.every((file) => file.status === 'done')

        if (allUploaded) {
          setIsSubmitting(false)
          setFileList([])
        }
      },
      onRemove: () => {
        onFileRemove?.()
      },
      beforeUpload: handleBeforeUpload,
    }

    return (
      <Tooltip title={`支持上传文件（最多${maxCount}个，每个不超过${maxSize}MB），接受${accept}`}>
        <Upload {...uploadProps}>
          <SvgIcon icon={UploadIcon} className="mt-2" />
        </Upload>
      </Tooltip>
    )
  },
)

export default FileUploader
FileUploader.displayName = 'FileUploader'

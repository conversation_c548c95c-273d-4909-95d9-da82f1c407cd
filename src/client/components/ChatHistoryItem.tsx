/**
 * @description 聊天历史中的一次聊天记录，包含一问一答
 */
import React, { Fragment } from 'react'
import { Light as SyntaxHighlighter } from 'react-syntax-highlighter'
import sql from 'react-syntax-highlighter/dist/cjs/languages/hljs/sql'
import clsx from 'clsx'
import { Avatar } from 'antd'
import { Chat, ThemeType, AnsChatItem, AssistantHelloTextChatItem } from '@shared/common-types'
import { IS_H5 } from '@shared/constants'
import './ChatHistoryItem.css'
import { renderChatAnsHelloTextItem, renderChatAnsTextItem, renderDocMarkdownItem } from './ChatHistoryRenderComp'
import UploadFileList from './UploadFileList'

SyntaxHighlighter.registerLanguage('sql', sql)

interface Props {
  chat: Chat
  theme: ThemeType
}

function ChatHistoryItem({ chat, theme }: Props) {
  const BiResultWrapper = (props: { children: JSX.Element | React.ReactNode; contentClassName?: string }) => {
    const { children, contentClassName } = props
    return (
      <div className="chat-item chat-item-ans group flex flex-col md:flex-row">
        <div
          id={chat.id}
          className={clsx(
            'left-content flex w-full max-w-full flex-col items-start gap-2 self-start rounded-lg bg-white dark:bg-slate-800',
            contentClassName,
          )}
        >
          {children}
        </div>
      </div>
    )
  }

  return (
    <Fragment>
      {chat.ask && (
        <div className="chat-item-ask flex items-start self-start">
          {/* items-center ➜ items-start */}
          <div className="flex-shrink-0">
            <Avatar size={40} className="mr-3.5 h-8 w-8 bg-black font-medium text-white">
              AD
            </Avatar>
          </div>
          <div className="flex flex-col gap-1">
            {chat.ask.fileList && chat.ask.fileList.length > 0 && (
              <UploadFileList files={chat.ask.fileList} isShowDelete={false} className="mt-0 h-auto" />
            )}

            {chat.ask.content && (
              <div className="rounded-b-xl rounded-tr-xl bg-gray-200 px-5 py-2 text-black">
                <div className="text-base leading-7">{chat.ask.content}</div>
              </div>
            )}
          </div>
        </div>
      )}
      {chat.isSystemPrompt ? (
        <>
          {!IS_H5 && (
            <>
              <BiResultWrapper contentClassName="px-6 py-4">
                {renderChatAnsHelloTextItem((chat.ans[0].content[0] as AssistantHelloTextChatItem).text, 9999)}
              </BiResultWrapper>
            </>
          )}
        </>
      ) : (
        <div>
          <AiResultContent theme={theme} currentChat={chat} chatAnsItem={chat.ans[0]} />
        </div>
      )}
    </Fragment>
  )
}

export function AiResultContent(props: { theme: ThemeType; currentChat: Chat; chatAnsItem: AnsChatItem }) {
  const { chatAnsItem } = props
  return (
    <div className="BI-result-content flex flex-col gap-2 break-all rounded-xl bg-white px-2 py-2">
      {(chatAnsItem.content || []).map((content, idx) => {
        switch (content.type) {
          case 'hello-text':
            return renderChatAnsHelloTextItem(content.text, idx)
          case 'text':
            return renderChatAnsTextItem(chatAnsItem, content, idx)
          case 'apply-result': {
            return renderDocMarkdownItem(content.text, idx)
          }
          default:
            return renderChatAnsHelloTextItem('', idx)
        }
      })}
    </div>
  )
}

export default React.memo(ChatHistoryItem)

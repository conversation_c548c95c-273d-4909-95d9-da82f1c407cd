/**
 * 管理页面的 Card 组件
 */
import React from 'react'
import clsx from 'clsx'

interface Props {
  className?: string
  title?: React.ReactNode
  children: React.ReactNode
  actions?: React.ReactNode
}

export default function AdminCard(props: Props) {
  return (
    <div
      className={clsx(
        'admin-card flex flex-1 flex-grow flex-col rounded-md bg-white dark:bg-slate-900',
        props.className,
      )}
    >
      {(props.title || props.actions) && (
        <h3 className="admin-card-title mb-4 flex flex-row">
          {props.title && <div className="text-xl font-bold text-gray-600 dark:text-gray-200">{props.title}</div>}
          {props.actions && <div className="ml-auto flex gap-2">{props.actions}</div>}
        </h3>
      )}
      <div className="admin-card-body">{props.children}</div>
    </div>
  )
}

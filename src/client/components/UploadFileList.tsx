import React from 'react'
import clsx from 'clsx'
import { useAtom, useAtomValue } from 'jotai'
import { message, Tooltip } from 'antd'
import { CloseOutlined } from '@ant-design/icons'
import axios from 'axios'
import { CustomUploadFile } from 'src/shared/common-types'
import { chatAIApiUrls } from 'src/shared/url-map'
import { currentSessionIdAtom, flowSessionAtom, selectFileAtom } from '../pages/AIAtoms'
import { formatFileSize } from '../utils'
import { getFileIcon, getFileType } from './UploadFileSvg'

/** 组件 Props */
interface UploadFileListProps {
  files?: CustomUploadFile[]
  onDelete?: (fileId: number) => void
  isShowDelete?: boolean
  className?: string
}

/** 文件列表组件 */
const UploadFileList: React.FC<UploadFileListProps> = ({ files, onDelete, isShowDelete = true, className }) => {
  const [selectFile, setSelectFile] = useAtom(selectFileAtom)
  const currentSessionId = useAtomValue(currentSessionIdAtom)
  const flowSession = useAtomValue(flowSessionAtom)
  const currentFileList = files || selectFile

  const handleDelete = async (fileId: number) => {
    if (!currentSessionId && !flowSession?.session_id) return message.error('sessionId is empty')
    if (!fileId) return message.error('fileId is empty')
    try {
      await axios.delete(chatAIApiUrls.chat.deleteFile((currentSessionId || flowSession?.session_id) as string, fileId))
      const newList = currentFileList.filter((file: CustomUploadFile) => file.fileId !== fileId)
      setSelectFile(newList)
      onDelete?.(Number(fileId))
    } catch (error: any) {
      console.error('error - deleteFile', error)
    }
  }

  return (
    <div className={clsx('flex flex-wrap gap-2', className)}>
      {currentFileList.map((item: CustomUploadFile, index: number) => {
        const fileType = getFileType({ fileName: item.name, fileType: item.type, file: item.originFileObj })

        return (
          <div
            key={index}
            className={clsx(
              'relative flex h-16 w-64 items-center justify-between rounded-lg bg-gray-100 p-1 transition-shadow duration-200 hover:shadow-md dark:bg-gray-700',
            )}
          >
            <div className="flex items-center gap-2">
              <div className={clsx({ 'ml-1': fileType === 'image' })}>{getFileIcon(fileType)}</div>
              <div className="pr-2">
                <Tooltip title={item.name}>
                  <p className="max-w-[180px] truncate text-sm font-semibold text-black dark:text-gray-100">
                    {item.name}
                  </p>
                </Tooltip>
                <p className="text-xs text-gray-500 dark:text-gray-300">{formatFileSize(item.size as number)}</p>
              </div>
            </div>
            {isShowDelete && (
              <button
                onClick={() => handleDelete(item.fileId as number)}
                className="absolute right-1 top-1 text-gray-400 transition-colors hover:text-black"
                aria-label="Delete file"
              >
                <CloseOutlined />
              </button>
            )}
          </div>
        )
      })}
    </div>
  )
}

export default UploadFileList

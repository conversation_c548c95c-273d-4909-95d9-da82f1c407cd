import React, { forwardRef } from 'react'

/** 注意，这里没有设置 icon 的默认大小，你需要手动设置 */
export const SvgIcon = forwardRef(function SvgIconInner(
  { icon, className, ...restProps }: { icon: string; className: string },
  ref: React.Ref<HTMLDivElement>,
) {
  return <div className={className} {...restProps} ref={ref} dangerouslySetInnerHTML={{ __html: icon }} />
})

/**
 * 从 iconfont 中添加 Icon 的方法：https://mk70znjkuv.feishu.cn/wiki/B3UMwNOJ1iFX4mkpVJRcG55inub#X6DqdYtg2ofEgKx505xcnH9Mnoh
 * 技巧：
 * 1. 让 icon 支持深浅主题的方法：找到 fill 或者 stroke 属性，将其改为 fill="currentColor" 或者 stroke="currentColor"
 * 2. 去掉 svg 根标签中的 width 和 height 属性，SvgIcon 会使用 className 来设置
 */
export const chartDescriptionIcon = `<svg fill="currentColor" viewBox="0 0 32 32" id="icon" xmlns="http://www.w3.org/2000/svg"><defs><style> .cls-1 { fill: none; } </style></defs><rect x="28" y="22" width="2" height="8" /><rect x="24" y="18" width="2" height="12" /><rect x="20" y="26" width="2" height="4" /><path d="M9,16a7,7,0,1,0,7,7A7.0078,7.0078,0,0,0,9,16Zm4.8989,6H10V18.1011A5.0145,5.0145,0,0,1,13.8989,22ZM9,28a5,5,0,0,1-1-9.8989V22a2,2,0,0,0,2,2h3.8989A5.0081,5.0081,0,0,1,9,28Z" /><path d="M22.5352,12l4-6H30V4H25.4648l-4,6H18V2H16V14a2,2,0,0,0,2,2H30V14H18V12Z" /><circle cx="11" cy="7" r="1" /><circle cx="9" cy="11" r="1" /><circle cx="7" cy="5" r="1" /><circle cx="5" cy="9" r="1" /><circle cx="3" cy="13" r="1" /><rect id="_Transparent_Rectangle_" data-name="&lt;Transparent Rectangle&gt;" class="cls-1" width="32" height="32" /></svg>`

export const pdfIcon = `<svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
<path d="M12.0278 1.55566L17.2778 6.79589V17.2223C17.2778 17.9587 16.6808 18.5557 15.9445 18.5557H5.05556C4.31923 18.5557 3.72223 17.9587 3.72223 17.2223V2.889C3.72223 2.15266 4.31923 1.55566 5.05556 1.55566H12.0278ZM11.798 2.11122H5.05556C4.8539 2.11122 4.66012 2.18954 4.51509 2.32967C4.37007 2.4698 4.28514 2.66078 4.27823 2.86233L4.27778 2.889V17.2223C4.27778 17.424 4.35611 17.6178 4.49624 17.7628C4.63637 17.9078 4.82735 17.9928 5.0289 17.9997L5.05556 18.0001H15.9445C16.1461 18.0001 16.3399 17.9218 16.4849 17.7817C16.6299 17.6415 16.7149 17.4505 16.7218 17.249L16.7222 17.2223V7.02633L11.798 2.11122Z" fill="#FF7875"/>
<path d="M17.1944 7.35044H12.9924C12.1032 7.35044 11.3801 6.63889 11.3614 5.754L11.3611 5.719V1.55566H11.9167V5.719C11.9167 6.30289 12.3819 6.77822 12.9619 6.79444L12.9924 6.79489H17.1944V7.35044Z" fill="#FF7875"/>
<path d="M2.16667 9.6665H18.8333C19.2037 9.6665 19.3889 9.85169 19.3889 10.2221V15.7776C19.3889 16.148 19.2037 16.3332 18.8333 16.3332H2.16667C1.7963 16.3332 1.61111 16.148 1.61111 15.7776V10.2221C1.61111 9.85169 1.7963 9.6665 2.16667 9.6665Z" fill="#FF7875"/>
<path d="M6.71943 14.8891V13.6541H7.15443C7.94443 13.6541 8.62443 13.2641 8.62443 12.3791C8.62443 11.4591 7.94943 11.1641 7.13443 11.1641H5.82443V14.8891H6.71943ZM7.10943 12.9491H6.71943V11.8741H7.08443C7.51443 11.8741 7.75443 12.0041 7.75443 12.3791C7.75443 12.7441 7.54443 12.9491 7.10943 12.9491ZM10.3794 14.8891C11.4644 14.8891 12.2144 14.3041 12.2144 13.0091C12.2144 11.7141 11.4644 11.1641 10.3294 11.1641H9.25943V14.8891H10.3794ZM10.2744 14.1691H10.1544V11.8791H10.2744C10.8744 11.8791 11.2994 12.1241 11.2994 13.0091C11.2994 13.8941 10.8744 14.1691 10.2744 14.1691ZM13.7994 14.8891V13.4691H15.0694V12.7241H13.7994V11.9091H15.2794V11.1641H12.9044V14.8891H13.7994Z" fill="white"/>
</svg>`

export const groupColumnChartIcon = `<svg fill="currentColor" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4172"><path d="M870.4 772.267c23.467 0 40.533 19.2 40.533 40.533s-19.2 40.533-40.533 40.533H142.933c-23.466 0-40.533-19.2-40.533-40.533v-608c0-23.467 19.2-40.533 40.533-40.533s40.534 19.2 40.534 40.533v567.467H870.4zM277.333 533.333c0-23.466 19.2-40.533 40.534-40.533S358.4 512 358.4 533.333v123.734c0 23.466-19.2 40.533-40.533 40.533s-40.534-19.2-40.534-42.667v-121.6z m157.867-160c0-23.466 19.2-40.533 40.533-40.533s40.534 19.2 40.534 40.533v277.334c0 23.466-19.2 40.533-40.534 40.533S435.2 672 435.2 650.667V373.333zM738.133 256c0-23.467 19.2-40.533 40.534-40.533S819.2 234.667 819.2 256v388.267c0 23.466-19.2 40.533-40.533 40.533s-40.534-19.2-40.534-40.533V256zM593.067 501.333c0-23.466 19.2-40.533 40.533-40.533s40.533 19.2 40.533 40.533v142.934c0 23.466-19.2 40.533-40.533 40.533s-40.533-19.2-40.533-40.533V501.333z" p-id="4173"></path></svg>`

export const StackedColumnChartIcon = `<svg fill="currentColor" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10203"><path d="M989.866667 512H34.133333c-10.24 0-17.066667 6.826667-17.066666 17.066667s6.826667 17.066667 17.066666 17.066666h955.733334c10.24 0 17.066667-6.826667 17.066666-17.066666s-6.826667-17.066667-17.066666-17.066667zM102.4 477.866667h170.666667c10.24 0 17.066667-6.826667 17.066666-17.066667v-204.8c0-10.24-6.826667-17.066667-17.066666-17.066667H102.4c-10.24 0-17.066667 6.826667-17.066667 17.066667v204.8c0 10.24 6.826667 17.066667 17.066667 17.066667zM409.6 477.866667h170.666667c10.24 0 17.066667-6.826667 17.066666-17.066667v-443.733333c0-10.24-6.826667-17.066667-17.066666-17.066667h-170.666667c-10.24 0-17.066667 6.826667-17.066667 17.066667v443.733333c0 10.24 6.826667 17.066667 17.066667 17.066667zM716.8 477.866667h170.666667c10.24 0 17.066667-6.826667 17.066666-17.066667v-375.466667c0-10.24-6.826667-17.066667-17.066666-17.066666h-170.666667c-10.24 0-17.066667 6.826667-17.066667 17.066666v375.466667c0 10.24 6.826667 17.066667 17.066667 17.066667zM580.266667 580.266667h-170.666667c-10.24 0-17.066667 6.826667-17.066667 17.066666v204.8c0 10.24 6.826667 17.066667 17.066667 17.066667h170.666667c10.24 0 17.066667-6.826667 17.066666-17.066667v-204.8c0-10.24-6.826667-17.066667-17.066666-17.066666zM273.066667 580.266667H102.4c-10.24 0-17.066667 6.826667-17.066667 17.066666v409.6c0 10.24 6.826667 17.066667 17.066667 17.066667h170.666667c10.24 0 17.066667-6.826667 17.066666-17.066667v-409.6c0-10.24-6.826667-17.066667-17.066666-17.066666zM887.466667 580.266667h-170.666667c-10.24 0-17.066667 6.826667-17.066667 17.066666v273.066667c0 10.24 6.826667 17.066667 17.066667 17.066667h170.666667c10.24 0 17.066667-6.826667 17.066666-17.066667v-273.066667c0-10.24-6.826667-17.066667-17.066666-17.066666z" p-id="10204"></path></svg>`

export const MultiLineChartIcon = `<svg fill="currentColor" viewBox="0 0 1079 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11180"><path d="M194.958961 802.177662l166.49974 47.343377a39.896104 39.896104 0 0 0 36.97039-7.979221l163.042078-139.902337 134.582857 89.633246a40.162078 40.162078 0 0 0 48.673247-3.723636l319.168831-292.571429a39.896104 39.896104 0 1 0-53.194805-59.046233L714.14026 707.490909l-132.987013-89.101299a39.896104 39.896104 0 0 0-48.141299 2.925715l-169.691429 145.753766-166.233766-47.343377a39.63013 39.63013 0 0 0-35.906493 7.181299L79.792208 792.070649v-83.781818l86.973506-65.42961 139.902338 46.545454a39.63013 39.63013 0 0 0 42.28987-11.170909l193.629091-218.098701 159.584416 68.621299a39.63013 39.63013 0 0 0 50.801039-17.82026l212.77922-398.961039a39.896104 39.896104 0 0 0-70.217143-37.768312l-195.224935 366.778182-152.669091-65.42961a40.162078 40.162078 0 0 0-45.481558 10.107013l-195.224935 219.694545-134.848831-44.683636a39.098182 39.098182 0 0 0-36.438442 5.851428L79.792208 608.282597V39.896104a39.896104 39.896104 0 0 0-79.792208 0V984.103896a39.896104 39.896104 0 0 0 39.896104 39.896104H984.103896a39.896104 39.896104 0 0 0 0-79.792208H79.792208v-50.003117z" p-id="11181"></path></svg>`

export const microphoneEmpty = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_595_4789)">
<mask id="mask0_595_4789" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
<path d="M24 0H0V24H24V0Z" fill="white"/>
</mask>
<g mask="url(#mask0_595_4789)">
<path d="M15 6C15 4.34315 13.6569 3 12 3C10.3431 3 9 4.34315 9 6V11C9 12.6569 10.3431 14 12 14C13.6569 14 15 12.6569 15 11V6Z" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M19 11C19 14.866 15.866 18 12 18C8.13401 18 5 14.866 5 11" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12 18V21" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
<defs>
<clipPath id="clip0_595_4789">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>`

export const microphoneFull = `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="8" y="2" width="8" height="13" rx="4" fill="currentColor" stroke="currentColor" stroke-width="1.5"/>
<path d="M20 11.5C20 15.9183 16.4183 19.5 12 19.5C7.58172 19.5 4 15.9183 4 11.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
<path d="M12 22V20" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
</svg>
`

export const llmDefaultIcon = `<svg fill="currentColor" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4886"><path d="M515.122109 96.460253c229.126644 0 415.534587 186.413103 415.534586 415.539747s-186.407942 415.534587-415.534586 415.534587c-81.819886 0-160.956307-23.738346-228.868619-68.650265-15.925334-10.52744-35.086308-16.095631-55.398075-16.100791h-0.030963c-23.98605 0-48.095953 7.229875-77.232193 20.064063 22.603034-53.431921 31.272691-94.463136 7.204072-133.636567-40.040397-65.141118-61.208809-140.257501-61.208809-217.205866 0-229.131805 186.407942-415.544908 415.534587-415.544908m0-82.56816c-275.096467 0-498.102747 223.006279-498.102747 498.107907 0 95.469435 26.860455 184.674011 73.434057 260.445779 17.999859 29.301376-100.057128 207.550552-76.571647 232.42421 2.637021 2.791836 6.698342 4.035519 11.915618 4.035519 42.729023 0 162.576707-83.564138 205.037383-83.553817 4.035519 0 7.38469 0.753434 9.887537 2.415118 78.682296 52.038583 173.000937 82.335937 274.399799 82.335938 275.106788 0 498.102747-223.001119 498.102746-498.102747 0-275.096467-222.995958-498.107907-498.102746-498.107907z" p-id="4887"></path><path d="M284.142841 489.866573v-50.738135h102.864446v105.640801c-8.112322 13.66503-21.14777 24.38857-39.096024 32.144816-17.958575 7.751086-37.702686 11.63695-59.252975 11.63695-25.250375 0-47.667631-5.557869-67.241446-16.673607-19.578975-11.115739-34.750874-26.700479-45.520859-46.7439-10.769984-20.03826-16.157557-42.909641-16.157556-68.634783 0-26.174107 5.614635-49.27771 16.854225-69.326291 11.239591-20.0331 26.586948-35.442383 46.047231-46.222689 19.460283-10.764824 41.232475-16.157557 65.326896-16.157556 103.571436 0 99.737177 72.422597 99.737177 87.997016l-49.344797 0.278668c-4.8612-10.883516-11.642111-19.341591-20.327249-25.369067-8.690299-6.027476-19.062924-9.036053-31.107554-9.036053-12.276853 0-23.402913 3.116948-33.357536 9.376646-9.970105 6.254538-17.839883 15.238986-23.629976 26.932702-5.795253 11.704037-8.685138 25.544525-8.685138 41.521463 0 24.331805 6.424835 43.497939 19.284826 57.513884 12.85483 14.026266 30.059971 21.018757 51.6051 21.018758 16.214322 0 28.615028-4.283223 37.181474-12.84967v-32.315114h-45.180265zM518.017155 324.678647c21.312906 0 39.849458 3.674283 55.599334 11.012529 15.749877 7.343406 27.846112 17.746994 36.293867 31.226246 8.478718 13.474092 12.694855 29.23945 12.694855 47.296074 0 18.046303-4.216137 33.811662-12.694855 47.280593-8.452915 13.489573-20.54399 23.893161-36.293867 31.231406-15.755037 7.338245-34.291589 11.007368-55.599334 11.007368h-32.666029v85.20002H425.922693V324.678647h92.094462z m-6.615774 127.077559c14.37202 0 26.065736-2.884725 35.106949-8.664496 9.036053-5.779771 13.55666-15.398962 13.55666-28.878214 0-13.241869-4.582533-22.809454-13.726956-28.707917-9.154745-5.893302-20.802016-8.839954-34.936653-8.839954h-26.050255v75.090581h26.050255zM631.646424 324.797339h211.973109v54.489825h-76.442634v209.645719h-59.093V379.287164H631.646424V324.797339z" p-id="4888"></path><path d="M380.76307 718.714549m-42.532923 0a42.532923 42.532923 0 1 0 85.065847 0 42.532923 42.532923 0 1 0-85.065847 0Z" p-id="4889"></path><path d="M501.67898 718.714549m-42.538084 0a42.538084 42.538084 0 1 0 85.076168 0 42.538084 42.538084 0 1 0-85.076168 0Z" p-id="4890"></path><path d="M622.59489 718.714549m-42.532924 0a42.532923 42.532923 0 1 0 85.065847 0 42.532923 42.532923 0 1 0-85.065847 0Z" p-id="4891"></path></svg>`

export const chartThemeIcon = `<svg t="1697551044261" fill="currentColor" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10233"><path d="M204.4 524.9c-14.5 1.5-26.2 13.2-27.7 27.7-2.1 19.9 14.6 36.7 34.6 34.6 14.5-1.5 26.2-13.2 27.8-27.8 2-19.9-14.8-36.6-34.7-34.5zM265.4 473.7c21.8-1.9 39.4-19.5 41.4-41.4 2.5-28.5-21.2-52.3-49.7-49.7-21.8 1.9-39.4 19.5-41.4 41.4-2.6 28.4 21.2 52.2 49.7 49.7zM415.8 266.9c-28.5 1.8-51.6 24.9-53.4 53.4-2.2 34.5 26.4 63.1 60.9 60.9 28.5-1.8 51.6-24.9 53.4-53.4 2.1-34.6-26.4-63.1-60.9-60.9zM621.9 253.8c-35.1 2.2-63.4 30.6-65.6 65.6-2.7 42.4 32.4 77.6 74.8 74.8 35.1-2.2 63.4-30.6 65.6-65.6 2.8-42.4-32.3-77.5-74.8-74.8zM966.5 276.4c-0.5-7.6-4-14.6-9.8-19.6l-0.7-0.6c-5.2-4.5-11.9-7-18.8-7-8.3 0-16.2 3.6-21.6 9.9L574 652.4l-43.5 85.5 1.1 0.9-4.9 11.3 11.1-5.9 1.5 1.3 78-54.3 342.3-394c5-5.8 7.4-13.2 6.9-20.8z" p-id="10234"></path><path d="M897.8 476.3c-13.8-1.4-26.7 7.4-30.4 20.7-6.9 24.6-19.3 64.5-35.1 97.8C809.5 643 767.4 710.1 696.7 756c-72.2 46.9-142.7 56.7-189.2 56.7-37 0-72.2-6.1-101.7-17.7-26.9-10.5-46.4-24.6-54.9-39.7-3.4-6.1-7.2-12.9-11.2-20.2-17.2-31.1-36.6-66.5-49.7-77.4-15.9-13.2-39.1-15-59.8-15-8.1 0-40.8 1.3-48.5 1.3-33.1 0-49.4-6.5-56.1-22.4-17.8-42.3-7.3-114.3 26.8-183.4C205.2 331.4 300 253.3 412.6 224c40-10.6 81.2-18.9 121.3-18.9 85.6 0 187.8 32.8 252.5 77.2 11.4 7.8 26.9 5.8 35.7-4.9 10.4-12.6 7.1-31.4-6.8-39.8-23.3-14-57.9-34-86.3-47.1-60.3-27.9-123.7-41.9-189.2-41.9-68.1 0-148.8 16.4-217.2 47.2-78.1 35-135.2 85-179.4 147.5-36.4 51.4-67.8 111.1-80.1 168.7-7.5 35.1-6.8 57.4-2.4 87.8 4.2 29.2 13.4 52.5 26.9 67.5 22.4 25.1 51.5 37.4 89 37.4 13.9 0 56.3-5 63.1-5 7.4 0 12.2 1.2 14.4 3.8 6.4 7.4 14.4 22.4 23.7 39.9 7.5 14.1 15.9 30.1 25.4 45.3 12.1 19.5 36.9 40.4 66.5 55.9 27 14.1 71.9 31 132.2 31 72 0 148.3-23.6 226.7-70.1 74.9-44.4 123-118.9 150.2-173.6 19-38.3 34.7-87.2 43.8-119.1 4.8-17.3-7-34.7-24.8-36.5z" p-id="10235"></path></svg>`

export const DatasourceIcon = `<svg t="1698136756954" class="icon" fill="currentColor" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6954" ><path d="M545.01 566.46h351.47v60.31H545.01zM545.01 709.87h351.47v60.31H545.01zM545.01 853.29h351.47v60.31H545.01z" p-id="6955" fill="#a7b3fd"></path><path d="M499.22 689.33c-97 0-175.83-78.88-175.83-175.83s78.88-175.83 175.83-175.83S675 416.55 675 513.5h-60.26A115.52 115.52 0 1 0 499.22 629z" p-id="6956" fill="#a7b3fd"></path><path d="M499.4 915.29h-70.29a50 50 0 0 1-49.94-49.94v-59.88l-42.66 42.66a49.15 49.15 0 0 1-35 14.72 49.88 49.88 0 0 1-35.65-14.63L165.73 748.07a49.83 49.83 0 0 1-0.1-70.64L208 635.06h-60.63a50 50 0 0 1-49.94-49.94V443.4a50 50 0 0 1 49.94-49.94h59.88l-42.65-42.65a49.83 49.83 0 0 1-0.1-70.63L264.65 180a49.15 49.15 0 0 1 35-14.73h0.35a49.6 49.6 0 0 1 35.33 14.63l42.38 42.38v-60.62a50 50 0 0 1 49.94-49.94h141.67a50 50 0 0 1 49.94 49.94v59.88l42.64-42.64a49.15 49.15 0 0 1 35-14.74 50 50 0 0 1 35.65 14.63L832.72 279a49.83 49.83 0 0 1 0.09 70.62L790.43 392h60.63A50 50 0 0 1 901 441.89v71.61h-60.31v-61.24h-99.07l-6.94-20.47a240.46 240.46 0 0 0-11.89-28.62l-9.5-19.36 69.5-69.5-85.51-85.51-69.87 69.87-19.41-9.5a237.06 237.06 0 0 0-28.62-11.7L559 270.53V172H438v99.1l-20.5 6.9a240.36 240.36 0 0 0-28.62 11.89l-19.36 9.5L300 229.93l-85.51 85.51 69.88 69.88-9.5 19.36a236.89 236.89 0 0 0-11.7 28.62l-6.94 20.47h-98.49v121h99.07l6.94 20.47a240.46 240.46 0 0 0 11.89 28.62l9.5 19.36-69.5 69.5 85.51 85.51 69.85-69.9 19.36 9.5A236.94 236.94 0 0 0 419 749.54l20.47 6.94V855h59.93z" p-id="6957" fill="#a7b3fd"></path></svg>`

export const arrowToggleRightIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M13.75 9.0251H14.5C14.5 9.01035 14.4996 8.9956 14.4987 8.98087L13.75 9.0251ZM14.2847 8.10156L13.9503 7.43023L14.2847 8.10156ZM15.344 8.2311L15.8446 7.67259C15.8399 7.66838 15.8351 7.66424 15.8303 7.66015L15.344 8.2311ZM18.65 11.1941L19.1569 10.6413L19.1506 10.6356L18.65 11.1941ZM18.65 12.7831L19.1507 13.3416L19.1569 13.3359L18.65 12.7831ZM15.344 15.7471L15.8303 16.3181C15.8352 16.3139 15.8399 16.3098 15.8447 16.3055L15.344 15.7471ZM14.2847 15.8767L14.6191 15.2053L14.6191 15.2053L14.2847 15.8767ZM13.75 14.9531L14.4987 14.9973C14.4996 14.9826 14.5 14.9679 14.5 14.9531H13.75ZM13.75 12.7391C14.1642 12.7391 14.5 12.4033 14.5 11.9891C14.5 11.5749 14.1642 11.2391 13.75 11.2391V12.7391ZM5 11.2391C4.58579 11.2391 4.25 11.5749 4.25 11.9891C4.25 12.4033 4.58579 12.7391 5 12.7391V11.2391ZM14.5 11.9891V9.0251H13V11.9891H14.5ZM14.4987 8.98087C14.4935 8.89368 14.5409 8.81182 14.6191 8.77288L13.9503 7.43023C13.3342 7.73714 12.9607 8.38219 13.0013 9.06934L14.4987 8.98087ZM14.6191 8.77288C14.6973 8.73394 14.7912 8.74542 14.8577 8.80206L15.8303 7.66015C15.3063 7.2138 14.5665 7.12332 13.9503 7.43023L14.6191 8.77288ZM14.8434 8.78961L18.1494 11.7526L19.1506 10.6356L15.8446 7.67259L14.8434 8.78961ZM18.1431 11.7469C18.2108 11.809 18.2494 11.8967 18.2494 11.9886H19.7494C19.7494 11.4763 19.5345 10.9876 19.1569 10.6413L18.1431 11.7469ZM18.2494 11.9886C18.2494 12.0805 18.2108 12.1682 18.1431 12.2303L19.1569 13.3359C19.5345 12.9896 19.7494 12.5009 19.7494 11.9886H18.2494ZM18.1493 12.2247L14.8433 15.1887L15.8447 16.3055L19.1507 13.3415L18.1493 12.2247ZM14.8577 15.1762C14.7912 15.2328 14.6973 15.2443 14.6191 15.2053L13.9503 16.548C14.5665 16.8549 15.3063 16.7644 15.8303 16.3181L14.8577 15.1762ZM14.6191 15.2053C14.5409 15.1664 14.4935 15.0845 14.4987 14.9973L13.0013 14.9089C12.9607 15.596 13.3342 16.2411 13.9503 16.548L14.6191 15.2053ZM14.5 14.9531V11.9891H13V14.9531H14.5ZM13.75 11.2391H5V12.7391H13.75V11.2391Z" fill="currentColor"/></svg> `

export const RankBarChartIcon = `<svg t="1698805193978" fill="currentColor" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6989"><path d="M125.866667 128h85.333333v768h-85.333333zM256 211.2h640v85.333333H256zM253.866667 381.866667h469.333333v85.333333h-469.333333zM256 554.666667h298.666667v85.333333H256zM256 725.333333h170.666667v85.333334h-170.666667z"  p-id="6990"></path></svg>`

export const moreIcon = `<svg t="1698114030704" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9209" fill="currentColor"><path d="M96 522.666667m-96 0a96 96 0 1 0 192 0 96 96 0 1 0-192 0Z" fill="currentColor" p-id="9210"></path><path d="M522.666667 522.666667m-96 0a96 96 0 1 0 192 0 96 96 0 1 0-192 0Z" fill="currentColor" p-id="9211"></path><path d="M928 522.666667m-96 0a96 96 0 1 0 192 0 96 96 0 1 0-192 0Z" fill="currentColor" p-id="9212"></path></svg>`

export const editChartIcon = `<svg t="1698115303766" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18711" fill="currentColor"><path d="M800 960H224c-52.928 0-96-43.072-96-96V224c0-52.928 43.072-96 96-96h448c17.696 0 32 14.336 32 32s-14.304 32-32 32H224c-17.632 0-32 14.368-32 32v640c0 17.664 14.368 32 32 32h576c17.664 0 32-14.336 32-32V352c0-17.664 14.304-32 32-32s32 14.336 32 32v512c0 52.928-43.072 96-96 96zM612 448a31.912 31.912 0 0 1-22.624-9.376c-12.512-12.512-12.512-32.736 0-45.248L907.392 75.36c12.512-12.512 32.736-12.512 45.248 0s12.512 32.736 0 45.248L634.624 438.624C628.384 444.896 620.192 448 612 448z m-132 0H288c-17.664 0-32-14.336-32-32s14.336-32 32-32h192c17.664 0 32 14.336 32 32s-14.336 32-32 32z m192 192H288c-17.664 0-32-14.304-32-32s14.336-32 32-32h384c17.696 0 32 14.304 32 32s-14.304 32-32 32z" fill="currentColor" p-id="18712"></path></svg>`

export const deleteIcon = `<svg t="1698115090611" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="16904" fill="currentColor"><path d="M898 178.7H665.3c4.3-9.8 6.7-20.6 6.7-32 0-44-36-80-80-80H432c-44 0-80 36-80 80 0 11.4 2.4 22.2 6.7 32H126c-13.2 0-24 10.8-24 24s10.8 24 24 24h772c13.2 0 24-10.8 24-24s-10.8-24-24-24z m-466 0c-8.5 0-16.5-3.4-22.6-9.4-6.1-6.1-9.4-14.1-9.4-22.6s3.4-16.5 9.4-22.6c6.1-6.1 14.1-9.4 22.6-9.4h160c8.5 0 16.5 3.4 22.6 9.4 6.1 6.1 9.4 14.1 9.4 22.6 0 8.5-3.4 16.5-9.4 22.6-6.1 6.1-14.1 9.4-22.6 9.4H432zM513 774.7c18.1 0 33-14.8 33-33v-334c0-18.1-14.9-33-33-33h-2c-18.1 0-33 14.8-33 33v334c0 18.2 14.8 33 33 33h2zM363 774.7c18.1 0 33-14.8 33-33v-334c0-18.1-14.9-33-33-33h-2c-18.1 0-33 14.8-33 33v334c0 18.2 14.8 33 33 33h2zM663 774.7c18.1 0 33-14.8 33-33v-334c0-18.1-14.9-33-33-33h-2c-18.1 0-33 14.8-33 33v334c0 18.2 14.8 33 33 33h2z" p-id="16905"></path><path d="M812 280.7c-13.3 0-24 10.7-24 24v530c0 41.9-34.1 76-76 76H312c-41.9 0-76-34.1-76-76v-530c0-13.3-10.7-24-24-24s-24 10.7-24 24v530c0 68.4 55.6 124 124 124h400c68.4 0 124-55.6 124-124v-530c0-13.2-10.7-24-24-24z" p-id="16906"></path></svg>`

export const kpiDataIcon = `<svg t="1698394419153" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="26571"><path d="M512 85.333333c235.637333 0 426.666667 191.029333 426.666667 426.666667S747.637333 938.666667 512 938.666667 85.333333 747.637333 85.333333 512 276.362667 85.333333 512 85.333333z m214.592 318.677334a32 32 0 0 0-45.248 0.064L544.736 541.066667l-81.792-89.109334a32 32 0 0 0-46.613333-0.576l-119.36 123.733334a32 32 0 1 0 46.058666 44.437333l95.754667-99.264 81.418667 88.704a32 32 0 0 0 46.24 0.96l160.213333-160.693333a32 32 0 0 0-0.064-45.248z" fill="currentColor" p-id="26572"></path></svg>`

export const userIcon = `<svg t="1700445411108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2694"><path d="M512 625.777778c-159.288889 0-284.444444-125.155556-284.444444-284.444445s125.155556-284.444444 284.444444-284.444444 284.444444 125.155556 284.444444 284.444444-125.155556 284.444444-284.444444 284.444445z m0-56.888889c125.155556 0 227.555556-102.4 227.555556-227.555556s-102.4-227.555556-227.555556-227.555555-227.555556 102.4-227.555556 227.555555 102.4 227.555556 227.555556 227.555556z" fill="currentColor" p-id="2695"></path><path d="M56.888889 1024c0-250.311111 204.8-455.111111 455.111111-455.111111s455.111111 204.8 455.111111 455.111111h-56.888889c0-221.866667-176.355556-398.222222-398.222222-398.222222s-398.222222 176.355556-398.222222 398.222222H56.888889z" fill="currentColor" p-id="2696"></path></svg>`

export const adminUserIcon = `<svg t="1700445521383" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2849"><path d="M512 568.888889c-159.288889 0-284.444444-125.155556-284.444444-284.444445s125.155556-284.444444 284.444444-284.444444 284.444444 125.155556 284.444444 284.444444-125.155556 284.444444-284.444444 284.444445z m0-56.888889c125.155556 0 227.555556-102.4 227.555556-227.555556s-102.4-227.555556-227.555556-227.555555-227.555556 102.4-227.555556 227.555555 102.4 227.555556 227.555556 227.555556zM824.888889 1024c-79.644444 0-142.222222-62.577778-142.222222-142.222222s62.577778-142.222222 142.222222-142.222222 142.222222 62.577778 142.222222 142.222222-62.577778 142.222222-142.222222 142.222222z m0-56.888889c45.511111 0 85.333333-39.822222 85.333333-85.333333s-39.822222-85.333333-85.333333-85.333334-85.333333 39.822222-85.333333 85.333334 39.822222 85.333333 85.333333 85.333333z" fill="currentColor" p-id="2850"></path><path d="M796.444444 455.111111h56.888889v284.444445h-56.888889zM853.333333 455.111111h113.777778v56.888889h-113.777778zM853.333333 568.888889h113.777778v56.888889h-113.777778z" fill="currentColor" p-id="2851"></path><path d="M512 512l-51.2 62.577778C261.688889 597.333333 113.777778 762.311111 113.777778 967.111111H56.888889v-56.888889h5.688889c28.444444-221.866667 216.177778-398.222222 449.422222-398.222222z m0 0l-51.2 62.577778C261.688889 597.333333 113.777778 762.311111 113.777778 967.111111H56.888889v-56.888889h5.688889c28.444444-221.866667 216.177778-398.222222 449.422222-398.222222z" fill="currentColor" p-id="2852"></path></svg>`

export const adminRoleIcon = `<svg t="1702951804711" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13796"><path d="M475.428571 541.257143c65.828571 0 131.657143-29.257143 182.857143-73.142857 51.2-51.2 73.142857-109.714286 73.142857-175.542857s-21.942857-138.971429-73.142857-182.857143C607.085714 58.514286 548.571429 36.571429 475.428571 36.571429c-65.828571 0-131.657143 29.257143-182.857142 73.142857-43.885714 43.885714-73.142857 109.714286-73.142858 175.542857s29.257143 131.657143 73.142858 175.542857c51.2 51.2 117.028571 80.457143 182.857142 80.457143z m0-409.6c87.771429 0 153.6 65.828571 153.6 153.6S563.2 438.857143 475.428571 438.857143c-80.457143 0-153.6-65.828571-153.6-153.6s73.142857-153.6 153.6-153.6zM928.914286 614.4c21.942857-7.314286 36.571429-29.257143 36.571428-51.2 0-29.257143-21.942857-51.2-51.2-51.2h-65.828571v-14.628571h65.828571c29.257143 0 51.2-21.942857 51.2-51.2 0-29.257143-21.942857-51.2-51.2-51.2h-117.028571c-29.257143 0-51.2 21.942857-51.2 51.2v226.742857c-65.828571 21.942857-117.028571 80.457143-117.028572 153.6 0 87.771429 73.142857 160.914286 168.228572 160.914285s168.228571-73.142857 168.228571-160.914285c0-73.142857-43.885714-131.657143-117.028571-153.6v-58.514286h80.457143z m-65.828572 212.114286c0 36.571429-29.257143 65.828571-65.828571 65.828571s-65.828571-29.257143-65.828572-65.828571 29.257143-65.828571 65.828572-65.828572 65.828571 29.257143 65.828571 65.828572z" fill="currentColor" p-id="13797"></path><path d="M687.542857 607.085714c0-29.257143-21.942857-51.2-51.2-51.2h-365.714286c-51.2 7.314286-102.4 21.942857-146.285714 58.514286-43.885714 36.571429-65.828571 87.771429-65.828571 146.285714v146.285715c0 29.257143 21.942857 51.2 51.2 51.2s51.2-21.942857 51.2-51.2v-146.285715c0-58.514286 51.2-102.4 117.028571-102.4h365.714286c21.942857 0 43.885714-21.942857 43.885714-51.2z" fill="currentColor" p-id="13798"></path></svg>`

export const normalRoleIcon = `<svg t="1702954254156" class="icon" viewBox="0 0 1026 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20649" fill="currentColor"><path d="M392.920908 608.305717a224.526233 224.526233 0 1 1 224.526234-224.526234 224.526233 224.526233 0 0 1-224.526234 224.526234z m0-384.902115a160.375881 160.375881 0 1 0 160.375881 160.375881 160.375881 160.375881 0 0 0-160.375881-160.375881z" p-id="20650"></path><path d="M713.67267 864.907126h-64.150352a256.60141 256.60141 0 0 0-513.202819 0h-64.150353a320.751762 320.751762 0 0 1 641.503524 0zM665.559906 576.23054v-64.150352a144.338293 144.338293 0 1 0-38.009084-283.704933l-16.839467-61.905091a208.488645 208.488645 0 1 1 54.848551 409.760376z" p-id="20651"></path><path d="M954.236492 800.756774h-64.150353a224.526233 224.526233 0 0 0-224.526233-224.526234v-64.150352a288.676586 288.676586 0 0 1 288.676586 288.676586z" p-id="20652" fill="currentColor"></path></svg>`

export const environmentIcon = `<svg t="1700447151011" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8605"><path d="M833.54112 96.96256h-643.072c-44.3904 0-80.384 35.9936-80.384 80.384v442.112c0 44.41088 35.9936 80.384 80.384 80.384h221.05088s20.10112 120.576-60.28288 120.576H311.04a40.18176 40.18176 0 0 0-40.192 40.19712 40.18176 40.18176 0 0 0 40.192 40.192h401.92a40.20736 40.20736 0 0 0 40.19712-40.192 40.21248 40.21248 0 0 0-40.19712-40.19712h-40.192c-80.384 0-60.288-120.576-60.288-120.576h221.06112c44.3904 0 80.37888-35.97312 80.37888-80.384v-442.112c0-44.3904-35.98848-80.384-80.37888-80.384z m-8.38144 501.9904c0 10.0864-8.34048 18.28864-18.62656 18.28864H210.95424c-10.24512 0-18.60608-8.20736-18.60608-18.28864V195.97312c0-10.09664 8.36096-18.31936 18.60608-18.31936h595.57888c10.28608 0 18.62656 8.22272 18.62656 18.31936v402.97984z" fill="currentColor" p-id="8606"></path></svg>`

export const llmIcon = `<svg t="1700401321477" fill="currentColor" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7312" ><path d="M833.536 307.2a22.1184 22.1184 0 0 0-13.4144-19.5584L475.4432 114.9952a19.7632 19.7632 0 0 0-17.6128 0L113.3568 287.3344C100.5568 295.7312 102.4 314.368 102.4 316.3136V684.032a19.8656 19.8656 0 0 0 9.5232 16.7936s337.1008 203.6736 342.9376 206.2336a25.088 25.088 0 0 0 24.7808 1.4336l112.64-64.9216c9.6256-5.5296 10.24-14.1312 9.1136-28.7744s-14.0288-22.6304-23.4496-17.2032-83.6608 50.0736-83.6608 50.0736V512l296.96-173.056 30.72-18.944A24.576 24.576 0 0 0 833.536 307.2zM440.32 843.5712L153.6 668.8768V347.4432L440.32 512v331.4688z m26.2144-385.2288L194.56 303.9232l272.4864-136.192 272.384 136.192z m448.1024 271.5648l-153.088 92.16a15.6672 15.6672 0 0 1-17.6128-0.7168c-0.9216 0-152.4736-91.136-152.4736-91.136a14.6432 14.6432 0 0 1-7.168-12.5952V552.96a20.48 20.48 0 0 1 13.6192-18.8416l148.48-81.92a15.2576 15.2576 0 0 1 13.0048 0l148.7872 81.92h-1.2288a3.4816 3.4816 0 0 1 1.4336 0l3.8912 1.9456a14.6432 14.6432 0 0 1 9.3184 12.6976V716.8a15.1552 15.1552 0 0 1-6.9632 12.6976zM641.6384 549.9904l110.4896 62.0544 112.0256-62.0544-112.0256-61.44zM774.144 774.656l112.64-68.096V583.68l-112.64 64z m-40.0384-1.9456V648.6016l-110.7968-62.6688v120.0128l110.7968 66.7648z m0 0" p-id="7313"></path></svg>`

export const grantIcon = `<svg t="1700561721407" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4101" fill="currentColor"><path d="M650.666667 644.266667c23.466667-12.8 51.2-21.333333 81.066666-21.333334 2.133333 0 4.266667-4.266667 2.133334-6.4-29.866667-27.733333-66.133333-49.066667-104.533334-66.133333h-2.133333c61.866667-44.8 102.4-117.333333 102.4-200.533333C729.6 213.333333 618.666667 102.4 482.133333 102.4S234.666667 213.333333 234.666667 349.866667c0 83.2 40.533333 155.733333 102.4 200.533333h-2.133334c-44.8 19.2-85.333333 46.933333-119.466666 81.066667-34.133333 34.133333-61.866667 74.666667-81.066667 119.466666-19.2 44.8-27.733333 89.6-29.866667 138.666667 0 4.266667 4.266667 8.533333 8.533334 8.533333h59.733333c4.266667 0 8.533333-4.266667 8.533333-8.533333 2.133333-76.8 32-149.333333 87.466667-204.8 55.466667-57.6 132.266667-87.466667 211.2-87.466667 57.6 0 110.933333 14.933333 157.866667 44.8 6.4 4.266667 8.533333 4.266667 12.8 2.133334z m-166.4-121.6c-44.8 0-89.6-17.066667-121.6-51.2-32-32-51.2-74.666667-51.2-121.6s17.066667-89.6 51.2-121.6 74.666667-51.2 121.6-51.2 89.6 17.066667 121.6 51.2c32 32 51.2 74.666667 51.2 121.6s-17.066667 89.6-51.2 121.6c-34.133333 34.133333-76.8 51.2-121.6 51.2z" p-id="4102"></path><path d="M923.733333 603.733333l-40.533333-40.533333c-2.133333-2.133333-10.666667-2.133333-14.933333 4.266667L635.733333 797.866667l-89.6-89.6c-2.133333-2.133333-6.4-2.133333-10.666666 2.133333l-40.533334 40.533333c-2.133333 2.133333-4.266667 8.533333-2.133333 10.666667l93.866667 93.866667 40.533333 40.533333c2.133333 2.133333 6.4 2.133333 10.666667-2.133333l40.533333-40.533334 241.066667-234.666666c4.266667-6.4 6.4-12.8 4.266666-14.933334z" p-id="4103"></path></svg>`

export const tianhongIcon = `<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
viewBox="0 0 129.7 55.3" style="enable-background:new 0 0 129.7 55.3;" xml:space="preserve">
<style type="text/css">
.st0{fill:#E60012;}
</style>
<path d="M104.5,19c-0.6,0.7-1,1.3-1.4,1.8c-1.6-0.7-3-1.6-4.3-2.7v1.4h-6.1v2.7H103V24H80.4v-1.7h10.4v-2.7h-6.2v-1.4
c-1.4,1.1-2.8,2.1-4.2,3c-0.3-0.5-0.8-1-1.3-1.6c2.6-1.3,4.9-2.9,6.8-4.9h-6.5V13h5.1V4.4h-4.3V2.8h4.3V0h1.9v2.8h10.9V0h1.9v2.8
h4.3v1.7h-4.3V13h5v1.7h-6.7C99.4,16.6,101.7,18,104.5,19L104.5,19z M90.8,15.5h1.9V18h5.9c-1.2-1-2.3-2.1-3.3-3.3h-7
C87.1,15.9,86,17,84.8,18h5.9V15.5z M86.3,6.2h10.9V4.4H86.3V6.2z M86.3,9.6h10.9V7.8H86.3V9.6z M86.3,13h10.9v-1.8H86.3V13z"/>
<path d="M105.8,22.1h10.3v-6.2h-9V14h9v-3.6h-6.5V8.7c-1.2,0.8-2.5,1.5-3.9,2.3c-0.4-0.6-0.8-1.3-1.3-1.9C110,6.4,114,3.4,116.3,0
h2.7l-0.6,0.7c2.7,3.4,6.4,6.1,11.3,8c-0.5,0.6-0.9,1.3-1.3,2c-1.3-0.6-2.6-1.2-3.8-2v1.7h-6.5V14h8.9v1.8h-8.9v6.2h10.3v1.9h-22.6
L105.8,22.1L105.8,22.1z M108.3,17.4l1.5-1.2c1.2,1.2,2.4,2.6,3.6,3.9l-1.7,1.4C110.6,20.2,109.5,18.8,108.3,17.4L108.3,17.4z
M117.1,2.2c-1.7,2.2-4.2,4.3-7.5,6.5h14.7C121.8,7,119.4,4.8,117.1,2.2z M120.7,20.3c1.4-1.4,2.5-2.8,3.4-4.2l1.8,1.1
c-1.2,1.5-2.4,2.9-3.7,4.2L120.7,20.3z"/>
<path d="M81.3,32.4v-4.8H79v-0.6h5.6v0.6h-2.3v4.8H81.3z"/>
<path d="M85.5,32.4v-5.4h0.9v5.4H85.5z"/>
<path d="M87.1,32.4l2.7-5.4h1l2.9,5.4h-1.1l-0.8-1.7h-2.9L88,32.4H87.1z M89.1,30.1h2.4l-0.7-1.5c-0.2-0.5-0.4-0.8-0.5-1.1
c-0.1,0.3-0.2,0.7-0.4,1L89.1,30.1z"/>
<path d="M94.2,32.4v-5.4h0.9l3.7,4.3v-4.3h0.9v5.4h-0.9l-3.7-4.3v4.3H94.2z"/>
<path d="M101.1,32.4v-5.4h0.9v2.2h3.6v-2.2h0.9v5.4h-0.9v-2.6H102v2.6H101.1z"/>
<path d="M107.6,29.7c0-0.9,0.3-1.6,0.9-2.1c0.6-0.5,1.4-0.8,2.4-0.8c0.6,0,1.2,0.1,1.8,0.4s0.9,0.6,1.2,1s0.4,0.9,0.4,1.5
c0,0.6-0.1,1-0.4,1.5c-0.3,0.4-0.7,0.8-1.2,1s-1.1,0.3-1.7,0.3c-0.7,0-1.2-0.1-1.8-0.4c-0.5-0.2-0.9-0.6-1.2-1
C107.8,30.6,107.6,30.2,107.6,29.7L107.6,29.7z M108.6,29.7c0,0.7,0.2,1.2,0.7,1.6s1,0.6,1.7,0.6c0.7,0,1.3-0.2,1.7-0.6
s0.7-0.9,0.7-1.6c0-0.4-0.1-0.8-0.3-1.2s-0.5-0.6-0.9-0.8c-0.4-0.2-0.8-0.3-1.2-0.3c-0.7,0-1.2,0.2-1.7,0.5
C108.8,28.3,108.6,28.9,108.6,29.7z"/>
<path d="M115.3,32.4v-5.4h0.9l3.7,4.3v-4.3h0.9v5.4h-0.9l-3.7-4.3v4.3H115.3z"/>
<path d="M125.4,30.2v-0.6h3v2c-0.5,0.3-0.9,0.5-1.4,0.6c-0.5,0.1-1,0.2-1.5,0.2c-0.7,0-1.3-0.1-1.9-0.3c-0.6-0.2-1-0.6-1.3-1
s-0.4-0.9-0.4-1.4s0.1-1,0.4-1.5c0.3-0.5,0.7-0.8,1.2-1c0.5-0.2,1.1-0.3,1.9-0.3c0.5,0,1,0.1,1.4,0.2s0.7,0.3,1,0.5s0.4,0.5,0.5,0.9
l-0.8,0.2c-0.1-0.3-0.2-0.5-0.4-0.7s-0.4-0.3-0.7-0.4c-0.3-0.1-0.6-0.2-1-0.2c-0.4,0-0.8,0.1-1.1,0.2s-0.6,0.2-0.8,0.4
s-0.3,0.3-0.4,0.5c-0.2,0.3-0.3,0.7-0.3,1.1c0,0.5,0.1,0.9,0.3,1.2c0.2,0.3,0.5,0.6,0.9,0.7s0.9,0.2,1.3,0.2c0.4,0,0.8-0.1,1.2-0.2
s0.7-0.2,0.9-0.4v-1L125.4,30.2z"/>
<path d="M80.4,36.6l1.3-2.8h0.5l1.4,2.8H83l-0.4-0.8h-1.4l-0.4,0.8H80.4z M81.3,35.5h1.1l-0.3-0.8c-0.1-0.2-0.2-0.4-0.2-0.6
c0,0.2-0.1,0.4-0.2,0.5L81.3,35.5z"/>
<path d="M83.6,35.7l0.4,0c0,0.1,0.1,0.2,0.1,0.3c0.1,0.1,0.2,0.2,0.3,0.2s0.3,0.1,0.5,0.1c0.2,0,0.3,0,0.4-0.1s0.2-0.1,0.3-0.2
s0.1-0.2,0.1-0.2c0-0.1,0-0.2-0.1-0.2c-0.1-0.1-0.2-0.1-0.3-0.2c-0.1,0-0.3-0.1-0.6-0.1s-0.5-0.1-0.6-0.2c-0.2-0.1-0.3-0.2-0.4-0.3
c-0.1-0.1-0.1-0.2-0.1-0.3c0-0.1,0.1-0.3,0.1-0.4c0.1-0.1,0.2-0.2,0.4-0.3c0.2-0.1,0.4-0.1,0.6-0.1c0.2,0,0.5,0,0.6,0.1
s0.3,0.2,0.4,0.3c0.1,0.1,0.1,0.3,0.2,0.4l-0.4,0c0-0.2-0.1-0.3-0.2-0.4s-0.3-0.1-0.6-0.1c-0.3,0-0.5,0-0.6,0.1
c-0.1,0.1-0.2,0.2-0.2,0.3c0,0.1,0,0.2,0.1,0.2c0.1,0.1,0.3,0.1,0.7,0.2c0.4,0.1,0.6,0.1,0.7,0.2c0.2,0.1,0.3,0.2,0.4,0.3
s0.1,0.2,0.1,0.4s-0.1,0.3-0.1,0.4s-0.2,0.2-0.4,0.3c-0.2,0.1-0.4,0.1-0.6,0.1c-0.3,0-0.6,0-0.8-0.1s-0.4-0.2-0.5-0.3
C83.6,36.1,83.6,35.9,83.6,35.7L83.6,35.7z"/>
<path d="M86.4,35.7l0.4,0c0,0.1,0.1,0.2,0.1,0.3s0.2,0.2,0.3,0.2s0.3,0.1,0.5,0.1c0.2,0,0.3,0,0.4-0.1s0.2-0.1,0.3-0.2
s0.1-0.2,0.1-0.2c0-0.1,0-0.2-0.1-0.2c-0.1-0.1-0.2-0.1-0.3-0.2c-0.1,0-0.3-0.1-0.6-0.1s-0.5-0.1-0.6-0.2c-0.2-0.1-0.3-0.2-0.4-0.3
c-0.1-0.1-0.1-0.2-0.1-0.3c0-0.1,0.1-0.3,0.1-0.4s0.2-0.2,0.4-0.3c0.2-0.1,0.4-0.1,0.6-0.1c0.2,0,0.5,0,0.6,0.1s0.3,0.2,0.4,0.3
c0.1,0.1,0.1,0.3,0.2,0.4l-0.4,0c0-0.2-0.1-0.3-0.2-0.4s-0.3-0.1-0.6-0.1c-0.3,0-0.5,0-0.6,0.1s-0.2,0.2-0.2,0.3
c0,0.1,0,0.2,0.1,0.2c0.1,0.1,0.3,0.1,0.7,0.2s0.6,0.1,0.7,0.2c0.2,0.1,0.3,0.2,0.4,0.3s0.1,0.2,0.1,0.4s-0.1,0.3-0.1,0.4
s-0.2,0.2-0.4,0.3c-0.2,0.1-0.4,0.1-0.6,0.1c-0.3,0-0.6,0-0.8-0.1s-0.4-0.2-0.5-0.3C86.5,36.1,86.4,35.9,86.4,35.7L86.4,35.7z"/>
<path d="M89.4,36.6v-2.8h2.4v0.3h-1.9V35h1.8v0.3h-1.8v0.9h2v0.3L89.4,36.6z"/>
<path d="M93.2,36.6v-2.4h-1.1v-0.3h2.6v0.3h-1.1v2.4H93.2z"/>
<path d="M96.7,36.6v-2.8h0.7l0.8,2c0.1,0.2,0.1,0.3,0.2,0.4c0-0.1,0.1-0.2,0.2-0.4l0.8-1.9h0.6v2.8h-0.4v-2.3l-1,2.3h-0.4l-0.9-2.4
v2.4H96.7z"/>
<path d="M100.1,36.6l1.3-2.8h0.5l1.4,2.8h-0.5l-0.4-0.8h-1.4l-0.4,0.8H100.1z M101,35.5h1.1l-0.3-0.8c-0.1-0.2-0.2-0.4-0.2-0.6
c0,0.2-0.1,0.4-0.2,0.5L101,35.5z"/>
<path d="M103.4,36.6v-2.8h0.4l1.7,2.2v-2.2h0.4v2.8h-0.4l-1.7-2.2v2.2H103.4z"/>
<path d="M106.2,36.6l1.3-2.8h0.5l1.4,2.8h-0.5l-0.4-0.8h-1.4l-0.4,0.8H106.2z M107.2,35.5h1.1l-0.3-0.8c-0.1-0.2-0.2-0.4-0.2-0.6
c0,0.2-0.1,0.4-0.2,0.5L107.2,35.5z"/>
<path d="M111.1,35.5v-0.3h1.4v1c-0.2,0.1-0.4,0.2-0.7,0.3c-0.2,0.1-0.5,0.1-0.7,0.1c-0.3,0-0.6-0.1-0.9-0.2
c-0.3-0.1-0.5-0.3-0.6-0.5s-0.2-0.5-0.2-0.7c0-0.3,0.1-0.5,0.2-0.8s0.3-0.4,0.6-0.5s0.5-0.2,0.9-0.2c0.2,0,0.4,0,0.6,0.1
c0.2,0.1,0.3,0.2,0.4,0.3c0.1,0.1,0.2,0.3,0.2,0.4l-0.4,0.1c-0.1-0.1-0.1-0.2-0.2-0.3c-0.1-0.1-0.2-0.2-0.3-0.2s-0.3-0.1-0.4-0.1
c-0.2,0-0.4,0-0.5,0.1c-0.1,0.1-0.3,0.1-0.4,0.2s-0.2,0.2-0.2,0.3c-0.1,0.2-0.1,0.4-0.1,0.6c0,0.2,0.1,0.4,0.1,0.6
c0.1,0.2,0.2,0.3,0.4,0.4s0.4,0.1,0.6,0.1c0.2,0,0.4,0,0.6-0.1c0.2-0.1,0.3-0.1,0.4-0.2v-0.5L111.1,35.5L111.1,35.5z"/>
<path d="M113,36.6v-2.8h2.4v0.3h-1.9V35h1.8v0.3h-1.8v0.9h2v0.3L113,36.6z"/>
<path d="M116.1,36.6v-2.8h0.6l0.8,2c0.1,0.2,0.1,0.3,0.2,0.4c0-0.1,0.1-0.2,0.2-0.4l0.8-1.9h0.6v2.8h-0.4v-2.3l-1,2.3h-0.4l-1-2.4
v2.4H116.1z"/>
<path d="M119.8,36.6v-2.8h2.4v0.3h-1.9V35h1.8v0.3h-1.8v0.9h2v0.3L119.8,36.6z"/>
<path d="M122.8,36.6v-2.8h0.4L125,36v-2.2h0.4v2.8h-0.4l-1.7-2.2v2.2H122.8z"/>
<path d="M126.8,36.6v-2.4h-1.1v-0.3h2.6v0.3h-1.1v2.4H126.8z"/>
<path class="st0" d="M45.5,18.4l0.7-5.7H55v1.4h2.8V0H42.3v2.8H55v7.1H43.6l-1.3,11.3h11.3c-0.1,3.1-0.3,6.1-0.6,8.9
c-0.2,2.2-1.3,3.5-3.3,3.5c-1.1,0-3-0.1-5.7-0.2c0.3,1.1,0.5,2.2,0.6,3.2c2.5,0,4.3,0,5.4,0c3.7,0,5.7-2.3,6-6.5
c0.3-5,0.6-9,0.7-11.8H45.5z M71.6,15.2l-2.8,1.1c1.8,4.8,3.6,9.8,5.3,15c-3.5,0.3-7.8,0.7-13.1,1.1c3.2-8.7,6.4-19.3,9.6-31.8
L66.9,0c-1.8,8.7-4.5,18.3-8,28.9c-0.7,2.1-1.6,3.5-2.5,4.1l1.3,3.5c0.9-0.3,1.8-0.5,2.8-0.7c4.3-0.5,9.1-1,14.5-1.6
c0.5,1.5,0.3,0.8,0.7,2.3l3.2-1.5C77.2,30.1,75.2,24.9,71.6,15.2z"/>
<path class="st0" d="M39.5,0H0v36.7h39.5L39.5,0z M34.9,34.5c-7.9-3.2-12.5-7.9-15-15.2C18,26,13.3,31,4.6,35
c-0.5-0.7-1.4-1.6-2.4-2.7c9.1-3.8,14-9,15.5-15.9H3v-2.8h15.1c0.1-1.7,0.2-4.2,0.2-8.4H4.3V2.3h31.4v2.8H21.5c0,2.5-0.1,5-0.2,8.4
H37v2.8H21.8c2.3,7.6,7,12.5,15.6,15.1C36.4,32.7,35.6,33.7,34.9,34.5z"/>
</svg>
`

export const attrAnalysisIcon = `<svg t="1702612494523" class="icon" viewBox="0 0 1026 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12220" ><path d="M514.56 1024a512 512 0 1 1 512-512 23.272727 23.272727 0 0 1-46.545455 0 465.454545 465.454545 0 1 0-465.454545 465.454545 23.272727 23.272727 0 1 1 0 46.545455z" fill="currentColor" p-id="12221"></path><path d="M478.487273 660.014545a23.272727 23.272727 0 0 1-22.109091-16.98909l-81.454546-288.349091-76.8 166.981818A23.272727 23.272727 0 0 1 276.712727 535.272727H23.272727a23.272727 23.272727 0 0 1 0-46.545454h238.545455l97.047273-210.734546a23.272727 23.272727 0 0 1 23.272727-13.498182 23.272727 23.272727 0 0 1 20.596363 16.872728l77.381819 273.92 42.356363-134.865455a23.272727 23.272727 0 0 1 21.410909-16.290909A23.272727 23.272727 0 0 1 565.876364 418.909091L593.454545 488.727273h190.021819a23.272727 23.272727 0 0 1 0 46.545454H577.047273a23.272727 23.272727 0 0 1-21.643637-14.894545l-8.843636-23.272727L500.363636 643.607273a23.272727 23.272727 0 0 1-21.876363 16.407272zM710.749091 895.418182a143.709091 143.709091 0 1 1 140.567273-174.545455 143.709091 143.709091 0 0 1-108.916364 171.054546 145.105455 145.105455 0 0 1-31.650909 3.490909z m0-240.989091a97.163636 97.163636 0 1 0 94.836364 75.985454 96.814545 96.814545 0 0 0-94.487273-75.985454z" fill="currentColor" p-id="12222"></path><path d="M767.488363 834.68453m22.098882-26.722455l0 0q22.098882-26.722455 48.821337-4.623572l167.329197 138.377566q26.722455 22.098882 4.623572 48.821337l0 0q-22.098882 26.722455-48.821337 4.623572l-167.329197-138.377566q-26.722455-22.098882-4.623572-48.821337Z" fill="currentColor" p-id="12223"></path></svg>`

export const metricIcon = `<svg t="1702954507724" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6556"><path d="M512 512c68.266667 0 119.466667 56.888889 119.466667 125.155556s-56.888889 125.155556-119.466667 125.155555-119.466667-56.888889-119.466667-125.155555S443.733333 512 512 512m0-51.2c-91.022222 0-170.666667 79.644444-170.666667 176.355556 0 96.711111 73.955556 176.355556 170.666667 176.355555s170.666667-79.644444 170.666667-176.355555c0-96.711111-79.644444-176.355556-170.666667-176.355556" fill="currentColor" p-id="6557"></path><path d="M870.4 460.8c28.444444 56.888889 39.822222 113.777778 39.822222 176.355556s-11.377778 125.155556-39.822222 176.355555l51.2 22.755556c28.444444-62.577778 45.511111-130.844444 45.511111-204.8 0-68.266667-17.066667-136.533333-45.511111-199.111111l-51.2 28.444444zM147.911111 802.133333c-22.755556-51.2-34.133333-108.088889-34.133333-164.977777C113.777778 409.6 295.822222 227.555556 512 227.555556c68.266667 0 130.844444 17.066667 187.733333 45.511111l22.755556-45.511111c-62.577778-34.133333-136.533333-56.888889-210.488889-56.888889-250.311111 0-455.111111 204.8-455.111111 460.8 0 68.266667 11.377778 130.844444 39.822222 187.733333l51.2-17.066667zM865.678222 338.602667l-218.737778 223.687111-40.675555-39.765334 218.737778-223.744z" fill="currentColor" p-id="6558"></path></svg>`

export const htmlIcon = `<svg t="1699512712726" class="icon" viewBox="0 0 1024 1024" version="1.1" fill="currentColor" xmlns="http://www.w3.org/2000/svg" p-id="10901"><path d="M192 0h448.1536L960 320v576c0 70.6944-57.3056 128-128 128H192C121.3056 1024 64 966.6944 64 896V128C64 57.3056 121.3056 0 192 0z" fill="#45B7EC" p-id="10902"></path><path d="M232.96 733.8496v-75.392h73.92v75.392h36.3904V552.384h-36.3904v78.016H232.96v-78.016h-36.4032v181.4656h36.4032z m214.7584 0V580.4288h51.84v-28.0448H360.2176v28.032h51.0976v153.4336h36.4032z m108.1728 0v-124.1344l0.7552-0.128 44.864 124.2624h24.6784l44.864-124.0064 0.7552 0.128v123.8784h36.3904V552.384h-47.488l-46.6048 133.7344h-0.7552l-46.1184-133.7344h-47.7312v181.4656h36.3904z m303.36 0v-27.9168h-81.2544V552.384h-36.3904v181.4656h117.6576z" fill="#FFFFFF" opacity=".9" p-id="10903"></path><path d="M281.8944 360.1664c5.632 2.3808 10.0864 1.9968 13.4016-1.152 3.328-3.1488 4.8128-8.384 4.48-15.7056a13.4144 13.4144 0 0 0-3.328-7.7824c-2.048-2.4704-5.5296-4.6336-10.4704-6.5024l-50.0224-19.6608c-2.8928-1.1904-4.352-2.2144-4.352-3.072 0-0.8448 1.4592-1.7792 4.352-2.7904l52.0704-20.6848c8.1664-2.7136 12.2496-8.4992 12.2496-17.3568 0-13.44-6.1184-17.7792-18.3808-13.0176l-81.1648 34.4576a21.2224 21.2224 0 0 0-9.7024 8.0384 21.632 21.632 0 0 0-0.128 22.848c2.1248 3.584 5.5808 6.3872 10.3424 8.4224l80.6528 33.9584z m49.536 59.2128c3.904 1.3568 7.8592 1.5744 11.8528 0.64 4.0064-0.9344 6.7712-3.6224 8.2944-8.0384l68.928-210.8416c1.7024-4.9408 1.4848-8.8448-0.64-11.7376a18.3552 18.3552 0 0 0-9.0624-6.3872c-4.9408-1.7024-9.2288-1.92-12.8896-0.64-3.6608 1.28-6.2592 4.3904-7.7824 9.3184l-68.928 211.3536a9.9456 9.9456 0 0 0-0.384 5.6064 14.016 14.016 0 0 0 6.016 8.4352c1.5232 1.024 3.0464 1.792 4.5824 2.2912z m114.0864-64.064c2.8928 6.464 8.256 8.0768 16.0768 4.8512l80.6656-33.9584c4.7616-2.048 8.2048-4.8512 10.3296-8.4224 2.1376-3.5712 3.2-7.3088 3.2-11.2256 0-4.096-1.0624-7.9104-3.2-11.4944-2.1248-3.5712-5.4016-6.2848-9.8176-8.1664l-81.1776-34.4576c-3.904-1.7024-7.1808-2.048-9.8176-1.024a10.9312 10.9312 0 0 0-6.016 5.888 32 32 0 0 0-2.0352 11.4688c0 6.6432 3.9168 11.328 11.7504 14.0416l52.0704 20.6848c2.8928 1.1904 4.3392 2.1248 4.3392 2.8032 0 0.6784-1.4464 1.7024-4.352 3.072l-50.0224 19.648c-4.928 1.8688-8.4224 4.032-10.4576 6.5024-2.048 2.4704-3.1488 5.0688-3.328 7.7824-0.1664 4.2624 0.4352 8.256 1.792 12.0064z" fill="#9BDFFF" opacity=".9" p-id="10904"></path><path d="M640 0l320 320H768c-70.6944 0-128-57.3056-128-128V0z" fill="#9BDFFF" p-id="10905"></path></svg>`

export const docxIcon = `<svg t="1703232145932" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="22893" fill="currentColor" ><path d="M145.6 0C100.8 0 64 35.2 64 80v862.4C64 987.2 100.8 1024 145.6 1024h732.8c44.8 0 81.6-36.8 81.6-81.6V324.8L657.6 0h-512z" fill="#14A9DA" p-id="22894"></path><path d="M960 326.4v16H755.2s-100.8-20.8-99.2-108.8c0 0 4.8 92.8 97.6 92.8H960z" fill="#0F93D0" p-id="22895"></path><path d="M657.6 0v233.6c0 25.6 17.6 92.8 97.6 92.8H960L657.6 0z" fill="#FFFFFF" p-id="22896"></path><path d="M291.2 862.4h-48c-9.6 0-17.6-8-17.6-17.6v-158.4c0-9.6 8-16 17.6-16h48c60.8 0 99.2 41.6 99.2 96s-38.4 96-99.2 96z m0-171.2h-41.6v148.8h41.6c48 0 75.2-33.6 75.2-73.6 0-41.6-27.2-75.2-75.2-75.2z m232 174.4c-57.6 0-96-43.2-96-99.2s38.4-99.2 96-99.2c56 0 94.4 41.6 94.4 99.2 0 56-38.4 99.2-94.4 99.2z m0-177.6c-43.2 0-70.4 33.6-70.4 78.4 0 44.8 27.2 76.8 70.4 76.8 41.6 0 70.4-32 70.4-76.8S564.8 688 523.2 688z m294.4 6.4c1.6 1.6 3.2 4.8 3.2 8 0 6.4-4.8 11.2-11.2 11.2-3.2 0-6.4-1.6-8-3.2-11.2-14.4-30.4-22.4-48-22.4-41.6 0-73.6 32-73.6 78.4 0 44.8 32 76.8 73.6 76.8 17.6 0 35.2-6.4 48-20.8 1.6-3.2 4.8-4.8 8-4.8 6.4 0 11.2 6.4 11.2 12.8 0 3.2-1.6 4.8-3.2 8-14.4 16-35.2 27.2-64 27.2-56 0-99.2-40-99.2-99.2s43.2-99.2 99.2-99.2c28.8 0 49.6 11.2 64 27.2z" fill="#FFFFFF" p-id="22897"></path></svg>`

export const excelIcon = `<svg t="1703218352006" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="16330" fill="currentColor"><path d="M145.6 0C100.8 0 64 36.8 64 81.6v860.8C64 987.2 100.8 1024 145.6 1024h732.8c44.8 0 81.6-36.8 81.6-81.6V324.8L657.6 0h-512z" fill="#45B058" p-id="16331"></path><path d="M374.4 862.4c-3.2 0-6.4-1.6-8-3.2l-59.2-80-60.8 80c-1.6 1.6-4.8 3.2-8 3.2-6.4 0-11.2-4.8-11.2-11.2 0-1.6 0-4.8 1.6-6.4l62.4-81.6-57.6-78.4c-1.6-1.6-3.2-3.2-3.2-6.4 0-4.8 4.8-11.2 11.2-11.2 4.8 0 8 1.6 9.6 4.8l56 73.6 54.4-73.6c1.6-3.2 4.8-4.8 8-4.8 6.4 0 12.8 4.8 12.8 11.2 0 3.2-1.6 4.8-1.6 6.4l-59.2 76.8 62.4 83.2c1.6 1.6 3.2 4.8 3.2 6.4 0 6.4-6.4 11.2-12.8 11.2z m160-1.6H448c-9.6 0-17.6-8-17.6-17.6V678.4c0-6.4 4.8-11.2 12.8-11.2 6.4 0 11.2 4.8 11.2 11.2v161.6h80c6.4 0 11.2 4.8 11.2 9.6 0 6.4-4.8 11.2-11.2 11.2z m112 3.2c-28.8 0-51.2-9.6-67.2-24-3.2-1.6-3.2-4.8-3.2-8 0-6.4 3.2-12.8 11.2-12.8 1.6 0 4.8 1.6 6.4 3.2 12.8 11.2 32 20.8 54.4 20.8 33.6 0 44.8-19.2 44.8-33.6 0-49.6-113.6-22.4-113.6-89.6 0-32 27.2-54.4 65.6-54.4 24 0 46.4 8 60.8 20.8 3.2 1.6 4.8 4.8 4.8 8 0 6.4-4.8 12.8-11.2 12.8-1.6 0-4.8-1.6-6.4-3.2-14.4-11.2-32-16-49.6-16-24 0-40 11.2-40 30.4 0 43.2 113.6 17.6 113.6 89.6 0 27.2-19.2 56-70.4 56z" fill="#FFFFFF" p-id="16332"></path><path d="M960 326.4v16H755.2s-102.4-20.8-99.2-108.8c0 0 3.2 92.8 96 92.8h208z" fill="#349C42" p-id="16333"></path><path d="M656 0v233.6c0 25.6 19.2 92.8 99.2 92.8H960L656 0z" fill="#FFFFFF" p-id="16334"></path></svg>`

export const exclamationMarkTriangleIcon = `<svg t="1703583433047" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4230" fill="currentColor"><path d="M957.6 872l-432-736c-3.2-5.6-8.8-8-13.6-8s-10.4 2.4-13.6 8l-432 736c-6.4 10.4 1.6 24 13.6 24h864c12 0 20-13.6 13.6-24z m-793.6-40L512 239.2l348 592.8h-696zM480 704h64v64h-64v-64z m0-320h64v256h-64V384z" p-id="4231"></path></svg>`

export const circleInfoIcon = `<svg t="1705036446607" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9376" fill="currentColor"><path d="M512 64a448 448 0 1 0 448 448A448 448 0 0 0 512 64z m0 832a384 384 0 1 1 384-384 384 384 0 0 1-384 384z" p-id="9377"></path><path d="M511.68 333.12m-48 0a48 48 0 1 0 96 0 48 48 0 1 0-96 0Z" p-id="9378"></path><path d="M544 480v-64h-96v64h32v192h-64v64h192v-64h-64v-192z" p-id="9379"></path></svg>`

export const rankIcon = `<svg t="1705367755758" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14065"><path d="M196.864 557.824a27.050667 27.050667 0 0 0-28.629333 28.629333v194.816a27.050667 27.050667 0 0 0 28.629333 28.629334h57.301333a27.050667 27.050667 0 0 0 28.629334-28.629334v-194.816a27.050667 27.050667 0 0 0-28.629334-28.629333z m0-57.301333h57.301333a84.394667 84.394667 0 0 1 85.930667 85.930666v194.816a84.394667 84.394667 0 0 1-85.930667 85.930667H196.864A84.394667 84.394667 0 0 1 110.933333 781.269333v-194.816a84.394667 84.394667 0 0 1 85.930667-85.930666z m286.464-286.464a27.050667 27.050667 0 0 0-28.629333 28.629333v538.581333a27.050667 27.050667 0 0 0 28.629333 28.629334h57.301333a27.050667 27.050667 0 0 0 28.629334-28.629334V242.688a27.050667 27.050667 0 0 0-28.629334-28.629333z m0-57.301334h57.301333a84.394667 84.394667 0 0 1 85.930667 85.930667v538.581333a84.394667 84.394667 0 0 1-85.930667 85.930667h-57.258666a84.394667 84.394667 0 0 1-85.930667-85.930667V242.688a84.394667 84.394667 0 0 1 85.930667-85.930667z m286.464 286.464a27.050667 27.050667 0 0 0-28.629333 28.629334v309.418666a27.050667 27.050667 0 0 0 28.629333 28.629334h57.301333a27.050667 27.050667 0 0 0 28.629334-28.629334v-309.418666a27.050667 27.050667 0 0 0-28.629334-28.629334z m0-57.301333h57.301333a84.394667 84.394667 0 0 1 85.930667 85.930667v309.418666a84.394667 84.394667 0 0 1-85.930667 85.930667h-57.258666a84.394667 84.394667 0 0 1-85.930667-85.930667v-309.418666a84.394667 84.394667 0 0 1 85.930667-85.888z" fill="currentColor" p-id="14066"></path></svg>`

export const historyIcon = `<svg t="1705050684723" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4243" fill="currentColor"><path d="M533.333333 85.333333C384 85.333333 251.733333 166.4 183.466667 290.133333L85.333333 192V469.333333h277.333334L243.2 349.866667C298.666667 243.2 405.333333 170.666667 533.333333 170.666667c174.933333 0 320 145.066667 320 320S708.266667 810.666667 533.333333 810.666667c-140.8 0-256-89.6-302.933333-213.333334H140.8c46.933333 170.666667 204.8 298.666667 392.533333 298.666667 226.133333 0 405.333333-183.466667 405.333334-405.333333S755.2 85.333333 533.333333 85.333333zM469.333333 298.666667v217.6l200.533334 119.466666 34.133333-55.466666-170.666667-102.4V298.666667H469.333333z" opacity=".9" p-id="4244"></path></svg>`

export const emptyCloseIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17 7L7 17" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M7 7L17 17" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>
`
export const closeIcon = `<svg t="1705051260487" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5217" fill="currentColor"><path d="M512 128C300.8 128 128 300.8 128 512s172.8 384 384 384 384-172.8 384-384S723.2 128 512 128zM512 832c-179.2 0-320-140.8-320-320s140.8-320 320-320 320 140.8 320 320S691.2 832 512 832z" p-id="5218"></path><path d="M672 352c-12.8-12.8-32-12.8-44.8 0L512 467.2 396.8 352C384 339.2 364.8 339.2 352 352S339.2 384 352 396.8L467.2 512 352 627.2c-12.8 12.8-12.8 32 0 44.8s32 12.8 44.8 0L512 556.8l115.2 115.2c12.8 12.8 32 12.8 44.8 0s12.8-32 0-44.8L556.8 512l115.2-115.2C684.8 384 684.8 364.8 672 352z" p-id="5219"></path></svg>`

export const noticeBellIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4 14.8851H3.25H4ZM6.73291 18.0834L6.83592 17.3405H6.83592L6.73291 18.0834ZM16.9193 18.0834L16.8163 17.3405H16.8162L16.9193 18.0834ZM6.48914 8.08696C6.48914 5.13944 8.87857 2.75 11.8261 2.75V1.25C8.05015 1.25 4.98914 4.31101 4.98914 8.08696H6.48914ZM6.48914 10.1795V8.08696H4.98914V10.1795H6.48914ZM4.75 14.8851C4.75 14.4037 4.88171 13.9552 5.11062 13.5714L3.82231 12.8031C3.4587 13.4128 3.25 14.1257 3.25 14.8851H4.75ZM6.83592 17.3405C5.63629 17.1742 4.75 16.1313 4.75 14.8851H3.25C3.25 16.8448 4.65411 18.5524 6.6299 18.8263L6.83592 17.3405ZM11.8261 17.772C10.3341 17.772 8.40652 17.5583 6.83592 17.3405L6.6299 18.8263C8.21268 19.0458 10.2254 19.272 11.8261 19.272V17.772ZM16.8162 17.3405C15.2457 17.5583 13.3181 17.772 11.8261 17.772V19.272C13.4268 19.272 15.4395 19.0458 17.0223 18.8263L16.8162 17.3405ZM18.9022 14.8851C18.9022 16.1313 18.0159 17.1742 16.8163 17.3405L17.0223 18.8263C18.9981 18.5524 20.4022 16.8448 20.4022 14.8851H18.9022ZM18.5416 13.5714C18.7705 13.9552 18.9022 14.4037 18.9022 14.8851H20.4022C20.4022 14.1257 20.1935 13.4128 19.8299 12.8031L18.5416 13.5714ZM17.1631 8.08696V10.1795H18.6631V8.08696H17.1631ZM11.8261 2.75C14.7736 2.75 17.1631 5.13944 17.1631 8.08696H18.6631C18.6631 4.31101 15.602 1.25 11.8261 1.25V2.75ZM19.8299 12.8031C19.521 12.2851 19.2404 11.8701 19.0091 11.4066C18.7876 10.9629 18.6631 10.567 18.6631 10.1795H17.1631C17.1631 10.8924 17.3931 11.5278 17.667 12.0766C17.9311 12.6056 18.2869 13.1443 18.5416 13.5714L19.8299 12.8031ZM4.98914 10.1795C4.98914 10.567 4.86462 10.9629 4.64311 11.4066C4.41176 11.8701 4.13121 12.2851 3.82231 12.8031L5.11062 13.5714C5.36531 13.1443 5.7211 12.6056 5.98519 12.0766C6.25912 11.5278 6.48914 10.8923 6.48914 10.1795H4.98914Z" fill="currentColor"/><path d="M14 20.834C13.5326 21.5369 12.7335 22.0002 11.8261 22.0002C10.9187 22.0002 10.1195 21.5369 9.65218 20.834" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/></svg>
`

export const saveIcon = `<svg t="1705371590634" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4242" fill="currentColor"><path d="M708.388571 121.904762L902.095238 320.804571V828.952381a73.142857 73.142857 0 0 1-73.142857 73.142857H195.047619a73.142857 73.142857 0 0 1-73.142857-73.142857V195.047619a73.142857 73.142857 0 0 1 73.142857-73.142857h513.340952zM292.571429 195.023238L195.047619 195.047619v633.904762l97.52381-0.024381V536.380952h438.857142v292.547048L828.952381 828.952381V350.549333l-97.52381-100.156952V365.714286H292.571429V195.023238zM658.285714 609.52381H365.714286v219.40419h292.571428V609.52381z m-48.761904 73.142857v73.142857h-195.04762v-73.142857h195.04762z m48.761904-487.619048H365.714286v97.52381h292.571428V195.047619z" p-id="4243"></path></svg>`

export const editIcon = `<svg t="1705374471798" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4249" fill="currentColor"><path d="M257.7 752c2 0 4-0.2 6-0.5L431.9 722c2-0.4 3.9-1.3 5.3-2.8l423.9-423.9c3.9-3.9 3.9-10.2 0-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2c-1.9 11.1 1.5 21.9 9.4 29.8 6.6 6.4 14.9 9.9 23.8 9.9z m67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z" p-id="4250"></path></svg>`

export const recommendedEditIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Icon Frame">
<path id="Vector 40" d="M5 16L4 20L8 19L19.5858 7.41421C20.3668 6.63316 20.3668 5.36683 19.5858 4.58579L19.4142 4.41421C18.6332 3.63316 17.3668 3.63317 16.5858 4.41421L5 16Z" stroke="#6A58EC" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector 41" d="M15 6L18 9" stroke="#6A58EC" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector 42" d="M13 20H21" stroke="#6A58EC" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</svg>
`

export const downloadReportIcon = `<svg t="1705486009310" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="51789" fill="currentColor"><path d="M1022.955 522.57c0 100.193-81.516 181.699-181.719 181.699H655.6c-11.298 0-20.467-9.169-20.467-20.466 0-11.308 9.17-20.466 20.467-20.466h185.637c77.628 0 140.787-63.148 140.787-140.766 0-77.424-62.841-140.449-140.203-140.766-0.42 0.03-0.819 0.05-1.218 0.061-5.945 0.143-11.686-2.292-15.687-6.703a20.455 20.455 0 0 1-5.168-16.25c1.33-10.806 1.944-19.76 1.944-28.192 0-60.764-23.658-117.885-66.617-160.833-42.969-42.968-100.09-66.617-160.843-66.617-47.369 0-92.742 14.45-131.208 41.782-37.617 26.739-65.953 63.7-81.926 106.884a20.5 20.5 0 0 1-14.828 12.894 20.492 20.492 0 0 1-18.86-5.547c-19.289-19.33-44.943-29.972-72.245-29.972-56.323 0-102.146 45.813-102.146 102.126 0 0.317 0.04 0.982 0.092 1.627 0.061 0.92 0.122 1.831 0.153 2.763a20.466 20.466 0 0 1-15.002 20.455c-32.356 8.934-61.541 28.55-82.181 55.218-21.305 27.517-32.572 60.508-32.572 95.413 0 86.244 70.188 156.423 156.443 156.423h169.981c11.298 0 20.466 9.158 20.466 20.466 0 11.297-9.168 20.466-20.466 20.466H199.951c-108.829 0-197.375-88.536-197.375-197.355 0-44.053 14.224-85.712 41.126-120.474 22.81-29.46 53.898-52.086 88.71-64.816 5.066-74.323 67.15-133.245 142.752-133.245 28.386 0 55.504 8.218 78.651 23.526 19.658-39.868 48.843-74.169 85.498-100.212 45.434-32.296 99.004-49.354 154.918-49.354 71.693 0 139.088 27.916 189.782 78.6 50.695 50.695 78.61 118.09 78.61 189.782 0 3.705-0.102 7.47-0.296 11.37 90.307 10.478 160.628 87.42 160.628 180.48zM629.26 820.712L527.235 922.724c-3.99 4.002-9.23 5.997-14.47 5.997s-10.478-1.995-14.47-5.997L396.273 820.711c-7.992-7.992-7.992-20.947 0-28.94s20.947-8.001 28.94 0l67.087 67.079v-358.7c0-11.297 9.159-20.466 20.466-20.466 11.308 0 20.467 9.169 20.467 20.466v358.7l67.088-67.078c7.992-8.002 20.947-7.992 28.939 0s7.992 20.947 0 28.939z" fill="currentColor" p-id="51790"></path></svg>`

export const fullCircleIcon = `<svg t="1706516045663" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12548" ><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" p-id="12549" fill="#52c41a"></path></svg>`

export const semiCircleIcon = `<svg t="1706516299847" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12894" ><path d="M512 0C229.2 0 0 229.2 0 512c0 272 212.1 494.5 480 511 10.6 0.7 21.3 1 32 1s21.4-0.3 32-1c267.9-16.5 480-239 480-511C1024 229.2 794.8 0 512 0z m-32 941.4c0 9.5-8.3 17-17.8 15.9-42.9-4.7-84.7-15.6-124.6-32.5-53.3-22.6-101.3-54.9-142.4-96-41.2-41.2-73.5-89.1-96-142.4C75.8 631.2 64 572.5 64 512s11.8-119.2 35.2-174.4c22.6-53.3 54.9-101.3 96-142.4 41.2-41.2 89.1-73.5 142.4-96 39.9-16.9 81.7-27.8 124.6-32.5 9.5-1 17.8 6.4 17.8 15.9v858.8z" p-id="12895" fill="#fadb14"></path></svg>`

export const prohibitedCircleIcon = `<svg t="1706516317023" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13156" ><path d="M512 0C229.2 0 0 229.2 0 512c0 272 212.1 494.5 480 511 10.6 0.7 21.3 1 32 1s21.4-0.3 32-1c267.9-16.5 480-239 480-511C1024 229.2 794.8 0 512 0zM337.6 924.8c-53.3-22.6-101.3-54.9-142.4-96-41.2-41.2-73.5-89.1-96-142.4C75.8 631.2 64 572.5 64 512s11.8-119.2 35.2-174.4c22.6-53.3 54.9-101.3 96-142.4 41.2-41.2 89.1-73.5 142.4-96 39.9-16.9 81.7-27.7 124.6-32.5 9.5-1 17.8 6.4 17.8 15.9v858.7c0 9.5-8.3 16.9-17.8 15.9-42.9-4.6-84.6-15.5-124.6-32.4z m491.2-96c-41.2 41.2-89.1 73.5-142.4 96-39.9 16.9-81.7 27.7-124.6 32.5-9.5 1-17.8-6.4-17.8-15.9V82.6c0-9.5 8.3-16.9 17.8-15.9 42.9 4.7 84.7 15.6 124.6 32.5 53.3 22.6 101.3 54.9 142.4 96 41.2 41.2 73.5 89.1 96 142.4C948.2 392.8 960 451.5 960 512s-11.8 119.2-35.2 174.4c-22.5 53.3-54.8 101.2-96 142.4z" p-id="13157" fill="#8c8c8c"></path></svg>`

export const measureOverviewIcon = `<svg viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3_37)"> <path d="M0 0V20H20L0 0ZM12 18V19H10V17H9V19H7V18H6V19H4V17H3V19H1V17H2V16H1V14H3V13H1V11H2V10H1V8H3V7H1V2.41L17.59 19H13V18H12ZM6 11H5V15H9V14H6V11Z" fill="currentColor"/> </g> <defs> <clipPath id="clip0_3_37"> <rect width="20" height="20" fill="currentColor"/> </clipPath> </defs> </svg>`

export const measureManageIcon = `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_4_83)"> <path d="M0 0V20H20L0 0ZM12 18V19H10V17H9V19H7V18H6V19H4V17H3V19H1V17H2V16H1V14H3V13H1V11H2V10H1V8H3V7H1V2.41L17.59 19H13V18H12ZM6 11H5V15H9V14H6V11Z" fill="currentColor"/> <g clip-path="url(#clip1_4_83)"> <path d="M19.1042 4.10416H18.2219C18.149 3.83958 18.0437 3.58854 17.9115 3.35521L18.5354 2.73125C18.6979 2.56875 18.6979 2.30416 18.5354 2.14166L17.8573 1.46354C17.6948 1.30104 17.4302 1.30104 17.2677 1.46354L16.6437 2.0875C16.4104 1.95521 16.1594 1.85 15.8948 1.77708V0.895831C15.8948 0.666664 15.7073 0.479164 15.4781 0.479164H14.5198C14.2906 0.479164 14.1031 0.666664 14.1031 0.895831V1.77812C13.8385 1.85104 13.5875 1.95625 13.3542 2.08854L12.7302 1.46458C12.5677 1.30208 12.3031 1.30208 12.1406 1.46458L11.4625 2.14271C11.3 2.30521 11.3 2.56979 11.4625 2.73229L12.0865 3.35625C11.9542 3.58958 11.849 3.84062 11.776 4.10521H10.8958C10.6667 4.10521 10.4792 4.29271 10.4792 4.52187V5.48021C10.4792 5.70937 10.6667 5.89687 10.8958 5.89687H11.7781C11.851 6.16146 11.9562 6.4125 12.0885 6.64583L11.4646 7.26979C11.3021 7.43229 11.3021 7.69687 11.4646 7.85937L12.1427 8.5375C12.3052 8.7 12.5698 8.7 12.7323 8.5375L13.3562 7.91354C13.5896 8.04583 13.8406 8.15104 14.1052 8.22396V9.10416C14.1052 9.33333 14.2927 9.52083 14.5219 9.52083H15.4802C15.7094 9.52083 15.8969 9.33333 15.8969 9.10416V8.22187C16.1615 8.14896 16.4125 8.04375 16.6458 7.91146L17.2698 8.53541C17.4323 8.69791 17.6969 8.69791 17.8594 8.53541L18.5375 7.85729C18.7 7.69479 18.7 7.43021 18.5375 7.26771L17.9135 6.64375C18.0458 6.41041 18.151 6.15937 18.224 5.89479H19.1042C19.3333 5.89479 19.5208 5.70729 19.5208 5.47812V4.51979C19.5208 4.29166 19.3333 4.10416 19.1042 4.10416ZM15 6.83333C13.9875 6.83333 13.1667 6.0125 13.1667 5C13.1667 3.9875 13.9875 3.16666 15 3.16666C16.0125 3.16666 16.8333 3.9875 16.8333 5C16.8333 6.0125 16.0125 6.83333 15 6.83333Z" fill="currentColor"/> <path d="M19.5392 4.00922H18.5634C18.4827 3.71659 18.3664 3.43894 18.22 3.18088L18.9101 2.49078C19.0899 2.31106 19.0899 2.01843 18.9101 1.83871L18.1601 1.08871C17.9804 0.908986 17.6878 0.908986 17.5081 1.08871L16.818 1.7788C16.5599 1.63249 16.2823 1.51613 15.9896 1.43548V0.460829C15.9896 0.207373 15.7823 -1.08007e-10 15.5288 0H14.4689C14.2154 0 14.0081 0.207373 14.0081 0.460829V1.43664C13.7154 1.51728 13.4378 1.63364 13.1797 1.77995L12.4896 1.08986C12.3099 0.910138 12.0173 0.910138 11.8376 1.08986L11.0876 1.83986C10.9078 2.01959 10.9078 2.31221 11.0876 2.49194L11.7776 3.18203C11.6313 3.44009 11.515 3.71774 11.4343 4.01037H10.4608C10.2074 4.01037 10 4.21774 10 4.4712V5.53111C10 5.78456 10.2074 5.99194 10.4608 5.99194H11.4366C11.5173 6.28456 11.6336 6.56221 11.78 6.82028L11.0899 7.51037C10.9101 7.69009 10.9101 7.98272 11.0899 8.16244L11.8399 8.91244C12.0196 9.09217 12.3122 9.09217 12.4919 8.91244L13.182 8.22235C13.4401 8.36866 13.7177 8.48502 14.0104 8.56567V9.53917C14.0104 9.79263 14.2177 10 14.4712 10H15.5311C15.7846 10 15.9919 9.79263 15.9919 9.53917V8.56336C16.2846 8.48272 16.5622 8.36636 16.8203 8.22005L17.5104 8.91014C17.6901 9.08986 17.9827 9.08986 18.1624 8.91014L18.9124 8.16014C19.0922 7.98042 19.0922 7.68779 18.9124 7.50806L18.2224 6.81797C18.3687 6.55991 18.485 6.28226 18.5657 5.98963H19.5392C19.7926 5.98963 20 5.78226 20 5.5288V4.46889C20 4.21659 19.7926 4.00922 19.5392 4.00922ZM15 7.02765C13.8802 7.02765 12.9724 6.11982 12.9724 5C12.9724 3.88018 13.8802 2.97235 15 2.97235C16.1198 2.97235 17.0277 3.88018 17.0277 5C17.0277 6.11982 16.1198 7.02765 15 7.02765Z" fill="currentColor"/> <path d="M14.0833 5C14.0833 5.24312 14.1799 5.47628 14.3518 5.64818C14.5237 5.82009 14.7569 5.91667 15 5.91667C15.2431 5.91667 15.4763 5.82009 15.6482 5.64818C15.8201 5.47628 15.9167 5.24312 15.9167 5C15.9167 4.75689 15.8201 4.52373 15.6482 4.35182C15.4763 4.17991 15.2431 4.08334 15 4.08334C14.7569 4.08334 14.5237 4.17991 14.3518 4.35182C14.1799 4.52373 14.0833 4.75689 14.0833 5Z" fill="currentColor"/> </g> </g> <defs> <clipPath id="clip0_4_83"> <rect width="20" height="20" fill="currentColor"/> </clipPath> <clipPath id="clip1_4_83"> <rect width="10" height="10" fill="currentColor" transform="translate(10)"/> </clipPath> </defs> </svg>`

export const measureCreateIcon = `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_4_81)"> <path d="M0 0V20H20L0 0ZM12 18V19H10V17H9V19H7V18H6V19H4V17H3V19H1V17H2V16H1V14H3V13H1V11H2V10H1V8H3V7H1V2.41L17.59 19H13V18H12ZM6 11H5V15H9V14H6V11Z" fill="currentColor"/> <path d="M14.375 4.375H11.875V5.625H14.375V8.125H15.625V5.625H18.125V4.375H15.625V1.875H14.375V4.375ZM15 10C13.6739 10 12.4021 9.47322 11.4645 8.53553C10.5268 7.59785 10 6.32608 10 5C10 3.67392 10.5268 2.40215 11.4645 1.46447C12.4021 0.526784 13.6739 0 15 0C16.3261 0 17.5979 0.526784 18.5355 1.46447C19.4732 2.40215 20 3.67392 20 5C20 6.32608 19.4732 7.59785 18.5355 8.53553C17.5979 9.47322 16.3261 10 15 10Z" fill="currentColor"/> </g> <defs> <clipPath id="clip0_4_81"> <rect width="20" height="20" fill="currentColor"/> </clipPath> </defs> </svg>`

export const dimensionOverviewIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.5796 3.03531C3.41982 3.00955 3.25744 3.06272 3.14385 3.17801C3.03025 3.2933 2.97949 3.45644 3.00761 3.61583L4.06828 9.62624C4.10075 9.81028 4.23316 9.96085 4.41154 10.0166C4.58993 10.0723 4.7845 10.0239 4.91599 9.89112L7.05397 7.73162L11.494 12.2001L11.4613 16.0202L8.41172 16.0355C8.2244 16.0364 8.05332 16.142 7.96846 16.309C7.88361 16.476 7.89921 16.6764 8.00889 16.8283L11.5947 21.7928C11.6894 21.924 11.8419 22.0012 12.0037 22C12.1656 21.9988 12.3168 21.9194 12.4096 21.7868L12.5 21.6577V19.9139L11.9936 20.6373L9.38863 17.0306L12.5 17.0151V16.0152L12.4939 16.0152V12.2144L17.0146 7.72912L19.1551 9.89117C19.2866 10.024 19.4811 10.0724 19.6595 10.0166C19.8379 9.96089 19.9703 9.81032 20.0028 9.62628L21.0635 3.61587C21.0916 3.45648 21.0408 3.29334 20.9272 3.17805C20.8136 3.06276 20.6513 3.00959 20.4915 3.03536L14.4455 4.01027C14.2606 4.04009 14.1078 4.17077 14.0497 4.34886C13.9916 4.52694 14.038 4.72255 14.1698 4.85567L16.311 7.01848L12.0025 11.2932L7.75755 7.02096L9.90131 4.85563C10.0331 4.72251 10.0794 4.5269 10.0213 4.34881C9.96327 4.17073 9.81052 4.04004 9.62559 4.01022L3.5796 3.03531ZM8.50687 4.84275L4.88514 8.50093L4.1146 4.1345L8.50687 4.84275ZM19.1859 8.50097L15.5642 4.84279L19.9565 4.13454L19.1859 8.50097Z" fill="currentColor"/>
<path d="M22 15H14V16H22V15Z" fill="currentColor"/>
<path d="M14 18H22V19H14V18Z" fill="currentColor"/>
<path d="M14 21H22V22H14V21Z" fill="currentColor"/>
</svg>`

export const dimensionManageIcon = `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3_47)"> <path fill-rule="evenodd" clip-rule="evenodd" d="M19.219 9.26613L19.3555 9.33963C19.5985 9.47044 19.75 9.72408 19.75 10C19.75 10.2759 19.5985 10.5296 19.3555 10.6604L16.7245 12.0769L19.3555 13.4935C19.5985 13.6243 19.75 13.8779 19.75 14.1538C19.75 14.4297 19.5985 14.6834 19.3556 14.8142L10.3556 19.6604C10.1336 19.7799 9.86641 19.7799 9.64443 19.6604L0.644425 14.8142C0.401488 14.6834 0.249994 14.4297 0.25 14.1538C0.250006 13.8779 0.40151 13.6243 0.644453 13.4935L3.27547 12.0769L0.644453 10.6604C0.401505 10.5296 0.25 10.2759 0.25 10C0.25 9.72408 0.401505 9.47044 0.644453 9.33963L3.27547 7.92308L0.644453 6.50652C0.40151 6.37572 0.250006 6.12209 0.25 5.84617C0.249994 5.57025 0.401488 5.31661 0.644425 5.1858L9.64443 0.339647C9.86641 0.220118 10.1336 0.220118 10.3556 0.339647L10.8909 0.627888C10.482 1.01231 10.1271 1.45343 9.83864 1.93871L2.58201 5.84612L5.21309 7.26271L5.21313 7.26274L10 9.84048L10.8832 9.36487C11.289 9.74778 11.7482 10.0747 12.2485 10.3333L10.3556 11.3526C10.1336 11.4722 9.8664 11.4722 9.6444 11.3526L4.85751 8.77489L2.58207 10L5.21309 11.4166L5.21313 11.4166L10 13.9943L14.7869 11.4166L14.7869 11.4166L15.6192 10.9684C17.0172 10.8251 18.2729 10.2018 19.219 9.26613ZM9.6444 15.5065L4.85751 12.9287L2.58201 14.1539L10 18.1482L17.418 14.1539L15.1425 12.9287L10.3556 15.5065C10.1336 15.626 9.8664 15.626 9.6444 15.5065Z" fill="currentColor"/> <g clip-path="url(#clip1_3_47)"> <path d="M19.1042 4.10417H18.2219C18.149 3.83959 18.0437 3.58855 17.9115 3.35521L18.5354 2.73126C18.6979 2.56876 18.6979 2.30417 18.5354 2.14167L17.8573 1.46355C17.6948 1.30105 17.4302 1.30105 17.2677 1.46355L16.6437 2.08751C16.4104 1.95521 16.1594 1.85001 15.8948 1.77709V0.895838C15.8948 0.666672 15.7073 0.479172 15.4781 0.479172H14.5198C14.2906 0.479172 14.1031 0.666672 14.1031 0.895838V1.77813C13.8385 1.85105 13.5875 1.95626 13.3542 2.08855L12.7302 1.46459C12.5677 1.30209 12.3031 1.30209 12.1406 1.46459L11.4625 2.14271C11.3 2.30521 11.3 2.5698 11.4625 2.7323L12.0865 3.35626C11.9542 3.58959 11.849 3.84063 11.776 4.10521H10.8958C10.6667 4.10521 10.4792 4.29271 10.4792 4.52188V5.48021C10.4792 5.70938 10.6667 5.89688 10.8958 5.89688H11.7781C11.851 6.16146 11.9562 6.41251 12.0885 6.64584L11.4646 7.2698C11.3021 7.4323 11.3021 7.69688 11.4646 7.85938L12.1427 8.53751C12.3052 8.70001 12.5698 8.70001 12.7323 8.53751L13.3562 7.91355C13.5896 8.04584 13.8406 8.15105 14.1052 8.22396V9.10417C14.1052 9.33334 14.2927 9.52084 14.5219 9.52084H15.4802C15.7094 9.52084 15.8969 9.33334 15.8969 9.10417V8.22188C16.1615 8.14896 16.4125 8.04376 16.6458 7.91146L17.2698 8.53542C17.4323 8.69792 17.6969 8.69792 17.8594 8.53542L18.5375 7.8573C18.7 7.6948 18.7 7.43021 18.5375 7.26771L17.9135 6.64376C18.0458 6.41042 18.151 6.15938 18.224 5.8948H19.1042C19.3333 5.8948 19.5208 5.7073 19.5208 5.47813V4.5198C19.5208 4.29167 19.3333 4.10417 19.1042 4.10417ZM15 6.83334C13.9875 6.83334 13.1667 6.01251 13.1667 5.00001C13.1667 3.98751 13.9875 3.16667 15 3.16667C16.0125 3.16667 16.8333 3.98751 16.8333 5.00001C16.8333 6.01251 16.0125 6.83334 15 6.83334Z" fill="currentColor"/> <path d="M19.5392 4.00922H18.5634C18.4827 3.71659 18.3664 3.43894 18.22 3.18088L18.9101 2.49078C19.0899 2.31106 19.0899 2.01843 18.9101 1.83871L18.1601 1.08871C17.9804 0.908986 17.6878 0.908986 17.5081 1.08871L16.818 1.7788C16.5599 1.63249 16.2823 1.51613 15.9896 1.43548V0.460829C15.9896 0.207373 15.7823 -1.08007e-10 15.5288 0H14.4689C14.2154 0 14.0081 0.207373 14.0081 0.460829V1.43664C13.7154 1.51728 13.4378 1.63364 13.1797 1.77995L12.4896 1.08986C12.3099 0.910138 12.0173 0.910138 11.8376 1.08986L11.0876 1.83986C10.9078 2.01959 10.9078 2.31221 11.0876 2.49194L11.7776 3.18203C11.6313 3.44009 11.515 3.71774 11.4343 4.01037H10.4608C10.2074 4.01037 10 4.21774 10 4.4712V5.53111C10 5.78456 10.2074 5.99194 10.4608 5.99194H11.4366C11.5173 6.28456 11.6336 6.56221 11.78 6.82028L11.0899 7.51037C10.9101 7.69009 10.9101 7.98272 11.0899 8.16244L11.8399 8.91244C12.0196 9.09217 12.3122 9.09217 12.4919 8.91244L13.182 8.22235C13.4401 8.36866 13.7177 8.48502 14.0104 8.56567V9.53917C14.0104 9.79263 14.2177 10 14.4712 10H15.5311C15.7846 10 15.9919 9.79263 15.9919 9.53917V8.56336C16.2846 8.48272 16.5622 8.36636 16.8203 8.22005L17.5104 8.91014C17.6901 9.08986 17.9827 9.08986 18.1624 8.91014L18.9124 8.16014C19.0922 7.98042 19.0922 7.68779 18.9124 7.50806L18.2224 6.81797C18.3687 6.55991 18.485 6.28226 18.5657 5.98963H19.5392C19.7926 5.98963 20 5.78226 20 5.5288V4.46889C20 4.21659 19.7926 4.00922 19.5392 4.00922ZM15 7.02765C13.8802 7.02765 12.9724 6.11982 12.9724 5C12.9724 3.88018 13.8802 2.97235 15 2.97235C16.1198 2.97235 17.0277 3.88018 17.0277 5C17.0277 6.11982 16.1198 7.02765 15 7.02765Z" fill="currentColor"/> <path d="M14.0833 4.99999C14.0833 5.24311 14.1799 5.47627 14.3518 5.64818C14.5237 5.82008 14.7569 5.91666 15 5.91666C15.2431 5.91666 15.4763 5.82008 15.6482 5.64818C15.8201 5.47627 15.9167 5.24311 15.9167 4.99999C15.9167 4.75688 15.8201 4.52372 15.6482 4.35181C15.4763 4.17991 15.2431 4.08333 15 4.08333C14.7569 4.08333 14.5237 4.17991 14.3518 4.35181C14.1799 4.52372 14.0833 4.75688 14.0833 4.99999Z" fill="currentColor"/> </g> </g> <defs> <clipPath id="clip0_3_47"> <rect width="20" height="20" fill="currentColor"/> </clipPath> <clipPath id="clip1_3_47"> <rect width="10" height="10" fill="currentColor" transform="translate(10)"/> </clipPath> </defs> </svg>`

export const dimensionCreateIcon = `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3_23)"> <path fill-rule="evenodd" clip-rule="evenodd" d="M19.219 9.26613L19.3555 9.33963C19.5985 9.47044 19.75 9.72408 19.75 10C19.75 10.2759 19.5985 10.5296 19.3555 10.6604L16.7245 12.0769L19.3555 13.4935C19.5985 13.6243 19.75 13.8779 19.75 14.1538C19.75 14.4297 19.5985 14.6834 19.3556 14.8142L10.3556 19.6604C10.1336 19.7799 9.86641 19.7799 9.64443 19.6604L0.644425 14.8142C0.401488 14.6834 0.249994 14.4297 0.25 14.1538C0.250006 13.8779 0.40151 13.6243 0.644453 13.4935L3.27547 12.0769L0.644453 10.6604C0.401505 10.5296 0.25 10.2759 0.25 10C0.25 9.72408 0.401505 9.47044 0.644453 9.33963L3.27547 7.92308L0.644453 6.50652C0.40151 6.37572 0.250006 6.12209 0.25 5.84617C0.249994 5.57025 0.401488 5.31661 0.644425 5.1858L9.64443 0.339647C9.86641 0.220118 10.1336 0.220118 10.3556 0.339647L10.8909 0.627888C10.482 1.01231 10.1271 1.45343 9.83864 1.93871L2.58201 5.84612L5.21309 7.26271L5.21313 7.26274L10 9.84048L10.8832 9.36487C11.289 9.74778 11.7482 10.0747 12.2485 10.3333L10.3556 11.3526C10.1336 11.4722 9.8664 11.4722 9.6444 11.3526L4.85751 8.77489L2.58207 10L5.21309 11.4166L5.21313 11.4166L10 13.9943L14.7869 11.4166L14.7869 11.4166L15.6192 10.9684C17.0172 10.8251 18.2729 10.2018 19.219 9.26613ZM9.6444 15.5065L4.85751 12.9287L2.58201 14.1539L10 18.1482L17.418 14.1539L15.1425 12.9287L10.3556 15.5065C10.1336 15.626 9.8664 15.626 9.6444 15.5065Z" fill="currentColor"/> <path d="M14.375 4.375H11.875V5.625H14.375V8.125H15.625V5.625H18.125V4.375H15.625V1.875H14.375V4.375ZM15 10C13.6739 10 12.4021 9.47322 11.4645 8.53553C10.5268 7.59785 10 6.32608 10 5C10 3.67392 10.5268 2.40215 11.4645 1.46447C12.4021 0.526784 13.6739 0 15 0C16.3261 0 17.5979 0.526784 18.5355 1.46447C19.4732 2.40215 20 3.67392 20 5C20 6.32608 19.4732 7.59785 18.5355 8.53553C17.5979 9.47322 16.3261 10 15 10Z" fill="currentColor"/> </g> <defs> <clipPath id="clip0_3_23"> <rect width="20" height="20" fill="currentColor"/> </clipPath> </defs> </svg> `

export const metricOverviewIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21ZM12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.28032 8.55084C7.45187 8.46694 7.65623 8.48808 7.80697 8.60532L12 11.8666L16.193 8.60532C16.3438 8.48808 16.5481 8.46694 16.7197 8.55084C16.8912 8.63474 17 8.80903 17 9V16H16V10.0223L12.307 12.8947C12.1264 13.0351 11.8736 13.0351 11.693 12.8947L8 10.0223V16H7V9C7 8.80903 7.10878 8.63474 7.28032 8.55084Z" fill="currentColor"/>
</svg>`

export const metricManageIcon = `<svg viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M11 11C12.4667 11 13.5667 12.2222 13.5667 13.6889C13.5667 15.1556 12.3444 16.3778 11 16.3778C9.65556 16.3778 8.43334 15.1556 8.43334 13.6889C8.43334 12.2222 9.53334 11 11 11ZM11 9.89999C9.04445 9.89999 7.33334 11.6111 7.33334 13.6889C7.33334 15.7667 8.92222 17.4778 11 17.4778C13.0778 17.4778 14.6667 15.7667 14.6667 13.6889C14.6667 11.6111 12.9556 9.89999 11 9.89999Z" fill="currentColor"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1484 3.66809C11.0989 3.66715 11.0495 3.66667 11 3.66667C5.62223 3.66667 1.22223 8.06667 1.22223 13.5667C1.22223 15.0333 1.46667 16.3778 2.07778 17.6L3.17778 17.2333C2.6889 16.1333 2.44445 14.9111 2.44445 13.6889C2.44445 8.80001 6.35556 4.88889 11 4.88889C11.0003 4.88889 11.0007 4.88889 11.001 4.88889C11.0086 4.47007 11.0591 4.06173 11.1484 3.66809ZM19.029 10.6483C19.4191 10.5081 19.7905 10.3287 20.1384 10.1148C20.5439 11.2239 20.7778 12.3953 20.7778 13.5667C20.7778 15.1556 20.4111 16.6222 19.8 17.9667L18.7 17.4778C19.3111 16.3778 19.5556 15.0333 19.5556 13.6889C19.5556 12.6184 19.4006 11.6255 19.029 10.6483ZM14.0198 10.2087L13.0252 11.2261L13.8991 12.0805L15.2186 10.7311C14.7972 10.6003 14.3958 10.4243 14.0198 10.2087Z" fill="currentColor"/> <g clip-path="url(#clip0_4_60)"> <path d="M21.1042 4.10417H20.2219C20.149 3.83959 20.0437 3.58855 19.9115 3.35521L20.5354 2.73126C20.6979 2.56876 20.6979 2.30417 20.5354 2.14167L19.8573 1.46355C19.6948 1.30105 19.4302 1.30105 19.2677 1.46355L18.6437 2.08751C18.4104 1.95521 18.1594 1.85001 17.8948 1.77709V0.895838C17.8948 0.666672 17.7073 0.479172 17.4781 0.479172H16.5198C16.2906 0.479172 16.1031 0.666672 16.1031 0.895838V1.77813C15.8385 1.85105 15.5875 1.95626 15.3542 2.08855L14.7302 1.46459C14.5677 1.30209 14.3031 1.30209 14.1406 1.46459L13.4625 2.14271C13.3 2.30521 13.3 2.5698 13.4625 2.7323L14.0865 3.35626C13.9542 3.58959 13.849 3.84063 13.776 4.10521H12.8958C12.6667 4.10521 12.4792 4.29271 12.4792 4.52188V5.48021C12.4792 5.70938 12.6667 5.89688 12.8958 5.89688H13.7781C13.851 6.16146 13.9562 6.41251 14.0885 6.64584L13.4646 7.2698C13.3021 7.4323 13.3021 7.69688 13.4646 7.85938L14.1427 8.53751C14.3052 8.70001 14.5698 8.70001 14.7323 8.53751L15.3562 7.91355C15.5896 8.04584 15.8406 8.15105 16.1052 8.22396V9.10417C16.1052 9.33334 16.2927 9.52084 16.5219 9.52084H17.4802C17.7094 9.52084 17.8969 9.33334 17.8969 9.10417V8.22188C18.1615 8.14896 18.4125 8.04376 18.6458 7.91146L19.2698 8.53542C19.4323 8.69792 19.6969 8.69792 19.8594 8.53542L20.5375 7.8573C20.7 7.6948 20.7 7.43021 20.5375 7.26771L19.9135 6.64376C20.0458 6.41042 20.151 6.15938 20.224 5.8948H21.1042C21.3333 5.8948 21.5208 5.7073 21.5208 5.47813V4.5198C21.5208 4.29167 21.3333 4.10417 21.1042 4.10417ZM17 6.83334C15.9875 6.83334 15.1667 6.01251 15.1667 5.00001C15.1667 3.98751 15.9875 3.16667 17 3.16667C18.0125 3.16667 18.8333 3.98751 18.8333 5.00001C18.8333 6.01251 18.0125 6.83334 17 6.83334Z" fill="currentColor"/> <path d="M21.5392 4.00922H20.5634C20.4827 3.71659 20.3664 3.43894 20.22 3.18088L20.9101 2.49078C21.0899 2.31106 21.0899 2.01843 20.9101 1.83871L20.1601 1.08871C19.9804 0.908986 19.6878 0.908986 19.5081 1.08871L18.818 1.7788C18.5599 1.63249 18.2823 1.51613 17.9896 1.43548V0.460829C17.9896 0.207373 17.7823 -1.08007e-10 17.5288 0H16.4689C16.2154 0 16.0081 0.207373 16.0081 0.460829V1.43664C15.7154 1.51728 15.4378 1.63364 15.1797 1.77995L14.4896 1.08986C14.3099 0.910138 14.0173 0.910138 13.8376 1.08986L13.0876 1.83986C12.9078 2.01959 12.9078 2.31221 13.0876 2.49194L13.7776 3.18203C13.6313 3.44009 13.515 3.71774 13.4343 4.01037H12.4608C12.2074 4.01037 12 4.21774 12 4.4712V5.53111C12 5.78456 12.2074 5.99194 12.4608 5.99194H13.4366C13.5173 6.28456 13.6336 6.56221 13.78 6.82028L13.0899 7.51037C12.9101 7.69009 12.9101 7.98272 13.0899 8.16244L13.8399 8.91244C14.0196 9.09217 14.3122 9.09217 14.4919 8.91244L15.182 8.22235C15.4401 8.36866 15.7177 8.48502 16.0104 8.56567V9.53917C16.0104 9.79263 16.2177 10 16.4712 10H17.5311C17.7846 10 17.9919 9.79263 17.9919 9.53917V8.56336C18.2846 8.48272 18.5622 8.36636 18.8203 8.22005L19.5104 8.91014C19.6901 9.08986 19.9827 9.08986 20.1624 8.91014L20.9124 8.16014C21.0922 7.98042 21.0922 7.68779 20.9124 7.50806L20.2224 6.81797C20.3687 6.55991 20.485 6.28226 20.5657 5.98963H21.5392C21.7926 5.98963 22 5.78226 22 5.5288V4.46889C22 4.21659 21.7926 4.00922 21.5392 4.00922ZM17 7.02765C15.8802 7.02765 14.9724 6.11982 14.9724 5C14.9724 3.88018 15.8802 2.97235 17 2.97235C18.1198 2.97235 19.0277 3.88018 19.0277 5C19.0277 6.11982 18.1198 7.02765 17 7.02765Z" fill="currentColor"/> <path d="M16.0833 4.99999C16.0833 5.24311 16.1799 5.47627 16.3518 5.64818C16.5237 5.82008 16.7569 5.91666 17 5.91666C17.2431 5.91666 17.4763 5.82008 17.6482 5.64818C17.8201 5.47627 17.9167 5.24311 17.9167 4.99999C17.9167 4.75688 17.8201 4.52372 17.6482 4.35181C17.4763 4.17991 17.2431 4.08333 17 4.08333C16.7569 4.08333 16.5237 4.17991 16.3518 4.35181C16.1799 4.52372 16.0833 4.75688 16.0833 4.99999Z" fill="currentColor"/> </g> <defs> <clipPath id="clip0_4_60"> <rect width="10" height="10" fill="currentColor" transform="translate(12)"/> </clipPath> </defs> </svg>`

export const metricCreateIcon = `<svg viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M11 11C12.4667 11 13.5667 12.2222 13.5667 13.6889C13.5667 15.1556 12.3444 16.3778 11 16.3778C9.65555 16.3778 8.43333 15.1556 8.43333 13.6889C8.43333 12.2222 9.53333 11 11 11ZM11 9.89999C9.04444 9.89999 7.33333 11.6111 7.33333 13.6889C7.33333 15.7667 8.92222 17.4778 11 17.4778C13.0778 17.4778 14.6667 15.7667 14.6667 13.6889C14.6667 11.6111 12.9556 9.89999 11 9.89999Z" fill="currentColor"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1484 3.66809C11.0989 3.66715 11.0495 3.66667 11 3.66667C5.62223 3.66667 1.22223 8.06667 1.22223 13.5667C1.22223 15.0333 1.46667 16.3778 2.07778 17.6L3.17778 17.2333C2.6889 16.1333 2.44445 14.9111 2.44445 13.6889C2.44445 8.80001 6.35556 4.88889 11 4.88889C11.0003 4.88889 11.0007 4.88889 11.001 4.88889C11.0086 4.47007 11.0591 4.06173 11.1484 3.66809ZM19.029 10.6483C19.4191 10.5081 19.7905 10.3287 20.1384 10.1148C20.5439 11.2239 20.7778 12.3953 20.7778 13.5667C20.7778 15.1556 20.4111 16.6222 19.8 17.9667L18.7 17.4778C19.3111 16.3778 19.5556 15.0333 19.5556 13.6889C19.5556 12.6184 19.4006 11.6255 19.029 10.6483ZM14.0198 10.2087L13.0252 11.2261L13.8991 12.0805L15.2186 10.7311C14.7972 10.6003 14.3958 10.4243 14.0198 10.2087Z" fill="currentColor"/> <g clip-path="url(#clip0_3_18)"> <path d="M16.375 4.375H13.875V5.625H16.375V8.125H17.625V5.625H20.125V4.375H17.625V1.875H16.375V4.375ZM17 10C15.6739 10 14.4021 9.47322 13.4645 8.53553C12.5268 7.59785 12 6.32608 12 5C12 3.67392 12.5268 2.40215 13.4645 1.46447C14.4021 0.526784 15.6739 0 17 0C18.3261 0 19.5979 0.526784 20.5355 1.46447C21.4732 2.40215 22 3.67392 22 5C22 6.32608 21.4732 7.59785 20.5355 8.53553C19.5979 9.47322 18.3261 10 17 10Z" fill="currentColor"/> </g> <defs> <clipPath id="clip0_3_18"> <rect width="10" height="10" fill="currentColor" transform="translate(12)"/> </clipPath> </defs> </svg>`

export const projectIcon = `<svg t="1709716327874" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1508"><path d="M756.385475 521.749038 455.285812 521.749038c-24.606421 0-44.474934-19.869536-44.474934-44.475957 0-24.599258 19.868513-44.470841 44.474934-44.470841l301.099662 0c24.604374 0 44.472887 19.87056 44.472887 44.470841C800.858362 501.880525 780.799514 521.749038 756.385475 521.749038L756.385475 521.749038 756.385475 521.749038zM756.385475 697.763844 455.285812 697.763844c-24.606421 0-44.474934-19.871583-44.474934-44.474934 0-24.600281 19.868513-44.474934 44.474934-44.474934l301.099662 0c24.604374 0 44.472887 19.874653 44.472887 44.474934C800.858362 677.892261 780.799514 697.763844 756.385475 697.763844L756.385475 697.763844 756.385475 697.763844zM960.777585 843.11221 960.777585 280.640702c0-68.322062-62.265112-61.883419-62.265112-61.883419s-371.690394 0.37453-351.819834 0c-21.196765 0.37453-31.986505-11.171434-31.986505-11.171434s-14.760169-25.544793-41.443901-65.669652c-27.820626-42.200124-60.181661-35.388998-60.181661-35.388998L139.420865 106.527199c-75.891458 0-76.648704 73.052806-76.648704 73.052806l0 659.933237c0 81.377402 61.504796 71.351048 61.504796 71.351048l779.914866 0C970.053855 910.86429 960.777585 843.11221 960.777585 843.11221L960.777585 843.11221 960.777585 843.11221 960.777585 843.11221zM904.952138 809.80257c0 25.171286-20.250206 45.422516-45.423539 45.422516L164.023192 855.225085c-25.173332 0-45.419446-20.25123-45.419446-45.422516L118.603747 338.933339c0-25.174356 20.246113-45.420469 45.419446-45.420469l695.506431 0c25.173332 0 45.423539 20.246113 45.423539 45.420469L904.953162 809.80257 904.952138 809.80257zM242.566036 477.274104c0 26.235524 21.265326 47.509037 47.498804 47.509037 26.234501 0 47.50392-21.273513 47.50392-47.509037 0-26.228361-21.269419-47.498804-47.50392-47.498804C263.830339 429.7753 242.566036 451.039603 242.566036 477.274104L242.566036 477.274104 242.566036 477.274104zM242.566036 653.28891c0 26.235524 21.265326 47.50392 47.498804 47.50392 26.234501 0 47.50392-21.268396 47.50392-47.50392 0-26.237571-21.269419-47.498804-47.50392-47.498804C263.830339 605.790106 242.566036 627.052362 242.566036 653.28891L242.566036 653.28891 242.566036 653.28891zM242.566036 653.28891" fill="currentColor" p-id="1509"></path></svg>`

export const metricTreeIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
<mask id="path-1-inside-1_13_2665" fill="white">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M20 10H18V11.5V12.5V14H20V10ZM17 12.5V14.5C17 14.7761 17.2239 15 17.5 15H20.5C20.7761 15 21 14.7761 21 14.5V9.5C21 9.22386 20.7761 9 20.5 9H17.5C17.2239 9 17 9.22386 17 9.5V11.5H13V4.5C13 4.22386 12.7761 4 12.5 4L7 4V2.5C7 2.22386 6.77614 2 6.5 2H3.5C3.22386 2 3 2.22386 3 2.5V6.5C3 6.77614 3.22386 7 3.5 7H6.5C6.77614 7 7 6.77614 7 6.5V5L12 5V11.5H7V10C7 9.72386 6.77614 9.5 6.5 9.5H3.5C3.22386 9.5 3 9.72386 3 10V14C3 14.2761 3.22386 14.5 3.5 14.5L6.5 14.5C6.77614 14.5 7 14.2761 7 14V12.5H12L12 19H7V17.5C7 17.2239 6.77614 17 6.5 17H3.5C3.22386 17 3 17.2239 3 17.5V21.5C3 21.7761 3.22386 22 3.5 22H6.5C6.77614 22 7 21.7761 7 21.5V20H12.5C12.7761 20 13 19.7761 13 19.5V12.5H17ZM6 12.5V11.5V10.5H4V13.5H6V12.5ZM4 18H6V21H4V18ZM6 3H4V6H6V3Z"/>
</mask>
<path d="M18 10V8.75H16.75V10H18ZM20 10H21.25V8.75H20V10ZM18 14H16.75V15.25H18V14ZM20 14V15.25H21.25V14H20ZM17 12.5H18.25V11.25H17V12.5ZM17 11.5V12.75H18.25V11.5H17ZM13 11.5H11.75V12.75H13V11.5ZM7 4H5.75V5.25H7V4ZM7 5V3.75H5.75V5H7ZM12 5H13.25V3.75H12V5ZM12 11.5V12.75H13.25V11.5H12ZM7 11.5L5.75 11.5V12.75L7 12.75V11.5ZM7 12.5V11.25L5.75 11.25V12.5L7 12.5ZM12 12.5H13.25V11.25H12V12.5ZM12 19V20.25H13.25V19H12ZM7 19H5.75V20.25H7V19ZM7 20V18.75H5.75V20H7ZM13 12.5V11.25H11.75V12.5H13ZM6 10.5L7.25 10.5V9.25H6V10.5ZM4 10.5V9.25H2.75V10.5H4ZM4 13.5H2.75V14.75H4V13.5ZM6 13.5V14.75L7.25 14.75V13.5L6 13.5ZM6 18H7.25V16.75H6V18ZM4 18V16.75H2.75V18H4ZM6 21V22.25H7.25V21H6ZM4 21H2.75V22.25H4V21ZM4 3V1.75H2.75V3H4ZM6 3H7.25V1.75H6V3ZM4 6H2.75V7.25H4V6ZM6 6V7.25H7.25V6H6ZM18 11.25H20V8.75H18V11.25ZM19.25 11.5V10H16.75V11.5H19.25ZM16.75 11.5V12.5H19.25V11.5H16.75ZM19.25 14V12.5H16.75V14H19.25ZM20 12.75H18V15.25H20V12.75ZM18.75 10V14H21.25V10H18.75ZM18.25 14.5V12.5H15.75V14.5H18.25ZM17.5 13.75C17.9142 13.75 18.25 14.0858 18.25 14.5H15.75C15.75 15.4665 16.5335 16.25 17.5 16.25V13.75ZM20.5 13.75H17.5V16.25H20.5V13.75ZM19.75 14.5C19.75 14.0858 20.0858 13.75 20.5 13.75V16.25C21.4665 16.25 22.25 15.4665 22.25 14.5H19.75ZM19.75 9.5V14.5H22.25V9.5H19.75ZM20.5 10.25C20.0858 10.25 19.75 9.91421 19.75 9.5H22.25C22.25 8.5335 21.4665 7.75 20.5 7.75V10.25ZM17.5 10.25H20.5V7.75H17.5V10.25ZM18.25 9.5C18.25 9.91421 17.9142 10.25 17.5 10.25V7.75C16.5335 7.75 15.75 8.5335 15.75 9.5H18.25ZM18.25 11.5V9.5H15.75V11.5H18.25ZM13 12.75H17V10.25H13V12.75ZM11.75 4.5V11.5H14.25V4.5H11.75ZM12.5 5.25C12.0858 5.25 11.75 4.91421 11.75 4.5H14.25C14.25 3.5335 13.4665 2.75 12.5 2.75V5.25ZM7 5.25L12.5 5.25V2.75L7 2.75V5.25ZM5.75 2.5V4H8.25V2.5H5.75ZM6.5 3.25C6.08579 3.25 5.75 2.91421 5.75 2.5H8.25C8.25 1.5335 7.4665 0.75 6.5 0.75V3.25ZM3.5 3.25H6.5V0.75L3.5 0.75V3.25ZM4.25 2.5C4.25 2.91421 3.91421 3.25 3.5 3.25V0.75C2.5335 0.75 1.75 1.5335 1.75 2.5L4.25 2.5ZM4.25 6.5V2.5L1.75 2.5V6.5H4.25ZM3.5 5.75C3.91421 5.75 4.25 6.08579 4.25 6.5H1.75C1.75 7.4665 2.5335 8.25 3.5 8.25V5.75ZM6.5 5.75H3.5V8.25H6.5V5.75ZM5.75 6.5C5.75 6.08579 6.08579 5.75 6.5 5.75V8.25C7.4665 8.25 8.25 7.4665 8.25 6.5H5.75ZM5.75 5V6.5H8.25V5H5.75ZM12 3.75L7 3.75V6.25H12V3.75ZM13.25 11.5V5H10.75V11.5H13.25ZM7 12.75H12V10.25H7V12.75ZM5.75 10V11.5L8.25 11.5V10L5.75 10ZM6.5 10.75C6.08579 10.75 5.75 10.4142 5.75 10L8.25 10C8.25 9.0335 7.4665 8.25 6.5 8.25V10.75ZM3.5 10.75L6.5 10.75V8.25H3.5V10.75ZM4.25 10C4.25 10.4142 3.91421 10.75 3.5 10.75V8.25C2.5335 8.25 1.75 9.0335 1.75 10H4.25ZM4.25 14V10H1.75V14H4.25ZM3.5 13.25C3.91421 13.25 4.25 13.5858 4.25 14H1.75C1.75 14.9665 2.5335 15.75 3.5 15.75V13.25ZM6.5 13.25L3.5 13.25V15.75L6.5 15.75V13.25ZM5.75 14C5.75 13.5858 6.08579 13.25 6.5 13.25V15.75C7.4665 15.75 8.25 14.9665 8.25 14L5.75 14ZM5.75 12.5V14L8.25 14V12.5L5.75 12.5ZM12 11.25H7V13.75H12V11.25ZM13.25 19V12.5H10.75L10.75 19H13.25ZM7 20.25H12V17.75H7V20.25ZM8.25 19V17.5H5.75V19H8.25ZM8.25 17.5C8.25 16.5335 7.4665 15.75 6.5 15.75L6.5 18.25C6.08579 18.25 5.75 17.9142 5.75 17.5H8.25ZM6.5 15.75L3.5 15.75V18.25H6.5L6.5 15.75ZM3.5 15.75C2.5335 15.75 1.75 16.5335 1.75 17.5H4.25C4.25 17.9142 3.91421 18.25 3.5 18.25V15.75ZM1.75 17.5V21.5H4.25V17.5H1.75ZM1.75 21.5C1.75 22.4665 2.5335 23.25 3.5 23.25V20.75C3.91421 20.75 4.25 21.0858 4.25 21.5H1.75ZM3.5 23.25H6.5V20.75H3.5V23.25ZM6.5 23.25C7.4665 23.25 8.25 22.4665 8.25 21.5H5.75C5.75 21.0858 6.08579 20.75 6.5 20.75V23.25ZM8.25 21.5V20H5.75V21.5H8.25ZM12.5 18.75H7V21.25H12.5V18.75ZM11.75 19.5C11.75 19.0858 12.0858 18.75 12.5 18.75V21.25C13.4665 21.25 14.25 20.4665 14.25 19.5H11.75ZM11.75 12.5L11.75 19.5H14.25V12.5H11.75ZM17 11.25H13V13.75H17V11.25ZM7.25 12.5V11.5L4.75 11.5V12.5L7.25 12.5ZM4.75 10.5V11.5L7.25 11.5V10.5L4.75 10.5ZM4 11.75H6V9.25H4V11.75ZM5.25 13.5V10.5H2.75V13.5H5.25ZM6 12.25H4V14.75H6V12.25ZM4.75 12.5V13.5L7.25 13.5V12.5L4.75 12.5ZM6 16.75H4V19.25H6V16.75ZM7.25 21V18H4.75V21H7.25ZM4 22.25H6V19.75H4V22.25ZM2.75 18V21H5.25V18H2.75ZM4 4.25H6V1.75L4 1.75V4.25ZM5.25 6V3H2.75V6H5.25ZM6 4.75H4V7.25H6V4.75ZM4.75 3V6H7.25V3H4.75Z" fill="currentColor" mask="url(#path-1-inside-1_13_2665)"/>
</svg>`

export const duplicateIcon = `<svg t="1710919852279" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5087"><path d="M216.462222 654.222222c-8.576 0-13.269333-0.028444-16.64-0.298666l-0.369778-0.042667-0.042666-0.369778c-0.284444-3.370667-0.298667-8.064-0.298667-16.64V216.462222c0-8.576 0.028444-13.269333 0.298667-16.64l0.042666-0.369778 0.369778-0.042666c3.370667-0.284444 8.064-0.298667 16.64-0.298667h335.075556c8.576 0 13.269333 0.028444 16.64 0.298667l0.369778 0.042666 0.042666 0.369778c0.284444 3.370667 0.298667 8.064 0.298667 16.64v24.220445a28.444444 28.444444 0 0 0 56.888889 0v-25.144889c0-7.296 0-14.407111-0.497778-20.423111-0.554667-6.613333-1.848889-14.407111-5.902222-22.229334a56.888889 56.888889 0 0 0-24.263111-24.263111c-7.822222-4.053333-15.616-5.347556-22.229334-5.902222C566.869333 142.222222 559.758222 142.222222 552.462222 142.222222h-336.924444c-7.296 0-14.407111 0-20.423111 0.497778-6.613333 0.554667-14.407111 1.848889-22.229334 5.902222a56.888889 56.888889 0 0 0-24.263111 24.263111c-4.053333 7.822222-5.347556 15.616-5.902222 22.229334-0.497778 6.016-0.497778 13.127111-0.497778 20.423111v422.257778c0 7.296 0 14.407111 0.497778 20.423111 0.554667 6.613333 1.848889 14.407111 5.902222 22.229333a56.888889 56.888889 0 0 0 24.263111 24.263111c7.822222 4.053333 15.616 5.347556 22.229334 5.902222 6.016 0.497778 13.127111 0.497778 20.423111 0.497778h116.750222a28.444444 28.444444 0 0 0 0-56.888889H216.462222z m287.573334-327.111111c-32.398222 0-48.597333 0-60.928 6.4a56.888889 56.888889 0 0 0-24.263112 24.263111c-6.4 12.330667-6.4 28.529778-6.4 60.928v371.484445c0 32.398222 0 48.597333 6.4 60.928a56.888889 56.888889 0 0 0 24.263112 24.263111c12.330667 6.4 28.529778 6.4 60.928 6.4h286.151111c32.398222 0 48.597333 0 60.928-6.4a56.888889 56.888889 0 0 0 24.263111-24.263111c6.4-12.330667 6.4-28.529778 6.4-60.928V418.702222c0-32.398222 0-48.597333-6.4-60.928a56.888889 56.888889 0 0 0-24.263111-24.263111C838.784 327.111111 822.584889 327.111111 790.186667 327.111111H504.035556z m286.151111 497.777778H504.035556c-17.152 0-26.538667-0.042667-33.28-0.611556l-0.753778-0.056889-0.056889-0.753777c-0.568889-6.741333-0.611556-16.128-0.611556-33.28V418.702222c0-17.152 0.042667-26.538667 0.611556-33.28l0.056889-0.753778 0.753778-0.056888c6.741333-0.568889 16.128-0.611556 33.28-0.611556h286.151111c17.152 0 26.538667 0.042667 33.28 0.611556l0.753777 0.056888 0.056889 0.753778c0.568889 6.741333 0.611556 16.128 0.611556 33.28v371.484445c0 17.152-0.042667 26.538667-0.611556 33.28l-0.056889 0.753777-0.753777 0.056889c-6.741333 0.568889-16.128 0.611556-33.28 0.611556z" fill="currentColor" p-id="5088"></path></svg>`

export const omitIcon = `<svg t="1710920780155" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9163"><path d="M219.428571 438.857143a73.142857 73.142857 0 1 1 0 146.285714 73.142857 73.142857 0 0 1 0-146.285714z m292.571429 0a73.142857 73.142857 0 1 1 0 146.285714 73.142857 73.142857 0 0 1 0-146.285714z m292.571429 0a73.142857 73.142857 0 1 1 0 146.285714 73.142857 73.142857 0 0 1 0-146.285714z" fill="currentColor" p-id="9164"></path></svg>`

export const clockIcon = `<svg t="1711520334397" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5142" ><path d="M512 853.333333C700.586667 853.333333 853.333333 700.586667 853.333333 512 853.333333 323.413333 700.586667 170.666667 512 170.666667 323.413333 170.666667 170.666667 323.413333 170.666667 512 170.666667 700.586667 323.413333 853.333333 512 853.333333M512 85.333333C747.52 85.333333 938.666667 276.48 938.666667 512 938.666667 747.52 747.52 938.666667 512 938.666667 276.053333 938.666667 85.333333 746.666667 85.333333 512 85.333333 276.48 276.48 85.333333 512 85.333333M533.333333 298.666667 533.333333 522.666667 725.333333 636.586667 693.333333 689.066667 469.333333 554.666667 469.333333 298.666667 533.333333 298.666667Z" fill="currentColor" p-id="5143"></path></svg>`

export const sendMessageIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
<path d="M4.10526 12L3.39451 5.56937C3.22234 4.01167 4.82732 2.87015 6.24227 3.54394L20.208 10.1943C21.7282 10.9182 21.7282 13.0818 20.208 13.8057L6.24227 20.4561C4.82732 21.1298 3.22234 19.9883 3.39451 18.4306L4.10526 12ZM4.10526 12H11.8421" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`

export const h5SendMessageIcon = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
<path d="M3.42105 10.0001L2.89451 5.23612C2.72234 3.67842 4.32732 2.5369 5.74228 3.21069L16.208 8.19436C17.7282 8.91825 17.7282 11.0819 16.208 11.8058L5.74227 16.7895C4.32732 17.4633 2.72234 16.3217 2.89451 14.764L3.42105 10.0001ZM3.42105 10.0001H9.86842" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`

export const askSuggestionIcon = `<svg viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Icon Frame">
<path id="Vector 135" d="M3.6665 10L3.06239 4.56299C2.88947 3.0067 4.49119 1.86433 5.90633 2.53465L17.8507 8.19252C19.3758 8.91494 19.3758 11.0851 17.8507 11.8075L5.90633 17.4653C4.49119 18.1357 2.88947 16.9933 3.06239 15.437L3.6665 10ZM3.6665 10H10.6665" stroke="#5637AD" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</svg>

`

export const uploadIcon = `<svg viewBox="0 0 21 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Document add">
<path id="Rectangle 74" d="M17 11V7.7241C17 7.25623 16.836 6.80316 16.5364 6.44373L12.5997 1.71963C12.2197 1.26365 11.6568 1 11.0633 1H9H3C1.89543 1 1 1.89543 1 3V19C1 20.1046 1.89543 21 3 21H10" stroke="currentColor" stroke-linecap="round"/>
<path id="Line" d="M14 18H20" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Line_2" d="M17 15L17 21" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector 139" d="M12 1.5V6C12 6.55228 12.4477 7 13 7H16.5" stroke="currentColor" stroke-linecap="round"/>
</g>
</svg>`

export const datasetIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.5 16.3856V7.84968C3.5 7.695 3.66808 7.59887 3.8014 7.6773L11.9014 12.442C11.9625 12.4779 12 12.5435 12 12.6144V21.1503C12 21.305 11.8319 21.4011 11.6986 21.3227L3.5986 16.558C3.53751 16.5221 3.5 16.4565 3.5 16.3856Z" stroke="currentColor" stroke-width="1.2"/>
<path d="M12.1039 2.39616L20.3571 7.41314C20.4217 7.45242 20.421 7.54644 20.3559 7.58478L12.1014 12.4404C12.0388 12.4772 11.9612 12.4772 11.8986 12.4404L3.64413 7.58478C3.57895 7.54644 3.57827 7.45242 3.64289 7.41314L11.8961 2.39616C11.9599 2.35736 12.0401 2.35736 12.1039 2.39616Z" stroke="currentColor" stroke-width="1.2"/>
<path d="M20.3981 16.5573L12.0149 21.2738C12.0082 21.2776 12 21.2728 12 21.2651V12.6144C12 12.5435 12.0375 12.4779 12.0986 12.442L20.1986 7.6773C20.3319 7.59887 20.5 7.695 20.5 7.84968V16.383C20.5 16.4553 20.461 16.5219 20.3981 16.5573Z" stroke="currentColor" stroke-width="1.2"/>
<path d="M3.59642 7.44163L11.8964 2.41671C11.9601 2.37816 12.0399 2.37816 12.1036 2.41671L20.4036 7.44163C20.4634 7.47786 20.5 7.54275 20.5 7.61271V16.3856C20.5 16.4565 20.4625 16.5221 20.4014 16.558L12.1014 21.4404C12.0388 21.4772 11.9612 21.4772 11.8986 21.4404L3.5986 16.558C3.53751 16.5221 3.5 16.4565 3.5 16.3856V7.61271C3.5 7.54274 3.53657 7.47786 3.59642 7.44163Z" stroke="currentColor" stroke-width="1.2"/>
</svg>`

export const followUpAskIcon = `<svg t="1705644654433" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7846" fill="currentColor" ><path d="M128 64c23.552 0 42.688 19.072 42.688 42.688v490.752h338.624a43.264 43.264 0 0 1 5.376 0h338.624V106.688a42.688 42.688 0 0 1 85.376 0V640a42.688 42.688 0 0 1-42.688 42.688H554.688v131.584l55.168-55.168a42.688 42.688 0 0 1 60.288 60.352l-128 128a42.688 42.688 0 0 1-60.288 0l-128-128a42.688 42.688 0 0 1 60.288-60.352l55.168 55.168v-131.584H128A42.688 42.688 0 0 1 85.312 640V106.688C85.312 83.072 104.448 64 128 64z" p-id="7847"></path><path d="M566.976 204.032c16.64-16.64 43.648-16.64 60.288 0l106.88 107.136a42.752 42.752 0 0 1-0.64 60.992L627.52 479.872a42.688 42.688 0 1 1-60.8-59.904L602.048 384H320a42.688 42.688 0 0 1 0-85.312h281.152l-34.24-34.304a42.688 42.688 0 0 1 0-60.352z" p-id="7848"></path></svg>`

export const dimensionCategoryIcon = `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M2.3864 2.02354C2.27988 2.00637 2.17163 2.04181 2.0959 2.11867C2.02017 2.19553 1.98633 2.30429 2.00508 2.41055L2.71218 6.41749C2.73384 6.54019 2.82211 6.64057 2.94103 6.67773C3.05995 6.71488 3.18967 6.68262 3.27732 6.59408L4.70265 5.15441L7.66266 8.13343L7.6409 10.6802L5.60781 10.6903C5.48293 10.691 5.36888 10.7613 5.31231 10.8727C5.25574 10.984 5.26614 11.1176 5.33926 11.2188L7.72979 14.5285C7.79297 14.616 7.89458 14.6675 8.00247 14.6667C8.11037 14.6659 8.21121 14.6129 8.27308 14.5245L8.33333 14.4384V13.2759L7.99575 13.7582L6.25908 11.3538L8.33333 11.3434V10.6768L8.32927 10.6768V8.14291L11.3431 5.15274L12.7701 6.59411C12.8577 6.68265 12.9874 6.71491 13.1064 6.67775C13.2253 6.64059 13.3135 6.54022 13.3352 6.41752L14.0423 2.41058C14.0611 2.30432 14.0272 2.19556 13.9515 2.1187C13.8758 2.04184 13.7675 2.00639 13.661 2.02357L9.63032 2.67351C9.50704 2.69339 9.4052 2.78051 9.36648 2.89924C9.32776 3.01796 9.35865 3.14837 9.44651 3.23711L10.874 4.67899L8.00169 7.5288L5.1717 4.68064L6.60087 3.23708C6.68873 3.14834 6.71962 3.01793 6.6809 2.89921C6.64218 2.78048 6.54034 2.69336 6.41706 2.67348L2.3864 2.02354ZM5.67125 3.2285L3.25676 5.66729L2.74306 2.75633L5.67125 3.2285ZM12.7906 5.66731L10.3761 3.22853L13.3043 2.75636L12.7906 5.66731Z" fill="currentColor"/> <path d="M14.6663 10H9.33301V10.6667H14.6663V10Z" fill="currentColor"/> <path d="M9.33301 12H14.6663V12.6667H9.33301V12Z" fill="currentColor"/> <path d="M9.33301 14H14.6663V14.6667H9.33301V14Z" fill="currentColor"/> </svg> `

export const metricLineIcon = `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M7.99967 13.9999C11.3134 13.9999 13.9997 11.3136 13.9997 7.99992C13.9997 4.68621 11.3134 1.99992 7.99967 1.99992C4.68597 1.99992 1.99967 4.68621 1.99967 7.99992C1.99967 11.3136 4.68597 13.9999 7.99967 13.9999ZM7.99967 14.6666C11.6816 14.6666 14.6663 11.6818 14.6663 7.99992C14.6663 4.31802 11.6816 1.33325 7.99967 1.33325C4.31778 1.33325 1.33301 4.31802 1.33301 7.99992C1.33301 11.6818 4.31778 14.6666 7.99967 14.6666Z" fill="currentColor"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M4.85322 5.70048C4.96759 5.64455 5.10383 5.65864 5.20432 5.7368L7.99967 7.91096L10.795 5.7368C10.8955 5.65864 11.0318 5.64455 11.1461 5.70048C11.2605 5.75641 11.333 5.87261 11.333 5.99992V10.6666H10.6663V6.68147L8.20432 8.59637C8.08395 8.68999 7.9154 8.68999 7.79503 8.59637L5.33301 6.68147V10.6666H4.66634V5.99992C4.66634 5.87261 4.73886 5.75641 4.85322 5.70048Z" fill="currentColor"/> </svg> `

export const overviewFolderIcon = `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M2.91602 2.5C2.22566 2.5 1.66602 3.05964 1.66602 3.75V15.4167C1.66602 16.107 2.22566 16.6667 2.91602 16.6667H17.0827C17.773 16.6667 18.3327 16.107 18.3327 15.4167V5.41667C18.3327 4.72631 17.773 4.16667 17.0827 4.16667H9.75527L8.21064 2.62204C8.1325 2.5439 8.02652 2.5 7.91602 2.5H2.91602ZM9.75527 5C9.53426 5 9.3223 4.9122 9.16602 4.75592L7.74343 3.33333H2.91602C2.6859 3.33333 2.49935 3.51988 2.49935 3.75V7.5H17.4993V5.41667C17.4993 5.18655 17.3128 5 17.0827 5H9.75527Z" fill="currentColor"/> <path opacity="0.4" fill-rule="evenodd" clip-rule="evenodd" d="M9.16667 4.75584C9.32295 4.91212 9.53491 4.99992 9.75592 4.99992H17.0833C17.3135 4.99992 17.5 5.18647 17.5 5.41659V7.49992H2.5V3.74992C2.5 3.5198 2.68655 3.33325 2.91667 3.33325H7.74408L9.16667 4.75584Z" fill="currentColor"/> </svg> `

export const overviewDatasetIcon = `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M9.80505 2.54842C9.927 2.48386 10.073 2.48386 10.195 2.54842L17.2783 6.29842C17.4147 6.37063 17.5 6.51233 17.5 6.66667C17.5 6.821 17.4147 6.9627 17.2783 7.03491L14.8257 8.33333L17.2783 9.63176C17.4147 9.70397 17.5 9.84566 17.5 10C17.5 10.1543 17.4147 10.296 17.2783 10.3682L14.8257 11.6667L17.2783 12.9651C17.4147 13.0373 17.5 13.179 17.5 13.3333C17.5 13.4877 17.4147 13.6294 17.2783 13.7016L10.195 17.4516C10.073 17.5161 9.927 17.5161 9.80505 17.4516L2.72171 13.7016C2.58531 13.6294 2.5 13.4877 2.5 13.3333C2.5 13.179 2.58531 13.0373 2.72171 12.9651L5.17429 11.6667L2.72171 10.3682C2.58531 10.296 2.5 10.1543 2.5 10C2.5 9.84566 2.58531 9.70397 2.72171 9.63176L5.17429 8.33333L2.72171 7.03491C2.58531 6.9627 2.5 6.821 2.5 6.66667C2.5 6.51233 2.58531 6.37063 2.72171 6.29842L9.80505 2.54842ZM6.06481 8.80479L3.80719 10L10 13.2785L16.1928 10L13.9352 8.80479L10.195 10.7849C10.073 10.8495 9.927 10.8495 9.80505 10.7849L6.06481 8.80479ZM6.06481 12.1381L3.80719 13.3333L10 16.6119L16.1928 13.3333L13.9352 12.1381L10.195 14.1182C10.073 14.1828 9.927 14.1828 9.80505 14.1182L6.06481 12.1381ZM3.80719 6.66667L10 9.94521L16.1928 6.66667L10 3.38812L3.80719 6.66667Z" fill="currentColor"/> </svg> `

export const overviewDatasourceIcon = `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M5.98842 7.20389C6.48073 5.45111 8.09106 4.16659 10.0003 4.16659C11.9096 4.16659 13.5199 5.45111 14.0122 7.20389L14.092 7.48792L14.3864 7.50701C16.1249 7.61977 17.5003 9.06605 17.5003 10.8333C17.5003 12.3865 16.438 13.6915 15.0003 14.0616V12.9166C15.0003 12.6865 14.8138 12.4999 14.5837 12.4999H5.41699C5.18688 12.4999 5.00033 12.6865 5.00033 12.9166V14.0616C3.56264 13.6915 2.50033 12.3865 2.50033 10.8333C2.50033 9.06605 3.87578 7.61977 5.61424 7.50701L5.90865 7.48792L5.98842 7.20389ZM5.00033 14.9166C3.09846 14.5305 1.66699 12.8491 1.66699 10.8333C1.66699 8.72257 3.23618 6.97869 5.27184 6.70418C5.94749 4.74286 7.8088 3.33325 10.0003 3.33325C12.1919 3.33325 14.0532 4.74286 14.7288 6.70418C16.7645 6.97869 18.3337 8.72257 18.3337 10.8333C18.3337 12.8491 16.9022 14.5305 15.0003 14.9166V16.2499C15.0003 16.48 14.8138 16.6666 14.5837 16.6666H5.41699C5.18688 16.6666 5.00033 16.48 5.00033 16.2499V14.9166ZM5.83366 14.9999L5.83366 15.8333H14.167L14.167 14.9999V14.1666L14.167 13.3333H5.83366L5.83366 14.1666V14.9999ZM6.667 14.5833C6.667 14.3531 6.85354 14.1666 7.08366 14.1666H7.917C8.14711 14.1666 8.33366 14.3531 8.33366 14.5833C8.33366 14.8134 8.14711 14.9999 7.917 14.9999H7.08366C6.85354 14.9999 6.667 14.8134 6.667 14.5833Z" fill="currentColor"/> </svg> `

export const overviewTableIcon = `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M3.33333 3.75C3.33333 3.51988 3.51988 3.33333 3.75 3.33333L6.66667 3.33333V4.16667H7.5V3.33333H12.5V4.16667H13.3333V3.33333L16.25 3.33333C16.4801 3.33333 16.6667 3.51988 16.6667 3.75V6.66667H15.8333V7.5H16.6667V16.25C16.6667 16.4801 16.4801 16.6667 16.25 16.6667H13.3333V15.8333H12.5V16.6667H7.5V15.8333H6.66667V16.6667H3.75C3.51988 16.6667 3.33333 16.4801 3.33333 16.25L3.33333 7.5H4.16667V6.66667H3.33333L3.33333 3.75ZM3.75 2.5C3.05964 2.5 2.5 3.05964 2.5 3.75V16.25C2.5 16.9404 3.05964 17.5 3.75 17.5H16.25C16.9404 17.5 17.5 16.9404 17.5 16.25V3.75C17.5 3.05964 16.9404 2.5 16.25 2.5H3.75ZM6.66667 5.83333V6.66667H5.83333V7.5H6.66667H7.5V6.66667V5.83333H6.66667ZM6.66667 9.16667V10.8333H7.5V9.16667H6.66667ZM6.66667 12.5V14.1667H7.5V12.5H6.66667ZM9.16667 7.5H10.8333V6.66667H9.16667V7.5ZM13.3333 7.5H14.1667V6.66667H13.3333V5.83333H12.5V6.66667V7.5H13.3333ZM12.5 10.8333V9.16667H13.3333V10.8333H12.5ZM12.5 14.1667V12.5H13.3333V14.1667H12.5Z" fill="currentColor"/> </svg> `

export const reportIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
<mask id="path-1-inside-1_26_231" fill="white">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M10 3H14V21H10V3ZM11 4V20H13V4H11Z"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M4 11H8V21H4V11ZM5 12V20H7V12H5Z"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M20 7H16V21H20V7ZM17 20V8H19V20H17Z"/>
</mask>
<path d="M10 3V1.75H8.75V3H10ZM14 3H15.25V1.75H14V3ZM14 21V22.25H15.25V21H14ZM10 21H8.75V22.25H10V21ZM11 4V2.75H9.75V4H11ZM11 20H9.75V21.25H11V20ZM13 20V21.25H14.25V20H13ZM13 4H14.25V2.75H13V4ZM4 11V9.75H2.75V11H4ZM8 11H9.25V9.75H8V11ZM8 21V22.25H9.25V21H8ZM4 21H2.75V22.25H4V21ZM5 12V10.75H3.75V12H5ZM5 20H3.75V21.25H5V20ZM7 20V21.25H8.25V20H7ZM7 12H8.25V10.75H7V12ZM20 7H21.25V5.75H20V7ZM16 7V5.75H14.75V7H16ZM16 21H14.75V22.25H16V21ZM20 21V22.25H21.25V21H20ZM17 20H15.75V21.25H17V20ZM17 8V6.75H15.75V8H17ZM19 8H20.25V6.75H19V8ZM19 20V21.25H20.25V20H19ZM10 4.25H14V1.75H10V4.25ZM12.75 3V21H15.25V3H12.75ZM14 19.75H10V22.25H14V19.75ZM11.25 21V3H8.75V21H11.25ZM9.75 4V20H12.25V4H9.75ZM11 21.25H13V18.75H11V21.25ZM14.25 20V4H11.75V20H14.25ZM13 2.75H11V5.25H13V2.75ZM4 12.25H8V9.75H4V12.25ZM6.75 11V21H9.25V11H6.75ZM8 19.75H4V22.25H8V19.75ZM5.25 21V11H2.75V21H5.25ZM3.75 12V20H6.25V12H3.75ZM5 21.25H7V18.75H5V21.25ZM8.25 20V12H5.75V20H8.25ZM7 10.75H5V13.25H7V10.75ZM20 5.75H16V8.25H20V5.75ZM14.75 7V21H17.25V7H14.75ZM16 22.25H20V19.75H16V22.25ZM21.25 21V7H18.75V21H21.25ZM18.25 20V8H15.75V20H18.25ZM17 9.25H19V6.75H17V9.25ZM17.75 8V20H20.25V8H17.75ZM19 18.75H17V21.25H19V18.75Z" fill="currentColor" mask="url(#path-1-inside-1_26_231)"/>
</svg>`

export const menuSceneIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"> <mask id="path-1-inside-1_1211_52580" fill="white"> <path fill-rule="evenodd" clip-rule="evenodd" d="M15 7C15 8.65685 13.6569 10 12 10C10.3431 10 9 8.65685 9 7C9 5.34315 10.3431 4 12 4C13.6569 4 15 5.34315 15 7ZM12 9C13.1046 9 14 8.10457 14 7C14 5.89543 13.1046 5 12 5C10.8954 5 10 5.89543 10 7C10 8.10457 10.8954 9 12 9ZM22 7C22 8.65685 20.6569 10 19 10C17.3431 10 16 8.65685 16 7C16 5.34315 17.3431 4 19 4C20.6569 4 22 5.34315 22 7ZM19 9C20.1046 9 21 8.10457 21 7C21 5.89543 20.1046 5 19 5C17.8954 5 17 5.89543 17 7C17 8.10457 17.8954 9 19 9ZM2 11V12H22V11H2ZM8 7C8 8.65685 6.65685 10 5 10C3.34315 10 2 8.65685 2 7C2 5.34315 3.34315 4 5 4C6.65685 4 8 5.34315 8 7ZM5 9C6.10457 9 7 8.10457 7 7C7 5.89543 6.10457 5 5 5C3.89543 5 3 5.89543 3 7C3 8.10457 3.89543 9 5 9ZM8 20V14H2V20H8ZM3 19V15H7V19H3ZM15 14V20H9V14H15ZM10 15V19H14V15H10ZM22 20V14H16V20H22ZM17 19V15H21V19H17Z"/> </mask> <path d="M2 12H0.75V13.25H2V12ZM2 11V9.75H0.75V11H2ZM22 12V13.25H23.25V12H22ZM22 11H23.25V9.75H22V11ZM8 14H9.25V12.75H8V14ZM8 20V21.25H9.25V20H8ZM2 14V12.75H0.75V14H2ZM2 20H0.75V21.25H2V20ZM3 15V13.75H1.75V15H3ZM3 19H1.75V20.25H3V19ZM7 15H8.25V13.75H7V15ZM7 19V20.25H8.25V19H7ZM15 20V21.25H16.25V20H15ZM15 14H16.25V12.75H15V14ZM9 20H7.75V21.25H9V20ZM9 14V12.75H7.75V14H9ZM10 19H8.75V20.25H10V19ZM10 15V13.75H8.75V15H10ZM14 19V20.25H15.25V19H14ZM14 15H15.25V13.75H14V15ZM22 14H23.25V12.75H22V14ZM22 20V21.25H23.25V20H22ZM16 14V12.75H14.75V14H16ZM16 20H14.75V21.25H16V20ZM17 15V13.75H15.75V15H17ZM17 19H15.75V20.25H17V19ZM21 15H22.25V13.75H21V15ZM21 19V20.25H22.25V19H21ZM12 11.25C14.3472 11.25 16.25 9.34721 16.25 7H13.75C13.75 7.9665 12.9665 8.75 12 8.75V11.25ZM7.75 7C7.75 9.34721 9.65279 11.25 12 11.25V8.75C11.0335 8.75 10.25 7.9665 10.25 7H7.75ZM12 2.75C9.65279 2.75 7.75 4.65279 7.75 7H10.25C10.25 6.0335 11.0335 5.25 12 5.25V2.75ZM16.25 7C16.25 4.65279 14.3472 2.75 12 2.75V5.25C12.9665 5.25 13.75 6.0335 13.75 7H16.25ZM12.75 7C12.75 7.41421 12.4142 7.75 12 7.75V10.25C13.7949 10.25 15.25 8.79493 15.25 7H12.75ZM12 6.25C12.4142 6.25 12.75 6.58579 12.75 7H15.25C15.25 5.20507 13.7949 3.75 12 3.75V6.25ZM11.25 7C11.25 6.58579 11.5858 6.25 12 6.25V3.75C10.2051 3.75 8.75 5.20507 8.75 7H11.25ZM12 7.75C11.5858 7.75 11.25 7.41421 11.25 7H8.75C8.75 8.79493 10.2051 10.25 12 10.25V7.75ZM19 11.25C21.3472 11.25 23.25 9.34721 23.25 7H20.75C20.75 7.9665 19.9665 8.75 19 8.75V11.25ZM14.75 7C14.75 9.34721 16.6528 11.25 19 11.25V8.75C18.0335 8.75 17.25 7.9665 17.25 7H14.75ZM19 2.75C16.6528 2.75 14.75 4.65279 14.75 7H17.25C17.25 6.0335 18.0335 5.25 19 5.25V2.75ZM23.25 7C23.25 4.65279 21.3472 2.75 19 2.75V5.25C19.9665 5.25 20.75 6.0335 20.75 7H23.25ZM19.75 7C19.75 7.41421 19.4142 7.75 19 7.75V10.25C20.7949 10.25 22.25 8.79493 22.25 7H19.75ZM19 6.25C19.4142 6.25 19.75 6.58579 19.75 7H22.25C22.25 5.20507 20.7949 3.75 19 3.75V6.25ZM18.25 7C18.25 6.58579 18.5858 6.25 19 6.25V3.75C17.2051 3.75 15.75 5.20507 15.75 7H18.25ZM19 7.75C18.5858 7.75 18.25 7.41421 18.25 7H15.75C15.75 8.79493 17.2051 10.25 19 10.25V7.75ZM3.25 12V11H0.75V12H3.25ZM22 10.75H2V13.25H22V10.75ZM20.75 11V12H23.25V11H20.75ZM2 12.25H22V9.75H2V12.25ZM5 11.25C7.34721 11.25 9.25 9.34721 9.25 7H6.75C6.75 7.9665 5.9665 8.75 5 8.75V11.25ZM0.75 7C0.75 9.34721 2.65279 11.25 5 11.25V8.75C4.0335 8.75 3.25 7.9665 3.25 7H0.75ZM5 2.75C2.65279 2.75 0.75 4.65279 0.75 7H3.25C3.25 6.0335 4.0335 5.25 5 5.25V2.75ZM9.25 7C9.25 4.65279 7.34721 2.75 5 2.75V5.25C5.9665 5.25 6.75 6.0335 6.75 7H9.25ZM5.75 7C5.75 7.41421 5.41421 7.75 5 7.75V10.25C6.79493 10.25 8.25 8.79493 8.25 7H5.75ZM5 6.25C5.41421 6.25 5.75 6.58579 5.75 7H8.25C8.25 5.20507 6.79493 3.75 5 3.75V6.25ZM4.25 7C4.25 6.58579 4.58579 6.25 5 6.25V3.75C3.20507 3.75 1.75 5.20507 1.75 7H4.25ZM5 7.75C4.58579 7.75 4.25 7.41421 4.25 7H1.75C1.75 8.79493 3.20507 10.25 5 10.25V7.75ZM6.75 14V20H9.25V14H6.75ZM2 15.25H8V12.75H2V15.25ZM3.25 20V14H0.75V20H3.25ZM8 18.75H2V21.25H8V18.75ZM1.75 15V19H4.25V15H1.75ZM7 13.75H3V16.25H7V13.75ZM8.25 19V15H5.75V19H8.25ZM3 20.25H7V17.75H3V20.25ZM16.25 20V14H13.75V20H16.25ZM9 21.25H15V18.75H9V21.25ZM7.75 14V20H10.25V14H7.75ZM15 12.75H9V15.25H15V12.75ZM11.25 19V15H8.75V19H11.25ZM14 17.75H10V20.25H14V17.75ZM12.75 15V19H15.25V15H12.75ZM10 16.25H14V13.75H10V16.25ZM20.75 14V20H23.25V14H20.75ZM16 15.25H22V12.75H16V15.25ZM17.25 20V14H14.75V20H17.25ZM22 18.75H16V21.25H22V18.75ZM15.75 15V19H18.25V15H15.75ZM21 13.75H17V16.25H21V13.75ZM22.25 19V15H19.75V19H22.25ZM17 20.25H21V17.75H17V20.25Z" fill="currentColor" mask="url(#path-1-inside-1_1211_52580)"/> </svg>`

export const menuProjectManagementIcon = `<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" fill="none"> <path fill-rule="evenodd" clip-rule="evenodd" d="M3 6V18.5C3 18.7761 3.22386 19 3.5 19H20.5C20.7761 19 21 18.7761 21 18.5V6.5C21 6.22386 20.7761 6 20.5 6C14.6667 6 8.83333 6 3 6ZM2 4.5C2 3.67157 2.67157 3 3.5 3H9.5C9.63261 3 9.75979 3.05268 9.85355 3.14645L11.7071 5H20.5C21.3284 5 22 5.67157 22 6.5V18.5C22 19.3284 21.3284 20 20.5 20H3.5C2.67157 20 2 19.3284 2 18.5V4.5Z" fill="currentColor"/> </svg>`

export const menuPermissionIcon = `<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" fill="none"> <path fill-rule="evenodd" clip-rule="evenodd" d="M21.8536 8.14652L18.5001 4.79297L11.1644 12.1287C10.3024 11.4233 9.20064 11.0001 8 11.0001C5.23858 11.0001 3 13.2387 3 16.0001C3 18.7615 5.23858 21.0001 8 21.0001C10.7614 21.0001 13 18.7615 13 16.0001C13 14.7995 12.5768 13.6977 11.8715 12.8358L15.5001 9.20718L18.1465 11.8536L18.8536 11.1465L16.2072 8.50008L18.5001 6.20718L21.1465 8.85363L21.8536 8.14652ZM12 16.0001C12 18.2093 10.2091 20.0001 8 20.0001C5.79086 20.0001 4 18.2093 4 16.0001C4 13.791 5.79086 12.0001 8 12.0001C10.2091 12.0001 12 13.791 12 16.0001Z" fill="currentColor"/> </svg>`

export const menuDataSourceIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"> <path fill-rule="evenodd" clip-rule="evenodd" d="M4.51675 4.99591C4.14894 5.35398 4 5.69311 4 6C4 6.30689 4.14894 6.64602 4.51675 7.00409C4.88614 7.3637 5.44666 7.71058 6.17997 8.01253C7.64423 8.61547 9.70166 9 12 9C14.2983 9 16.3558 8.61547 17.82 8.01253C18.5533 7.71058 19.1139 7.3637 19.4832 7.00409C19.8511 6.64602 20 6.30689 20 6C20 5.69311 19.8511 5.35398 19.4832 4.99591C19.1139 4.6363 18.5533 4.28942 17.82 3.98747C16.3558 3.38453 14.2983 3 12 3C9.70166 3 7.64423 3.38453 6.17997 3.98747C5.44666 4.28942 4.88614 4.6363 4.51675 4.99591ZM20 7.88604C19.5276 8.2931 18.9112 8.64471 18.2008 8.93721C16.5887 9.60103 14.3961 10 12 10C9.60392 10 7.41135 9.60103 5.79922 8.93721C5.08885 8.64471 4.47242 8.2931 4 7.88604V10C4 10.3069 4.14894 10.646 4.51675 11.0041C4.88614 11.3637 5.44666 11.7106 6.17997 12.0125C7.64423 12.6155 9.70166 13 12 13C14.2983 13 16.3558 12.6155 17.82 12.0125C18.5533 11.7106 19.1139 11.3637 19.4832 11.0041C19.8511 10.646 20 10.3069 20 10V7.88604ZM20 11.886C19.5276 12.2931 18.9112 12.6447 18.2008 12.9372C16.5887 13.601 14.3961 14 12 14C9.60392 14 7.41135 13.601 5.79922 12.9372C5.08885 12.6447 4.47242 12.2931 4 11.886V14C4 14.3069 4.14894 14.646 4.51675 15.0041C4.88614 15.3637 5.44666 15.7106 6.17997 16.0125C7.64423 16.6155 9.70166 17 12 17C14.2983 17 16.3558 16.6155 17.82 16.0125C18.5533 15.7106 19.1139 15.3637 19.4832 15.0041C19.8511 14.646 20 14.3069 20 14V11.886ZM20 15.886C19.5276 16.2931 18.9112 16.6447 18.2008 16.9372C16.5887 17.601 14.3961 18 12 18C9.60392 18 7.41135 17.601 5.79922 16.9372C5.08885 16.6447 4.47242 16.2931 4 15.886V18C4 18.3069 4.14894 18.646 4.51675 19.0041C4.88614 19.3637 5.44666 19.7106 6.17997 20.0125C7.64423 20.6155 9.70166 21 12 21C14.2983 21 16.3558 20.6155 17.82 20.0125C18.5533 19.7106 19.1139 19.3637 19.4832 19.0041C19.8511 18.646 20 18.3069 20 18V15.886ZM21 18C21 18.6596 20.6732 19.2412 20.1808 19.7206C19.6899 20.1985 19.0057 20.6058 18.2008 20.9372C16.5887 21.601 14.3961 22 12 22C9.60392 22 7.41135 21.601 5.79922 20.9372C4.99433 20.6058 4.31005 20.1985 3.81919 19.7206C3.32676 19.2412 3 18.6596 3 18V6C3 5.3404 3.32676 4.75878 3.81919 4.27938C4.31005 3.80152 4.99433 3.39421 5.79922 3.06279C7.41135 2.39897 9.60392 2 12 2C14.3961 2 16.5887 2.39897 18.2008 3.06279C19.0057 3.39421 19.6899 3.80152 20.1808 4.27938C20.6732 4.75878 21 5.3404 21 6V18Z" fill="currentColor"/> </svg>`

export const menuSqlSearchIcon = `<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" fill="none"> <path fill-rule="evenodd" clip-rule="evenodd" d="M14.3333 8.26471C14.0572 8.26471 13.8333 8.04085 13.8333 7.76471V4H6V10H18V8.26471H14.3333ZM3 10H5V4V3H6H13.8333H14.3333L14.8333 3.5105L18.5103 7.26471L19 7.76471V8.26471V10H21V19H19V20V21H18H6H5V20V19H3V10ZM18 20V19H6V20H18ZM14.8333 4.93965L17.1105 7.26471H14.8333V4.93965ZM8.16667 11.3333C7.24619 11.3333 6.5 12.0795 6.5 13C6.5 13.9205 7.24619 14.6667 8.16667 14.6667C8.6269 14.6667 9 15.0398 9 15.5C9 15.9602 8.6269 16.3333 8.16667 16.3333C7.70643 16.3333 7.33333 15.9602 7.33333 15.5H6.5C6.5 16.4205 7.24619 17.1667 8.16667 17.1667C9.08714 17.1667 9.83333 16.4205 9.83333 15.5C9.83333 14.5795 9.08714 13.8333 8.16667 13.8333C7.70643 13.8333 7.33333 13.4602 7.33333 13C7.33333 12.5398 7.70643 12.1667 8.16667 12.1667C8.6269 12.1667 9 12.5398 9 13H9.83333C9.83333 12.0795 9.08714 11.3333 8.16667 11.3333ZM15.6667 11.3333V16.75C15.6667 16.9801 15.8532 17.1667 16.0833 17.1667H18.1667V16.3333H16.5V11.3333H15.6667ZM13.1667 13V15.5C13.1667 15.9602 12.7936 16.3333 12.3333 16.3333C11.8731 16.3333 11.5 15.9602 11.5 15.5V13C11.5 12.5398 11.8731 12.1667 12.3333 12.1667C12.7936 12.1667 13.1667 12.5398 13.1667 13ZM10.6667 13C10.6667 12.0795 11.4129 11.3333 12.3333 11.3333C13.2538 11.3333 14 12.0795 14 13V15.5C14 15.8091 13.9159 16.0985 13.7692 16.3467L14.2946 16.872L13.7054 17.4613L13.18 16.9359C12.9319 17.0825 12.6424 17.1667 12.3333 17.1667C11.4129 17.1667 10.6667 16.4205 10.6667 15.5V13Z" fill="currentColor"/> </svg>`

export const menuVirtualTableIcon = `<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" fill="none"> <path fill-rule="evenodd" clip-rule="evenodd" d="M4 4.5C4 4.22386 4.22386 4 4.5 4H8V5H9V4H15V5H16V4H19.5C19.7761 4 20 4.22386 20 4.5V8H19V9H20V19.5C20 19.7761 19.7761 20 19.5 20H16V19H15V20H9V19H8V20H4.5C4.22386 20 4 19.7761 4 19.5V9H5V8H4V4.5ZM4.5 3C3.67157 3 3 3.67157 3 4.5V19.5C3 20.3284 3.67157 21 4.5 21H19.5C20.3284 21 21 20.3284 21 19.5V4.5C21 3.67157 20.3284 3 19.5 3H4.5ZM8 7V8H7V9H8H9V8V7H8ZM8 11V13H9V11H8ZM8 15V17H9V15H8ZM11 9H13V8H11V9ZM16 9H17V8H16V7H15V8V9H16ZM15 13V11H16V13H15ZM15 17V15H16V17H15Z" fill="currentColor"/> </svg>`

export const menuIndexSceneIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="lightning-line">
<path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M16.9743 2.65811C17.0252 2.50564 16.9996 2.33803 16.9056 2.20764C16.8116 2.07726 16.6607 2 16.5 2H8.5C8.26166 2 8.05646 2.16823 8.00971 2.40194L6.00971 12.4019C5.98034 12.5488 6.01836 12.7012 6.11333 12.817C6.2083 12.9328 6.35021 13 6.5 13H9.8767L8.01191 21.3915C7.96107 21.6203 8.07598 21.8537 8.28831 21.953C8.50064 22.0522 8.75341 21.9906 8.89632 21.8049L18.8963 8.80486C19.0125 8.65387 19.0327 8.45002 18.9486 8.27912C18.8644 8.10823 18.6905 8 18.5 8H15.1937L16.9743 2.65811ZM13.8063 9L15.8063 3H8.90991L7.10991 12H11.1233L9.47655 19.4104L17.4846 9H13.8063Z" fill="#424242"/></g></svg>`

export const menuMaterializationIcon = `<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" fill="none"> <path fill-rule="evenodd" clip-rule="evenodd" d="M16.9743 2.65811C17.0252 2.50564 16.9996 2.33803 16.9056 2.20764C16.8116 2.07726 16.6607 2 16.5 2H8.5C8.26166 2 8.05646 2.16823 8.00971 2.40194L6.00971 12.4019C5.98034 12.5488 6.01836 12.7012 6.11333 12.817C6.2083 12.9328 6.35021 13 6.5 13H9.8767L8.01191 21.3915C7.96107 21.6203 8.07598 21.8537 8.28831 21.953C8.50064 22.0522 8.75341 21.9906 8.89632 21.8049L18.8963 8.80486C19.0125 8.65387 19.0327 8.45002 18.9486 8.27912C18.8644 8.10823 18.6905 8 18.5 8H15.1937L16.9743 2.65811ZM13.8063 9L15.8063 3H8.90991L7.10991 12H11.1233L9.47655 19.4104L17.4846 9H13.8063Z" fill="currentColor"/> </svg>`

export const menuOperationIcon = `<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" fill="none"> <path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 3C3.22386 3 3 3.22386 3 3.5V15.5C3 15.7761 3.22386 16 3.5 16H11.5V20H5V21H19V20H12.5V16H20.5C20.7761 16 21 15.7761 21 15.5V3.5C21 3.22386 20.7761 3 20.5 3H3.5ZM4 15V4H20V15H4ZM12.5 14V7H11.5V14H12.5ZM14.5 14H15.5V8H14.5V14ZM18.5 9V14H17.5V9H18.5ZM9.5 14V11H8.5V14H9.5ZM6.5 9V14H5.5V9H6.5Z" fill="currentColor"/> </svg>`

export const menuMetricmodelIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g id="model-duotone"> <path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M11.7465 2.06903C11.903 1.97699 12.097 1.97699 12.2535 2.06903L20.7535 7.06903C20.9062 7.15887 21 7.32282 21 7.5V16.5C21 16.6772 20.9062 16.8411 20.7535 16.931L12.2535 21.931C12.097 22.023 11.903 22.023 11.7465 21.931L3.24649 16.931C3.09377 16.8411 3 16.6772 3 16.5V7.5C3 7.32282 3.09377 7.15887 3.24649 7.06903L11.7465 2.06903ZM4 8.37421V16.214L11.5 20.6258V12.786L4 8.37421ZM12.5 12.786V20.6258L20 16.214V8.37421L12.5 12.786ZM19.5138 7.5L12 11.9199L4.48615 7.5L12 3.08009L19.5138 7.5Z" fill="#424242"/> </g> </svg>`

export const loginProxyIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="none"> <path fill-rule="evenodd" clip-rule="evenodd" d="M8.33342 3.33333V5H9.58341H10.4167H11.6667V3.33333H8.33342ZM10.4167 5.83333H12.0834C12.3135 5.83333 12.5001 5.64679 12.5001 5.41667V2.91667C12.5001 2.68655 12.3135 2.5 12.0834 2.5H7.91675C7.68663 2.5 7.50008 2.68655 7.50008 2.91667V5.41667C7.50008 5.64679 7.68663 5.83333 7.91675 5.83333H9.58341V9.16667H3.75008C3.51996 9.16667 3.33341 9.35321 3.33341 9.58333V14.1667H2.08341C1.8533 14.1667 1.66675 14.3532 1.66675 14.5833V17.0833C1.66675 17.3135 1.8533 17.5 2.08341 17.5H5.41675C5.64687 17.5 5.83341 17.3135 5.83341 17.0833V14.5833C5.83341 14.3532 5.64687 14.1667 5.41675 14.1667H4.16675V10H9.58341V14.1667H8.33341C8.1033 14.1667 7.91675 14.3532 7.91675 14.5833V17.0833C7.91675 17.3135 8.1033 17.5 8.33342 17.5H11.6667C11.8969 17.5 12.0834 17.3135 12.0834 17.0833V14.5833C12.0834 14.3532 11.8969 14.1667 11.6667 14.1667H10.4167V10H15.8334V14.1667H14.5834C14.3533 14.1667 14.1667 14.3532 14.1667 14.5833V17.0833C14.1667 17.3135 14.3533 17.5 14.5834 17.5H17.9167C18.1469 17.5 18.3334 17.3135 18.3334 17.0833V14.5833C18.3334 14.3532 18.1469 14.1667 17.9167 14.1667H16.6667V9.58333C16.6667 9.35321 16.4802 9.16667 16.2501 9.16667H10.4167V5.83333ZM10.4167 15H9.58341H8.75008V16.6667H11.2501V15H10.4167ZM15.0001 16.6667V15H17.5001V16.6667H15.0001ZM2.50008 15V16.6667H5.00008V15H2.50008Z" fill="currentColor"/> </svg>`

export const saveChatIcon = `<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" fill="currentColor"> <path fill-rule="evenodd" clip-rule="evenodd" d="M4.5 4C4.22386 4 4 4.22386 4 4.5V19.5C4 19.7761 4.22386 20 4.5 20H8V14.5C8 14.2239 8.22386 14 8.5 14H15.5C15.7761 14 16 14.2239 16 14.5V20H19.5C19.7761 20 20 19.7761 20 19.5V8.91421C20 8.78161 19.9473 8.65443 19.8536 8.56066L16 4.70711V8.5C16 8.77614 15.7761 9 15.5 9H8.5C8.22386 9 8 8.77614 8 8.5V4H4.5ZM9 4V8H15V4H9ZM15 20V15H9V20H15ZM3 4.5C3 3.67157 3.67157 3 4.5 3H15.5C15.6326 3 15.7598 3.05268 15.8536 3.14645L20.5607 7.85355C20.842 8.13486 21 8.51639 21 8.91421V19.5C21 20.3284 20.3284 21 19.5 21H4.5C3.67157 21 3 20.3284 3 19.5V4.5Z" fill="currentColor"/> </svg>`

export const clearHistoryMessageIcon = `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g id="format-fill"> <path id="Icon" opacity="0.3" d="M8.25 7.5C8.25 7.5 6.75 9 2.25 10.5C2.25 11.25 2.625 12.375 3 12.75C6 12.75 8.72727 11.6053 8.72727 11.6053L9.375 12.2763C9.375 12.2763 7.05682 14.2105 3.75 14.25C5.55682 17.2105 8.25 19.5 10.6705 21C15 20.25 17.25 16.5 17.25 16.5L8.25 7.5Z" fill="currentColor"/> <path id="Icon_2" d="M17.1115 10.3483L20.25 13.4869L18 15.75L9 6.75L11.2636 4.5L14.402 7.63856L18.2902 3.75L21 6.45941L17.1115 10.3483Z" fill="currentColor"/> </g> </svg> `

export const backToHomeIcon = `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M14 16V21H21V10L19 8.44444V5H16V6.11111L12 3L3 10V21H10V16H14ZM17 8.15575L12 4.26686L4 10.4891V20H9V15H15V20H20V10.4891L18 8.93353V6H17V8.15575Z" fill="currentColor"/> </svg> `

export const newLlmIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20.1897 7.38792C20.7272 8.31878 20.543 9.72507 19.4819 11.3686C18.4401 12.9822 16.6391 14.6684 14.3122 16.0119C11.9853 17.3553 9.62446 18.0719 7.70613 18.1673C5.75223 18.2645 4.44227 17.7209 3.90483 16.79C3.3674 15.8591 3.55156 14.4529 4.61268 12.8093C5.65449 11.1957 7.45545 9.50948 9.78238 8.16603C12.1093 6.82258 14.4701 6.10602 16.3884 6.01059C18.3423 5.91339 19.6523 6.45705 20.1897 7.38792Z" stroke="currentColor" stroke-width="1.2"/>
<path d="M12.046 21.4911C10.9711 21.4911 9.84529 20.6285 8.95252 18.8877C8.076 17.1787 7.51615 14.7759 7.51615 12.089C7.51615 9.4021 8.076 6.99931 8.95252 5.29027C9.84529 3.54955 10.9711 2.68691 12.046 2.68691C13.1208 2.68691 14.2466 3.54955 15.1394 5.29027C16.0159 6.99931 16.5758 9.4021 16.5758 12.089C16.5758 14.7759 16.0159 17.1787 15.1394 18.8877C14.2466 20.6285 13.1208 21.4911 12.046 21.4911Z" stroke="currentColor" stroke-width="1.2"/>
<path d="M20.1888 16.7901C19.6514 17.721 18.3414 18.2647 16.3875 18.1675C14.4692 18.072 12.1084 17.3555 9.78147 16.012C7.45455 14.6686 5.65359 12.9823 4.61178 11.3687C3.55065 9.72521 3.36649 8.31892 3.90393 7.38806C4.44136 6.45719 5.75133 5.91353 7.70522 6.01073C9.62355 6.10616 11.9844 6.82272 14.3113 8.16617C16.6382 9.50962 18.4392 11.1959 19.481 12.8095C20.5421 14.453 20.7263 15.8593 20.1888 16.7901Z" stroke="currentColor" stroke-width="1.2"/>
</svg>`

export const columnStringIcon = `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="text-line"><path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M6.00016 9.33333V11.3333H6.66683V10.8241C7.02067 11.1408 7.48793 11.3333 8.00016 11.3333C9.10473 11.3333 10.0002 10.4379 10.0002 9.33333V8.66667C10.0002 7.5621 9.10473 6.66667 8.00016 6.66667C7.48793 6.66667 7.02067 6.85924 6.66683 7.17593V4H6.00016V8.66667V9.33333ZM6.66683 8.66667V9.33333C6.66683 10.0697 7.26378 10.6667 8.00016 10.6667C8.73654 10.6667 9.3335 10.0697 9.3335 9.33333V8.66667C9.3335 7.93029 8.73654 7.33333 8.00016 7.33333C7.26378 7.33333 6.66683 7.93029 6.66683 8.66667ZM1.54551 7.31658C1.96164 6.90045 2.52604 6.66667 3.11454 6.66667H3.66683C4.5873 6.66667 5.3335 7.41286 5.3335 8.33333V10C5.3335 10.2823 5.493 10.5404 5.74552 10.6667L5.8159 10.7019L5.51776 11.2981L5.44738 11.263C5.2025 11.1405 5.0038 10.9526 4.86816 10.7267C4.48376 11.1112 3.95851 11.3333 3.40386 11.3333H2.66683C1.93045 11.3333 1.3335 10.7364 1.3335 10C1.3335 9.26363 1.9304 8.66667 2.6668 8.66667H3.3335C3.7589 8.66667 4.11847 8.52467 4.37645 8.37725C4.49517 8.30941 4.58992 8.24179 4.65657 8.18954C4.58685 7.70538 4.17029 7.33333 3.66683 7.33333H3.11454C2.70285 7.33333 2.30802 7.49688 2.01691 7.78799L1.90253 7.90237L1.43113 7.43096L1.54551 7.31658ZM4.66683 8.97875C4.3432 9.15723 3.88475 9.33333 3.3335 9.33333H2.6668C2.29862 9.33333 2.00016 9.6318 2.00016 10C2.00016 10.3682 2.29864 10.6667 2.66683 10.6667H3.40386C3.8732 10.6667 4.31147 10.4321 4.57181 10.0416L4.66683 9.89907V8.97875ZM10.6668 8.66667C10.6668 7.5621 11.5623 6.66667 12.6668 6.66667C13.3428 6.66667 13.9405 7.00205 14.3025 7.51549L13.6959 7.8188C13.4514 7.52232 13.0812 7.33333 12.6668 7.33333C11.9305 7.33333 11.3335 7.93029 11.3335 8.66667V9.33333C11.3335 10.0697 11.9305 10.6667 12.6668 10.6667C13.0812 10.6667 13.4514 10.4777 13.6959 10.1812L14.3025 10.4845C13.9405 10.998 13.3428 11.3333 12.6668 11.3333C11.5623 11.3333 10.6668 10.4379 10.6668 9.33333V8.66667Z" fill="#858585"/></g></svg>`

export const columnCalendarIcon = `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="date-line"><g id="Icon"><path d="M7.33333 8H8.66667V9.33333H7.33333V8Z" fill="#858585"/><path d="M12 9.33333V8H10.6667V9.33333H12Z" fill="#858585"/><path d="M5.33333 10.6667V12H4V10.6667H5.33333Z" fill="#858585"/><path d="M8.66667 10.6667H7.33333V12H8.66667V10.6667Z" fill="#858585"/><path fill-rule="evenodd" clip-rule="evenodd" d="M4.66667 3.33333H3C2.44772 3.33333 2 3.78105 2 4.33333V13C2 13.5523 2.44772 14 3 14H13C13.5523 14 14 13.5523 14 13V4.33333C14 3.78105 13.5523 3.33333 13 3.33333H11.3333V2H10.6667V3.33333H5.33333V2H4.66667V3.33333ZM3 4C2.81591 4 2.66667 4.14924 2.66667 4.33333V6H13.3333V4.33333C13.3333 4.14924 13.1841 4 13 4H3ZM13.3333 6.66667H2.66667V13C2.66667 13.1841 2.81591 13.3333 3 13.3333H13C13.1841 13.3333 13.3333 13.1841 13.3333 13V6.66667Z" fill="#858585"/></g></g></svg>`

export const columnNumberIcon = `<svg  viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="numbers-line"><path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M3.12775 3.35864C3.25231 3.41023 3.33352 3.53177 3.33352 3.66659V11.9999H2.66685V4.47133L1.90256 5.23563L1.43115 4.76423L2.76449 3.43089C2.85982 3.33556 3.00319 3.30704 3.12775 3.35864ZM7.00016 3.99993C6.07969 3.99993 5.3335 4.74612 5.3335 5.6666V5.99993H4.66683V5.6666C4.66683 4.37793 5.7115 3.33326 7.00016 3.33326C8.28883 3.33326 9.3335 4.37793 9.3335 5.6666C9.3335 6.21671 9.14271 6.72316 8.82397 7.12211L5.66669 11.3333H9.3335V11.9999H5.00016C4.87392 11.9999 4.7585 11.9286 4.70203 11.8157C4.64557 11.7028 4.65773 11.5676 4.73346 11.4666L8.29376 6.71794L8.30055 6.70919C8.52986 6.42362 8.66683 6.06156 8.66683 5.6666C8.66683 4.74612 7.92064 3.99993 7.00016 3.99993ZM10.6668 5.6666C10.6668 4.74612 11.413 3.99993 12.3335 3.99993C13.254 3.99993 14.0002 4.74612 14.0002 5.6666C14.0002 6.58707 13.254 7.33326 12.3335 7.33326H12.0002V7.99993H12.3335C13.254 7.99993 14.0002 8.74612 14.0002 9.6666C14.0002 10.5871 13.254 11.3333 12.3335 11.3333C11.413 11.3333 10.6668 10.5871 10.6668 9.6666H10.0002C10.0002 10.9553 11.0448 11.9999 12.3335 11.9999C13.6222 11.9999 14.6668 10.9553 14.6668 9.6666C14.6668 8.81782 14.2136 8.0749 13.536 7.6666C14.2136 7.2583 14.6668 6.51537 14.6668 5.6666C14.6668 4.37793 13.6222 3.33326 12.3335 3.33326C11.0448 3.33326 10.0002 4.37793 10.0002 5.6666H10.6668Z" fill="#858585"/></g></svg>`

export const warningIcon = `<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g id="alert-triangle-duotone"> <path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M12.4998 2L22.4998 20H2.49976L12.4998 2ZM11.9998 13.999V7.99902H12.9998V13.999H11.9998ZM13.4997 15.9994C13.4997 16.5516 13.052 16.9994 12.4997 16.9994C11.9474 16.9994 11.4997 16.5516 11.4997 15.9994C11.4997 15.4471 11.9474 14.9994 12.4997 14.9994C13.052 14.9994 13.4997 15.4471 13.4997 15.9994Z" fill="#FF7A19"/> </g> </svg>`

export const folderIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g id="folder-duotone"> <path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M3.5 3C2.67157 3 2 3.67157 2 4.5V18.5C2 19.3284 2.67157 20 3.5 20H20.5C21.3284 20 22 19.3284 22 18.5V6.5C22 5.67157 21.3284 5 20.5 5H11.7071L9.85355 3.14645C9.75978 3.05268 9.63261 3 9.5 3H3.5ZM11.7073 6.00016C11.4421 6.00016 11.1877 5.89481 11.0002 5.70727L9.29306 4.00016H3.50016C3.22402 4.00016 3.00016 4.22402 3.00016 4.50016V9.00016H21.0002V6.50016C21.0002 6.22402 20.7763 6.00016 20.5002 6.00016H11.7073Z" fill="#FFC55D"/> <path id="Icon_2" opacity="0.4" fill-rule="evenodd" clip-rule="evenodd" d="M11 5.70711C11.1875 5.89464 11.4419 6 11.7071 6H20.5C20.7761 6 21 6.22386 21 6.5V9H3V4.5C3 4.22386 3.22386 4 3.5 4H9.29289L11 5.70711Z" fill="#FDB022"/> </g> </svg>`

export const projectEditIcon = `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <g id="edit-line"> <path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M13.4554 2.62204C13.6181 2.45932 13.8819 2.45932 14.0446 2.62204L17.378 5.95537C17.5407 6.11809 17.5407 6.38191 17.378 6.54463L6.54463 17.378C6.46649 17.4561 6.36051 17.5 6.25 17.5H2.91667C2.68655 17.5 2.5 17.3135 2.5 17.0833V13.75C2.5 13.6395 2.5439 13.5335 2.62204 13.4554L13.4554 2.62204ZM11.8393 5.41667L14.5833 8.16074L16.4941 6.25L13.75 3.50592L11.8393 5.41667ZM13.9941 8.75L11.25 6.00592L3.33333 13.9226V16.6667H6.07741L13.9941 8.75Z" fill="#503CE4"/> </g> </svg>`

export const robotIcon = `<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg"> <g id="general/robot"> <path id="Vector" fill-rule="evenodd" clip-rule="evenodd" d="M7.25 1.25H4.75V2.25H5.5V2.75H1.75V10.5H10.25V2.75H6.5V2.25H7.25V1.25ZM2.75 9.5V3.75H9.25V9.5H2.75ZM0.25 5.25V8H1.25V5.25H0.25ZM10.75 5.25V8H11.75V5.25H10.75ZM5.25 6H4V7.25H5.25V6ZM8 6H6.75V7.25H8V6Z" fill="#3491FA"/> </g> </svg> `

export const bookIcon = `<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg"> <g id="general/book"> <path id="Vector" fill-rule="evenodd" clip-rule="evenodd" d="M1.46121 1.34183C1.59368 1.24811 1.76339 1.2245 1.91641 1.27851L6 2.71978L10.0836 1.27851C10.2366 1.2245 10.4063 1.24811 10.5388 1.34183C10.6713 1.43556 10.75 1.58773 10.75 1.75V8.75C10.75 8.962 10.6163 9.15094 10.4164 9.2215L6.16641 10.7215C6.05873 10.7595 5.94127 10.7595 5.83359 10.7215L1.58359 9.2215C1.38368 9.15094 1.25 8.962 1.25 8.75V1.75C1.25 1.58773 1.32875 1.43556 1.46121 1.34183ZM5.5 3.60376L2.25 2.4567V8.39625L5.5 9.54331V3.60376ZM6.5 9.54331L9.75 8.39625V2.4567L6.5 3.60376V9.54331ZM3.16817 3.40413L4.91817 4.02913L4.58183 4.97087L2.83183 4.34587L3.16817 3.40413ZM9.16817 4.34587L7.41817 4.97087L7.08183 4.02913L8.83183 3.40413L9.16817 4.34587ZM3.16817 5.15413L4.91817 5.77913L4.58183 6.72087L2.83183 6.09587L3.16817 5.15413ZM9.16817 6.09587L7.41817 6.72087L7.08183 5.77913L8.83183 5.15413L9.16817 6.09587ZM3.16817 6.90413L4.91817 7.52913L4.58183 8.47088L2.83183 7.84588L3.16817 6.90413ZM9.16817 7.84588L7.41817 8.47088L7.08183 7.52913L8.83183 6.90413L9.16817 7.84588Z" fill="#0FC6C2"/> </g> </svg>`

export const dimensionIcon = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="dimension-category-line">
<path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M2.3864 2.02354C2.27988 2.00637 2.17163 2.04181 2.0959 2.11867C2.02017 2.19553 1.98633 2.30429 2.00508 2.41055L2.71218 6.41749C2.73384 6.54019 2.82211 6.64057 2.94103 6.67773C3.05995 6.71488 3.18967 6.68262 3.27732 6.59408L4.70265 5.15441L7.66266 8.13343L7.6409 10.6802L5.60781 10.6903C5.48293 10.691 5.36888 10.7613 5.31231 10.8727C5.25574 10.984 5.26614 11.1176 5.33926 11.2188L7.72979 14.5285C7.79297 14.616 7.89458 14.6675 8.00247 14.6667C8.11037 14.6659 8.21121 14.6129 8.27308 14.5245L8.33333 14.4384V13.2759L7.99575 13.7582L6.25908 11.3538L8.33333 11.3434V10.6768L8.32927 10.6768V8.14291L11.3431 5.15274L12.7701 6.59411C12.8577 6.68265 12.9874 6.71491 13.1064 6.67775C13.2253 6.64059 13.3135 6.54022 13.3352 6.41752L14.0423 2.41058C14.0611 2.30432 14.0272 2.19556 13.9515 2.1187C13.8758 2.04184 13.7675 2.00639 13.661 2.02357L9.63032 2.67351C9.50704 2.69339 9.4052 2.78051 9.36648 2.89924C9.32776 3.01796 9.35865 3.14837 9.44651 3.23711L10.874 4.67899L8.00169 7.5288L5.1717 4.68064L6.60087 3.23708C6.68873 3.14834 6.71962 3.01793 6.6809 2.89921C6.64218 2.78048 6.54034 2.69336 6.41706 2.67348L2.3864 2.02354ZM5.67125 3.2285L3.25676 5.66729L2.74306 2.75633L5.67125 3.2285ZM12.7906 5.66731L10.3761 3.22853L13.3043 2.75636L12.7906 5.66731Z" fill="#0D7254"/>
<g id="Icon_2">
<path d="M14.6668 10H9.3335V10.6667H14.6668V10Z" fill="#0D7254"/>
<path d="M9.3335 12H14.6668V12.6667H9.3335V12Z" fill="#0D7254"/>
<path d="M9.3335 14H14.6668V14.6667H9.3335V14Z" fill="#0D7254"/>
</g>
</g>
</svg>`

export const measureIcon = `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="surveillance-line">
<g id="Icon">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.14884 7.32279L11.2359 5.23577L10.7645 4.76436L8.67744 6.85139C8.47896 6.73416 8.24729 6.66673 8.00016 6.66673C7.26378 6.66673 6.66683 7.26369 6.66683 8.00007C6.66683 8.73645 7.26378 9.3334 8.00016 9.3334C8.73654 9.3334 9.3335 8.73645 9.3335 8.00007C9.3335 7.75294 9.26607 7.52127 9.14884 7.32279ZM8.00016 7.3334C7.63197 7.3334 7.3335 7.63188 7.3335 8.00007C7.3335 8.36826 7.63197 8.66673 8.00016 8.66673C8.36835 8.66673 8.66683 8.36826 8.66683 8.00007C8.66683 7.81588 8.59258 7.64967 8.47157 7.52866C8.35056 7.40765 8.18435 7.3334 8.00016 7.3334Z" fill="#1B3373"/>
<path d="M8.00016 3.33337C8.67858 3.33337 9.32318 3.47814 9.90477 3.73846L9.39374 4.24949C8.9598 4.08819 8.49027 4.00004 8.00016 4.00004C5.79102 4.00004 4.00016 5.7909 4.00016 8.00004H3.3335C3.3335 5.42271 5.42283 3.33337 8.00016 3.33337Z" fill="#1B3373"/>
<path d="M12.0002 8.00004C12.0002 7.50996 11.912 7.04046 11.7507 6.60654L12.2618 6.0955C12.5221 6.67707 12.6668 7.32165 12.6668 8.00004H12.0002Z" fill="#1B3373"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.00016 14.6667C11.6821 14.6667 14.6668 11.6819 14.6668 8.00004C14.6668 4.31814 11.6821 1.33337 8.00016 1.33337C4.31826 1.33337 1.3335 4.31814 1.3335 8.00004C1.3335 11.6819 4.31826 14.6667 8.00016 14.6667ZM8.00016 14C11.3139 14 14.0002 11.3137 14.0002 8.00004C14.0002 4.68633 11.3139 2.00004 8.00016 2.00004C4.68645 2.00004 2.00016 4.68633 2.00016 8.00004C2.00016 11.3137 4.68645 14 8.00016 14Z" fill="#1B3373"/>
</g>
</g>
</svg>`

export const metricModelIcon = `
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="model-fill">
<g id="Icon">
<path d="M9.78874 1.72415C9.91914 1.64745 10.0809 1.64745 10.2113 1.72415L16.7744 5.58483C16.9057 5.66207 16.9059 5.8519 16.7748 5.9294L10.2525 9.78345C10.0961 9.87585 9.90198 9.87619 9.74529 9.78434L3.19789 5.94621C3.06619 5.86901 3.06605 5.67868 3.19763 5.60128L9.78874 1.72415Z" fill="#6A58EC"/>
<path d="M2.80114 6.6796C2.66782 6.60144 2.5 6.69759 2.5 6.85214V13.75C2.5 13.8976 2.57814 14.0342 2.70541 14.1091L9.28193 17.9776C9.41526 18.0561 9.58334 17.9599 9.58334 17.8053V10.9418C9.58334 10.7644 9.48929 10.6002 9.3362 10.5105L2.80114 6.6796Z" fill="#6A58EC"/>
<path d="M10.4167 17.8053C10.4167 17.9599 10.5847 18.0561 10.7181 17.9776L17.2946 14.1091C17.4219 14.0342 17.5 13.8976 17.5 13.75V6.81929C17.5 6.66443 17.3316 6.56832 17.1983 6.6471L10.6623 10.5093C10.5101 10.5992 10.4167 10.7629 10.4167 10.9397V17.8053Z" fill="#6A58EC"/>
</g>
</g>
</svg>`

export const catalogIcon = `
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="cloud-storage-line">
<path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M5.98793 7.20401C6.48024 5.45123 8.09057 4.16671 9.99984 4.16671C11.9091 4.16671 13.5194 5.45123 14.0118 7.20401L14.0915 7.48804L14.3859 7.50713C16.1244 7.61989 17.4998 9.06617 17.4998 10.8334C17.4998 12.3866 16.4375 13.6917 14.9998 14.0617V12.9167C14.9998 12.6866 14.8133 12.5 14.5832 12.5H5.41651C5.18639 12.5 4.99984 12.6866 4.99984 12.9167V14.0617C3.56215 13.6917 2.49984 12.3866 2.49984 10.8334C2.49984 9.06617 3.87529 7.61989 5.61376 7.50713L5.90816 7.48804L5.98793 7.20401ZM4.99984 14.9167C3.09797 14.5306 1.6665 12.8492 1.6665 10.8334C1.6665 8.72269 3.23569 6.97881 5.27135 6.7043C5.947 4.74299 7.80831 3.33337 9.99984 3.33337C12.1914 3.33337 14.0527 4.74299 14.7283 6.7043C16.764 6.97881 18.3332 8.72269 18.3332 10.8334C18.3332 12.8492 16.9017 14.5306 14.9998 14.9167V16.25C14.9998 16.4802 14.8133 16.6667 14.5832 16.6667H5.41651C5.18639 16.6667 4.99984 16.4802 4.99984 16.25V14.9167ZM5.83317 15L5.83317 15.8334H14.1665L14.1665 15V14.1667L14.1665 13.3334H5.83317L5.83317 14.1667V15ZM6.66651 14.5834C6.66651 14.3533 6.85306 14.1667 7.08317 14.1667H7.91651C8.14663 14.1667 8.33317 14.3533 8.33317 14.5834C8.33317 14.8135 8.14663 15 7.91651 15H7.08317C6.85306 15 6.66651 14.8135 6.66651 14.5834Z" fill="#171717"/>
</g>
</svg>`

export const databaseIcon = `
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="dataset-line">
<path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M9.80505 2.54842C9.927 2.48386 10.073 2.48386 10.195 2.54842L17.2783 6.29842C17.4147 6.37063 17.5 6.51233 17.5 6.66667C17.5 6.821 17.4147 6.9627 17.2783 7.03491L14.8257 8.33333L17.2783 9.63176C17.4147 9.70397 17.5 9.84566 17.5 10C17.5 10.1543 17.4147 10.296 17.2783 10.3682L14.8257 11.6667L17.2783 12.9651C17.4147 13.0373 17.5 13.179 17.5 13.3333C17.5 13.4877 17.4147 13.6294 17.2783 13.7016L10.195 17.4516C10.073 17.5161 9.927 17.5161 9.80505 17.4516L2.72171 13.7016C2.58531 13.6294 2.5 13.4877 2.5 13.3333C2.5 13.179 2.58531 13.0373 2.72171 12.9651L5.17429 11.6667L2.72171 10.3682C2.58531 10.296 2.5 10.1543 2.5 10C2.5 9.84566 2.58531 9.70397 2.72171 9.63176L5.17429 8.33333L2.72171 7.03491C2.58531 6.9627 2.5 6.821 2.5 6.66667C2.5 6.51233 2.58531 6.37063 2.72171 6.29842L9.80505 2.54842ZM6.06481 8.80479L3.80719 10L10 13.2785L16.1928 10L13.9352 8.80479L10.195 10.7849C10.073 10.8495 9.927 10.8495 9.80505 10.7849L6.06481 8.80479ZM6.06481 12.1381L3.80719 13.3333L10 16.6119L16.1928 13.3333L13.9352 12.1381L10.195 14.1182C10.073 14.1828 9.927 14.1828 9.80505 14.1182L6.06481 12.1381ZM3.80719 6.66667L10 9.94521L16.1928 6.66667L10 3.38812L3.80719 6.66667Z" fill="#1C1C1E"/>
</g>
</svg>`

export const virtualTableIcon = `
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="virtual-table-line">
<path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M3.33333 3.75C3.33333 3.51988 3.51988 3.33333 3.75 3.33333L6.66667 3.33333V4.16667H7.5V3.33333H12.5V4.16667H13.3333V3.33333L16.25 3.33333C16.4801 3.33333 16.6667 3.51988 16.6667 3.75V6.66667H15.8333V7.5H16.6667V16.25C16.6667 16.4801 16.4801 16.6667 16.25 16.6667H13.3333V15.8333H12.5V16.6667H7.5V15.8333H6.66667V16.6667H3.75C3.51988 16.6667 3.33333 16.4801 3.33333 16.25L3.33333 7.5H4.16667V6.66667H3.33333L3.33333 3.75ZM3.75 2.5C3.05964 2.5 2.5 3.05964 2.5 3.75V16.25C2.5 16.9404 3.05964 17.5 3.75 17.5H16.25C16.9404 17.5 17.5 16.9404 17.5 16.25V3.75C17.5 3.05964 16.9404 2.5 16.25 2.5H3.75ZM6.66667 5.83333V6.66667H5.83333V7.5H6.66667H7.5V6.66667V5.83333H6.66667ZM6.66667 9.16667V10.8333H7.5V9.16667H6.66667ZM6.66667 12.5V14.1667H7.5V12.5H6.66667ZM9.16667 7.5H10.8333V6.66667H9.16667V7.5ZM13.3333 7.5H14.1667V6.66667H13.3333V5.83333H12.5V6.66667V7.5H13.3333ZM12.5 10.8333V9.16667H13.3333V10.8333H12.5ZM12.5 14.1667V12.5H13.3333V14.1667H12.5Z" fill="#1C1C1E"/>
</g>
</svg>`

export const timeIcon = `
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="datetime-line">
<path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M5.83333 4.16667H3.75C3.05964 4.16667 2.5 4.72631 2.5 5.41667V16.25C2.5 16.9404 3.05964 17.5 3.75 17.5H10V16.6667H3.75C3.51988 16.6667 3.33333 16.4801 3.33333 16.25V8.33333H16.6667V10H17.5V5.41667C17.5 4.72631 16.9404 4.16667 16.25 4.16667H14.1667V2.5H13.3333V4.16667H6.66667V2.5H5.83333V4.16667ZM16.6667 7.5V5.41667C16.6667 5.18655 16.4801 5 16.25 5H3.75C3.51988 5 3.33333 5.18655 3.33333 5.41667V7.5H16.6667ZM13.75 16.6667C15.3608 16.6667 16.6667 15.3608 16.6667 13.75C16.6667 12.1392 15.3608 10.8333 13.75 10.8333C12.1392 10.8333 10.8333 12.1392 10.8333 13.75C10.8333 15.3608 12.1392 16.6667 13.75 16.6667ZM13.75 17.5C15.8211 17.5 17.5 15.8211 17.5 13.75C17.5 11.6789 15.8211 10 13.75 10C11.6789 10 10 11.6789 10 13.75C10 15.8211 11.6789 17.5 13.75 17.5ZM14.1667 11.6667V13.3333H15.8333V14.1667H13.75C13.5199 14.1667 13.3333 13.9801 13.3333 13.75V11.6667H14.1667Z" fill="#1C1C1E"/>
</g>
</svg>`

export const scenarioUserIcon = `
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 39794">
<g id="Mask group">
<mask id="mask0_844_81392" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
<circle id="Ellipse 229" cx="10" cy="10" r="10" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_844_81392)">
<rect id="Rectangle 8030" x="-1.42871" width="22.8571" height="22.8571" fill="#C7B3D6"/>
<ellipse id="Ellipse 228" cx="10" cy="18.2142" rx="7.5" ry="6.42857" fill="url(#paint0_linear_844_81392)"/>
</g>
</g>
<path id="Ellipse 227" d="M13.5713 8.21418C13.5713 10.5811 11.8924 12.4999 9.8213 12.4999C7.75024 12.4999 6.07129 10.5811 6.07129 8.21418C6.07129 5.84725 7.75024 3.92847 9.8213 3.92847C11.8924 3.92847 13.5713 5.84725 13.5713 8.21418Z" fill="url(#paint1_radial_844_81392)"/>
</g>
<defs>
<linearGradient id="paint0_linear_844_81392" x1="10" y1="11.7856" x2="10" y2="19.8214" gradientUnits="userSpaceOnUse">
<stop stop-color="#F1D6F2"/>
<stop offset="0.25" stop-color="#F5EFF5"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<radialGradient id="paint1_radial_844_81392" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(9.64272 8.21418) rotate(90) scale(6.96429 6.09375)">
<stop stop-color="#F7E8F9"/>
<stop offset="1" stop-color="white"/>
</radialGradient>
</defs>
</svg>`

export const columnNotUpdate = `<svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="refresh-data-line">
<path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M10.369 5L8.58037 3.21133L9.16963 2.62207L11.6696 5.12207C11.7478 5.20021 11.7917 5.30619 11.7917 5.4167C11.7917 5.5272 11.7478 5.63319 11.6696 5.71133L9.16963 8.21133L8.58037 7.62207L10.3691 5.83333H3.45833V14.1667H6.79167V15H3.04167C2.81155 15 2.625 14.8135 2.625 14.5833V5.41667C2.625 5.18655 2.81155 5 3.04167 5H10.369ZM16.7917 5.83333H13.4583V5H17.2083C17.4385 5 17.625 5.18655 17.625 5.41667V14.5833C17.625 14.8135 17.4385 15 17.2083 15H9.88089L11.6696 16.7887L11.0804 17.378L8.58037 14.878C8.50223 14.7999 8.45833 14.6939 8.45833 14.5834C8.45833 14.4729 8.50223 14.3669 8.58037 14.2887L11.0804 11.7887L11.6696 12.378L9.88095 14.1667H16.7917V5.83333Z" fill="#503CE4"/>
</g>
</svg>`

export const columnUpdated = `<svg width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="check-circle-duotone">
<path id="Icon" d="M6.62516 1.0835C3.60731 1.0835 1.2085 3.48231 1.2085 6.50016C1.2085 9.51802 3.60731 11.9168 6.62516 11.9168C9.64302 11.9168 12.0418 9.51802 12.0418 6.50016C12.0418 3.48231 9.64302 1.0835 6.62516 1.0835Z" fill="#503CE4"/>
<path id="Icon_2" fill-rule="evenodd" clip-rule="evenodd" d="M9.79567 4.79567L6.00401 8.58734C5.89824 8.69311 5.72676 8.69311 5.62099 8.58734L3.45433 6.42067L3.83734 6.03766L5.8125 8.01282L9.41266 4.41266L9.79567 4.79567Z" fill="white"/>
</g>
</svg>`

export const docSceneIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none">
<path d="M13.8333 3.3335H11.8333" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.16669 2V4.66667" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.16665 3.3335H1.83331" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M4.49998 8H1.83331" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.16669 6.6665V9.33317" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14.5 8H7.16669" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M13.8333 12.6665H11.8333" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.16669 11.3335V14.0002" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.16665 12.6665H1.83331" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`

export const checkDatasetIcon = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"> <path d="M12 2C6.42857 2 2 6.42857 2 12C2 17.5714 6.42857 22 12 22C17.5714 22 22 17.5714 22 12C22 6.42857 17.5714 2 12 2Z" fill="#52C41A"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M17.8536 8.85355L10.8536 15.8536C10.6583 16.0488 10.3417 16.0488 10.1464 15.8536L6.14645 11.8536L6.85355 11.1464L10.5 14.7929L17.1464 8.14645L17.8536 8.85355Z" fill="white"/> </svg>`

export const agentAskBIIcon = `<svg viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"> <mask id="mask0_126_36125" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="22" height="22"> <path d="M0 11C0 4.92487 4.92487 0 11 0C17.0751 0 22 4.92487 22 11C22 17.0751 17.0751 22 11 22C4.92487 22 0 17.0751 0 11Z" fill="url(#paint0_linear_126_36125)"/> </mask> <g mask="url(#mask0_126_36125)"> <path d="M0 4.49362C0 2.01186 2.01186 0 4.49362 0H17.5064C19.9881 0 22 2.01186 22 4.49362V17.5064C22 19.9881 19.9881 22 17.5064 22H4.49362C2.01186 22 0 19.9881 0 17.5064V4.49362Z" fill="url(#paint1_linear_126_36125)"/> <path d="M0.574707 10.9997C0.574707 6.98934 3.82576 3.73828 7.83615 3.73828H14.0192C18.0295 3.73828 21.2806 6.98934 21.2806 10.9997C21.2806 15.0101 18.0295 18.2612 14.0192 18.2612H7.83614C3.82576 18.2612 0.574707 15.0101 0.574707 10.9997Z" fill="url(#paint2_linear_126_36125)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M0 10.9996C0 6.61202 3.52242 3.05518 7.86755 3.05518H14.1325C18.4776 3.05518 22 6.61202 22 10.9996C22 15.3872 18.4776 18.9441 14.1325 18.9441H7.86755C3.52242 18.9441 0 15.3872 0 10.9996ZM7.86755 4.08501C4.08568 4.08501 1.01987 7.18079 1.01987 10.9996C1.01987 14.8185 4.08568 17.9142 7.86755 17.9142H14.1325C17.9143 17.9142 20.9801 14.8185 20.9801 10.9996C20.9801 7.18079 17.9143 4.08501 14.1325 4.08501H7.86755Z" fill="url(#paint3_linear_126_36125)" fill-opacity="0.84"/> </g> <path fill-rule="evenodd" clip-rule="evenodd" d="M7 10C6.44772 10 6 10.4477 6 11V14C6 14.5523 6.44772 15 7 15C7.55228 15 8 14.5523 8 14V11C8 10.4477 7.55228 10 7 10Z" fill="url(#paint4_linear_126_36125)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M11 6C10.4477 6 10 6.44772 10 7V14C10 14.5523 10.4477 15 11 15C11.5523 15 12 14.5523 12 14V7C12 6.44772 11.5523 6 11 6Z" fill="url(#paint5_linear_126_36125)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M15 9C14.4477 9 14 9.44772 14 10V14C14 14.5523 14.4477 15 15 15C15.5523 15 16 14.5523 16 14V10C16 9.44772 15.5523 9 15 9Z" fill="url(#paint6_linear_126_36125)"/> <defs> <linearGradient id="paint0_linear_126_36125" x1="11" y1="0" x2="11" y2="22" gradientUnits="userSpaceOnUse"> <stop stop-color="#7750FE"/> <stop offset="1" stop-color="#B19BFF"/> </linearGradient> <linearGradient id="paint1_linear_126_36125" x1="11" y1="5.89795" x2="11" y2="22" gradientUnits="userSpaceOnUse"> <stop stop-color="#705EF4"/> <stop offset="1" stop-color="#7765F4"/> </linearGradient> <linearGradient id="paint2_linear_126_36125" x1="10.9276" y1="3.73828" x2="10.9276" y2="18.2612" gradientUnits="userSpaceOnUse"> <stop stop-color="#604BDE"/> <stop offset="1" stop-color="#9058EC"/> </linearGradient> <linearGradient id="paint3_linear_126_36125" x1="11" y1="3.57009" x2="11" y2="18.4291" gradientUnits="userSpaceOnUse"> <stop stop-color="#9E88F7"/> <stop offset="1" stop-color="#CAC2FF"/> </linearGradient> <linearGradient id="paint4_linear_126_36125" x1="7" y1="10" x2="7" y2="15" gradientUnits="userSpaceOnUse"> <stop stop-color="#F6E7FF"/> <stop offset="1" stop-color="#FEFDFF"/> </linearGradient> <linearGradient id="paint5_linear_126_36125" x1="11" y1="6" x2="11" y2="15" gradientUnits="userSpaceOnUse"> <stop stop-color="#F6E7FF"/> <stop offset="1" stop-color="#FEFDFF"/> </linearGradient> <linearGradient id="paint6_linear_126_36125" x1="15" y1="9" x2="15" y2="15" gradientUnits="userSpaceOnUse"> <stop stop-color="#F6E7FF"/> <stop offset="1" stop-color="#FEFDFF"/> </linearGradient> </defs> </svg>`

export const agentAskDocIcon = `<svg  viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg"> <mask id="mask0_126_36152" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="23" height="22"> <path d="M0.666626 11C0.666626 4.92487 5.59149 0 11.6666 0C17.7418 0 22.6666 4.92487 22.6666 11C22.6666 17.0751 17.7418 22 11.6666 22C5.59149 22 0.666626 17.0751 0.666626 11Z" fill="url(#paint0_linear_126_36152)"/> </mask> <g mask="url(#mask0_126_36152)"> <path d="M0.666626 4.49362C0.666626 2.01186 2.67849 0 5.16024 0H18.173C20.6548 0 22.6666 2.01186 22.6666 4.49362V17.5064C22.6666 19.9881 20.6548 22 18.173 22H5.16024C2.67849 22 0.666626 19.9881 0.666626 17.5064V4.49362Z" fill="url(#paint1_linear_126_36152)"/> <path d="M1.24219 10.9997C1.24219 6.98934 4.49324 3.73828 8.50363 3.73828H14.6866C18.697 3.73828 21.9481 6.98934 21.9481 10.9997C21.9481 15.0101 18.697 18.2612 14.6866 18.2612H8.50363C4.49324 18.2612 1.24219 15.0101 1.24219 10.9997Z" fill="url(#paint2_linear_126_36152)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M7.09998 9.1666C7.09998 8.66955 7.50292 8.2666 7.99998 8.2666H15.3333C15.8304 8.2666 16.2333 8.66955 16.2333 9.1666C16.2333 9.66366 15.8304 10.0666 15.3333 10.0666H7.99998C7.50292 10.0666 7.09998 9.66366 7.09998 9.1666Z" fill="url(#paint3_linear_126_36152)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M7.09998 13.7501C7.09998 13.253 7.50292 12.8501 7.99998 12.8501H11.6666C12.1637 12.8501 12.5666 13.253 12.5666 13.7501C12.5666 14.2472 12.1637 14.6501 11.6666 14.6501H7.99998C7.50292 14.6501 7.09998 14.2472 7.09998 13.7501Z" fill="url(#paint4_linear_126_36152)"/> <path opacity="0.55" fill-rule="evenodd" clip-rule="evenodd" d="M0.666626 10.9996C0.666626 6.61202 4.18905 3.05518 8.53418 3.05518H14.7991C19.1442 3.05518 22.6666 6.61202 22.6666 10.9996C22.6666 15.3872 19.1442 18.9441 14.7991 18.9441H8.53417C4.18905 18.9441 0.666626 15.3872 0.666626 10.9996ZM8.53418 4.08501C4.75231 4.08501 1.68649 7.18079 1.68649 10.9996C1.68649 14.8185 4.75231 17.9142 8.53417 17.9142H14.7991C18.5809 17.9142 21.6468 14.8185 21.6468 10.9996C21.6468 7.18079 18.5809 4.08501 14.7991 4.08501H8.53418Z" fill="url(#paint5_linear_126_36152)"/> </g> <defs> <linearGradient id="paint0_linear_126_36152" x1="11.6666" y1="0" x2="11.6666" y2="22" gradientUnits="userSpaceOnUse"> <stop stop-color="#7750FE"/> <stop offset="1" stop-color="#B19BFF"/> </linearGradient> <linearGradient id="paint1_linear_126_36152" x1="11.6666" y1="0" x2="11.6666" y2="22" gradientUnits="userSpaceOnUse"> <stop stop-color="#23D5E1"/> <stop offset="1" stop-color="#31DDE8"/> </linearGradient> <linearGradient id="paint2_linear_126_36152" x1="11.5951" y1="3.73828" x2="11.5951" y2="18.2612" gradientUnits="userSpaceOnUse"> <stop stop-color="#0BBAC6"/> <stop offset="1" stop-color="#28DDE8"/> </linearGradient> <linearGradient id="paint3_linear_126_36152" x1="7.99998" y1="9.6666" x2="15.3333" y2="9.6666" gradientUnits="userSpaceOnUse"> <stop stop-color="#DBFEFF"/> <stop offset="1" stop-color="#EEFFFD"/> </linearGradient> <linearGradient id="paint4_linear_126_36152" x1="7.99998" y1="14.2501" x2="11.6666" y2="14.2501" gradientUnits="userSpaceOnUse"> <stop stop-color="#DBFEFF"/> <stop offset="1" stop-color="#EEFFFD"/> </linearGradient> <linearGradient id="paint5_linear_126_36152" x1="11.6666" y1="3.57009" x2="11.6666" y2="18.4291" gradientUnits="userSpaceOnUse"> <stop stop-color="#6DFEFE"/> <stop offset="1" stop-color="#D5FCFF"/> </linearGradient> </defs> </svg> `

export const newChatIcon = `<svg t="1729403129522" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="28471"><path d="M464 512a48 48 0 1 0 96 0 48 48 0 0 0-96 0zM264 512a48 48 0 1 0 96 0 48 48 0 0 0-96 0z" p-id="28472" fill="#515151"></path><path d="M925.184 338.410667a448.128 448.128 0 0 0-96.298667-143.317334 444.352 444.352 0 0 0-143.296-96.298666A445.824 445.824 0 0 0 512 64h-2.005333A446.933333 446.933333 0 0 0 193.514667 196.394667 443.946667 443.946667 0 0 0 98.325333 339.2 448.192 448.192 0 0 0 64 514.090667a449.408 449.408 0 0 0 48 199.893333v152.021333a45.994667 45.994667 0 0 0 45.994667 45.994667h152.106666a449.386667 449.386667 0 0 0 199.893334 48h2.090666c15.402667 0 30.72-0.768 45.866667-2.304a4.245333 4.245333 0 0 0 3.797333-4.224v-67.861333a4.309333 4.309333 0 0 0-4.821333-4.266667c-14.805333 1.770667-29.802667 2.645333-44.949333 2.645333h-1.706667a373.290667 373.290667 0 0 1-173.098667-43.498666l-8.384-4.48h-140.8v-140.8l-4.48-8.405334a373.333333 373.333333 0 0 1-43.52-173.098666c-0.405333-99.712 37.696-193.301333 107.605334-263.808 69.781333-70.506667 163.093333-109.504 262.784-109.909334h1.706666c49.984 0 98.496 9.706667 144.192 28.906667a369.621333 369.621333 0 0 1 118.997334 80 370.154667 370.154667 0 0 1 80 118.997333 368.64 368.64 0 0 1 25.088 90.624c0.298667 2.112 2.112 3.690667 4.266666 3.690667h67.861334c2.56 0 4.522667-2.197333 4.224-4.736a445.781333 445.781333 0 0 0-31.573334-119.061333z" p-id="28473" fill="#515151"></path><path d="M771.370667 512c9.728 0 17.621333 7.893333 17.621333 17.621333v153.386667h153.365333c9.728 0 17.621333 7.893333 17.621334 17.621333v45.824c0 9.749333-7.893333 17.642667-17.621334 17.642667h-153.365333v153.365333c0 9.728-7.893333 17.621333-17.621333 17.621334h-45.845334a17.621333 17.621333 0 0 1-17.621333-17.621334v-153.386666h-153.386667a17.621333 17.621333 0 0 1-17.621333-17.621334v-45.824c0-9.749333 7.893333-17.642667 17.621333-17.642666h153.386667v-153.386667c0-9.728 7.893333-17.621333 17.621333-17.621333h45.845334z" p-id="28474" fill="currentColor"></path></svg>`

export const imageSquareIcon = `<svg t="1729402109852" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18762"><path d="M177.738667 652.949333A364.074667 364.074667 0 0 0 331.306667 826.517333v-310.826666L192.490667 645.152c-4.298667 4.010667-9.397333 6.613333-14.752 7.808z m-19.989334-62.922666l209.706667-195.562667H170.666667c-0.618667 0-1.237333-0.010667-1.845334-0.053333A362.186667 362.186667 0 0 0 149.333333 512c0 26.784 2.901333 52.906667 8.416 78.026667z m302.08-195.562667a32.064 32.064 0 0 1-5.152 6.186667l-62.112 57.909333c1.76 3.957333 2.730667 8.341333 2.730667 12.949333v79.541334c3.584 1.642667 6.912 3.978667 9.802667 7.008l67.573333 70.848h90.570667l68.298666-72.938667v-95.872l-68.181333-66.826667c-2.773333 0.778667-5.685333 1.194667-8.693333 1.194667h-94.837334zM395.232 855.466667A362.218667 362.218667 0 0 0 512 874.666667c32.768 0 64.522667-4.341333 94.72-12.490667l-211.413333-221.653333V853.333333c0 0.714667-0.032 1.418667-0.074667 2.133334z m275.466667-17.269334a364.266667 364.266667 0 0 0 155.690666-145.28H533.706667l131.946666 138.346667c2.026667 2.133333 3.712 4.469333 5.045334 6.933333z m184.693333-209.216A362.208 362.208 0 0 0 874.666667 512c0-36.661333-5.44-72.053333-15.552-105.408L650.912 628.906667H853.333333c0.693333 0 1.376 0.021333 2.058667 0.064zM197.962667 330.464h301.312l-139.733334-136.938667a31.978667 31.978667 0 0 1-5.898666-7.893333A364.266667 364.266667 0 0 0 197.973333 330.474667z m220.373333-168.917333l213.205333 208.938666v-195.2c0-1.92 0.170667-3.786667 0.490667-5.621333A362.112 362.112 0 0 0 512 149.333333c-32.384 0-63.786667 4.245333-93.653333 12.213334zM695.552 446.613333V487.637333l134.432-143.573333c0.746667-0.8 1.546667-1.557333 2.346667-2.261333a364.373333 364.373333 0 0 0-136.778667-142.666667v247.488zM512 938.666667C276.362667 938.666667 85.333333 747.637333 85.333333 512S276.362667 85.333333 512 85.333333s426.666667 191.029333 426.666667 426.666667-191.029333 426.666667-426.666667 426.666667z" fill="currentColor" p-id="18763"></path></svg>`

export const chatHistoryIcon = `<svg t="1705050684723" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4243" fill="currentColor"><path d="M533.333333 85.333333C384 85.333333 251.733333 166.4 183.466667 290.133333L85.333333 192V469.333333h277.333334L243.2 349.866667C298.666667 243.2 405.333333 170.666667 533.333333 170.666667c174.933333 0 320 145.066667 320 320S708.266667 810.666667 533.333333 810.666667c-140.8 0-256-89.6-302.933333-213.333334H140.8c46.933333 170.666667 204.8 298.666667 392.533333 298.666667 226.133333 0 405.333333-183.466667 405.333334-405.333333S755.2 85.333333 533.333333 85.333333zM469.333333 298.666667v217.6l200.533334 119.466666 34.133333-55.466666-170.666667-102.4V298.666667H469.333333z" opacity=".9" p-id="4244"></path></svg>`

export const makingSquareIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21ZM12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.28032 8.55084C7.45187 8.46694 7.65623 8.48808 7.80697 8.60532L12 11.8666L16.193 8.60532C16.3438 8.48808 16.5481 8.46694 16.7197 8.55084C16.8912 8.63474 17 8.80903 17 9V16H16V10.0223L12.307 12.8947C12.1264 13.0351 11.8736 13.0351 11.693 12.8947L8 10.0223V16H7V9C7 8.80903 7.10878 8.63474 7.28032 8.55084Z" fill="currentColor"/>
</svg>`

export const userInfoIcon = `<svg t="1729403737108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="33399"><path d="M512 170.652444c-78.421333 0-142.222222 63.800889-142.222222 142.222223s63.800889 142.222222 142.222222 142.222222 142.222222-63.800889 142.222222-142.222222-63.800889-142.222222-142.222222-142.222223m0 369.777778c-125.468444 0-227.555556-102.087111-227.555556-227.555555s102.087111-227.555556 227.555556-227.555556 227.555556 102.087111 227.555556 227.555556-102.087111 227.555556-227.555556 227.555555M199.111111 711.096889c-15.687111 0-28.444444 12.757333-28.444444 28.444444v85.333334c0 15.687111 12.757333 28.444444 28.444444 28.444444h625.777778c15.687111 0 28.444444-12.757333 28.444444-28.444444v-85.333334c0-15.687111-12.757333-28.444444-28.444444-28.444444H199.111111z m625.777778 227.555555H199.111111c-62.734222 0-113.777778-51.057778-113.777778-113.777777v-85.333334c0-62.734222 51.043556-113.777778 113.777778-113.777777h625.777778c62.734222 0 113.777778 51.043556 113.777778 113.777777v85.333334c0 62.72-51.043556 113.777778-113.777778 113.777777z" fill="currentColor" p-id="33400"></path></svg>`

export const userFeedbackIcon = `<svg t="1729403856086" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="34583"><path d="M768 512a42.666667 42.666667 0 1 1 85.333333 0v277.333333a42.666667 42.666667 0 0 1-42.666666 42.666667H256a42.666667 42.666667 0 0 1-42.666667-42.666667V234.666667a42.666667 42.666667 0 0 1 42.666667-42.666667h277.333333v0.106667a42.666667 42.666667 0 0 1 0 85.12V277.333333H298.666667v469.333334h469.333333V512z" fill="#515151" p-id="34584"></path><path d="M798.4 206.101333l30.165333 30.165334a42.666667 42.666667 0 0 1 0 60.352l-316.8 316.778666-90.496-90.517333 316.8-316.778667a42.666667 42.666667 0 0 1 60.330667 0zM360.789333 673.877333l30.229334-120.746666 90.517333 90.517333-120.746667 30.229333z" fill="currentColor" p-id="34585"></path></svg>`

export const logoutIcon = `<svg t="1729403915911" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="35731"><path d="M952.532295 495.269967 767.686357 310.423005c-9.060353-9.060353-23.75196-9.060353-32.81743 0l-37.363979 37.362956c-9.060353 9.060353-9.060353 23.757077 0 32.81743l81.538061 81.538061L407.384337 462.141452c-12.812817 0-23.199375 10.386558-23.199375 23.199375l0 52.845579c0 12.815887 10.386558 23.204491 23.199375 23.204491l371.479593 0-81.538061 81.538061c-9.060353 9.060353-9.060353 23.757077 0 32.81743l37.368072 37.363979c9.060353 9.05933 23.75503 9.05933 32.815383 0l147.653875-147.653875c0-0.005117 0.005117-0.005117 0.005117-0.005117l37.368072-37.368072C961.592648 519.020904 961.592648 504.33032 952.532295 495.269967L952.532295 495.269967zM634.083499 64.754816l-499.803213 0c-38.441521 0-69.608358 31.166837-69.608358 69.608358l0 754.806002c0 38.446637 31.166837 69.608358 69.608358 69.608358l499.803213 0c38.441521 0 69.608358-31.16172 69.608358-69.608358l0-97.937566c0-12.811794-10.386558-23.204491-23.204491-23.204491l-50.29243 0c-12.812817 0-23.205515 10.392698-23.205515 23.204491l0 37.257555c0 34.328853 0 34.328853-34.791387 34.328853L195.199751 862.818017c-34.801621 0-34.801621 0.00614-34.801621-34.806737L160.39813 194.712657c0-34.900881-0.074701-34.802644 34.801621-34.802644l376.99726 0c34.798551 0 34.791387 0.285502 34.791387 34.329876l0 38.353516c0 12.815887 10.392698 23.204491 23.205515 23.204491l50.29243 0c12.817933 0 23.204491-10.388605 23.204491-23.204491L703.690834 134.363174C703.691857 95.921653 672.52502 64.754816 634.083499 64.754816L634.083499 64.754816zM634.083499 64.754816" fill="currentColor" p-id="35732"></path></svg>`

export const AssistantIcon = `<svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_137_441)">
      <mask id="path-1-inside-1_137_441" fill="white">
        <path d="M18.1709 6.82923C18.5173 7.17718 18.7778 7.60105 18.9319 8.06723L19.4299 9.59723C19.4718 9.71491 19.549 9.81675 19.6511 9.88877C19.7531 9.96078 19.875 9.99944 19.9999 9.99944C20.1248 9.99944 20.2466 9.96078 20.3487 9.88877C20.4507 9.81675 20.528 9.71491 20.5699 9.59723L21.0679 8.06723C21.2226 7.60199 21.4838 7.17929 21.8307 6.83277C22.1775 6.48625 22.6005 6.22549 23.0659 6.07123L24.5959 5.57423C24.7136 5.53237 24.8154 5.4551 24.8874 5.35304C24.9594 5.25099 24.9981 5.12914 24.9981 5.00423C24.9981 4.87933 24.9594 4.75748 24.8874 4.65542C24.8154 4.55337 24.7136 4.4761 24.5959 4.43423L24.5659 4.42623L23.0349 3.92923C22.5695 3.77498 22.1465 3.51421 21.7997 3.1677C21.4528 2.82118 21.1916 2.39848 21.0369 1.93323L20.5399 0.403234C20.4985 0.285097 20.4214 0.182745 20.3193 0.110332C20.2172 0.0379194 20.0951 -0.000976562 19.9699 -0.000976562C19.8447 -0.000976562 19.7226 0.0379194 19.6205 0.110332C19.5184 0.182745 19.4413 0.285097 19.3999 0.403234L18.9019 1.93323L18.8889 1.97123C18.7335 2.42487 18.4767 2.83706 18.1379 3.17638C17.7991 3.5157 17.3873 3.77319 16.9339 3.92923L15.4039 4.42623C15.2862 4.4681 15.1844 4.54537 15.1124 4.64742C15.0403 4.74948 15.0017 4.87133 15.0017 4.99623C15.0017 5.12114 15.0403 5.24299 15.1124 5.34504C15.1844 5.4471 15.2862 5.52437 15.4039 5.56623L16.9339 6.06323C17.4009 6.21923 17.8239 6.48123 18.1709 6.82923ZM26.8209 10.3582L27.7389 10.6562L27.7579 10.6602C27.8289 10.6849 27.8906 10.7311 27.9342 10.7924C27.9778 10.8537 28.0012 10.927 28.0012 11.0022C28.0012 11.0775 27.9778 11.1508 27.9342 11.2121C27.8906 11.2734 27.8289 11.3196 27.7579 11.3442L26.8389 11.6432C26.5602 11.7365 26.307 11.8932 26.0991 12.1009C25.8913 12.3086 25.7344 12.5617 25.6409 12.8402L25.3419 13.7582C25.3168 13.8288 25.2704 13.8899 25.2092 13.9332C25.1479 13.9764 25.0748 13.9996 24.9999 13.9996C24.9249 13.9996 24.8518 13.9764 24.7906 13.9332C24.7294 13.8899 24.683 13.8288 24.6579 13.7582L24.3589 12.8402C24.2667 12.5604 24.1104 12.3059 23.9024 12.0972C23.6944 11.8885 23.4404 11.7313 23.1609 11.6382L22.2419 11.3402C22.1708 11.3156 22.1092 11.2694 22.0656 11.2081C22.022 11.1468 21.9985 11.0735 21.9985 10.9982C21.9985 10.923 22.022 10.8497 22.0656 10.7884C22.1092 10.7271 22.1708 10.6809 22.2419 10.6562L23.1609 10.3572C23.4362 10.2616 23.6858 10.1039 23.8904 9.89631C24.0951 9.68875 24.2492 9.4369 24.3409 9.16023L24.6399 8.24223C24.665 8.17163 24.7114 8.11052 24.7726 8.06731C24.8338 8.02411 24.9069 8.00091 24.9819 8.00091C25.0568 8.00091 25.1299 8.02411 25.1912 8.06731C25.2524 8.11052 25.2988 8.17163 25.3239 8.24223L25.6219 9.16023C25.7148 9.43929 25.8716 9.69281 26.0797 9.90061C26.2879 10.1084 26.5417 10.2658 26.8209 10.3582ZM14.3659 4.00023H4.75488C4.02554 4.00023 3.32606 4.28996 2.81034 4.80569C2.29461 5.32141 2.00488 6.02089 2.00488 6.75023V21.2502C2.00488 21.9794 2.29448 22.6787 2.80999 23.1944C3.32549 23.7101 4.02471 24 4.75388 24.0002H23.2499C23.9792 24.0002 24.6787 23.7105 25.1944 23.1948C25.7102 22.6791 25.9999 21.9796 25.9999 21.2502V14.5652C25.9366 14.6332 25.8666 14.6946 25.7899 14.7492C25.6047 14.8801 25.3898 14.963 25.1647 14.9904C24.9395 15.0178 24.7111 14.9889 24.4999 14.9062V21.2502C24.4999 21.9402 23.9399 22.5002 23.2499 22.5002H19.9999V18.5002C19.9999 17.9698 19.7892 17.4611 19.4141 17.086C19.039 16.7109 18.5303 16.5002 17.9999 16.5002H9.99988C9.46945 16.5002 8.96074 16.7109 8.58567 17.086C8.2106 17.4611 7.99988 17.9698 7.99988 18.5002V22.5002H4.75388C4.06388 22.5002 3.50388 21.9402 3.50388 21.2502V6.75023C3.50388 6.06023 4.06388 5.50023 4.75388 5.50023H14.0759C13.9935 5.24608 13.9768 4.97518 14.0275 4.71285C14.0782 4.45052 14.1947 4.20534 14.3659 4.00023ZM18.4999 18.5002V22.5002H9.49988V18.5002C9.49988 18.3676 9.55256 18.2404 9.64633 18.1467C9.7401 18.0529 9.86727 18.0002 9.99988 18.0002H17.9999C18.1325 18.0002 18.2597 18.0529 18.3534 18.1467C18.4472 18.2404 18.4999 18.3676 18.4999 18.5002ZM11.9999 11.5002C11.9999 10.9698 12.2106 10.4611 12.5857 10.086C12.9607 9.71095 13.4694 9.50023 13.9999 9.50023C14.5303 9.50023 15.039 9.71095 15.4141 10.086C15.7892 10.4611 15.9999 10.9698 15.9999 11.5002C15.9999 12.0307 15.7892 12.5394 15.4141 12.9144C15.039 13.2895 14.5303 13.5002 13.9999 13.5002C13.4694 13.5002 12.9607 13.2895 12.5857 12.9144C12.2106 12.5394 11.9999 12.0307 11.9999 11.5002ZM13.9999 8.00023C13.0716 8.00023 12.1814 8.36898 11.525 9.02536C10.8686 9.68174 10.4999 10.572 10.4999 11.5002C10.4999 12.4285 10.8686 13.3187 11.525 13.9751C12.1814 14.6315 13.0716 15.0002 13.9999 15.0002C14.9281 15.0002 15.8184 14.6315 16.4748 13.9751C17.1311 13.3187 17.4999 12.4285 17.4999 11.5002C17.4999 10.572 17.1311 9.68174 16.4748 9.02536C15.8184 8.36898 14.9281 8.00023 13.9999 8.00023Z" />
      </mask>
      <path
        d="M18.1709 6.82923C18.5173 7.17718 18.7778 7.60105 18.9319 8.06723L19.4299 9.59723C19.4718 9.71491 19.549 9.81675 19.6511 9.88877C19.7531 9.96078 19.875 9.99944 19.9999 9.99944C20.1248 9.99944 20.2466 9.96078 20.3487 9.88877C20.4507 9.81675 20.528 9.71491 20.5699 9.59723L21.0679 8.06723C21.2226 7.60199 21.4838 7.17929 21.8307 6.83277C22.1775 6.48625 22.6005 6.22549 23.0659 6.07123L24.5959 5.57423C24.7136 5.53237 24.8154 5.4551 24.8874 5.35304C24.9594 5.25099 24.9981 5.12914 24.9981 5.00423C24.9981 4.87933 24.9594 4.75748 24.8874 4.65542C24.8154 4.55337 24.7136 4.4761 24.5959 4.43423L24.5659 4.42623L23.0349 3.92923C22.5695 3.77498 22.1465 3.51421 21.7997 3.1677C21.4528 2.82118 21.1916 2.39848 21.0369 1.93323L20.5399 0.403234C20.4985 0.285097 20.4214 0.182745 20.3193 0.110332C20.2172 0.0379194 20.0951 -0.000976562 19.9699 -0.000976562C19.8447 -0.000976562 19.7226 0.0379194 19.6205 0.110332C19.5184 0.182745 19.4413 0.285097 19.3999 0.403234L18.9019 1.93323L18.8889 1.97123C18.7335 2.42487 18.4767 2.83706 18.1379 3.17638C17.7991 3.5157 17.3873 3.77319 16.9339 3.92923L15.4039 4.42623C15.2862 4.4681 15.1844 4.54537 15.1124 4.64742C15.0403 4.74948 15.0017 4.87133 15.0017 4.99623C15.0017 5.12114 15.0403 5.24299 15.1124 5.34504C15.1844 5.4471 15.2862 5.52437 15.4039 5.56623L16.9339 6.06323C17.4009 6.21923 17.8239 6.48123 18.1709 6.82923ZM26.8209 10.3582L27.7389 10.6562L27.7579 10.6602C27.8289 10.6849 27.8906 10.7311 27.9342 10.7924C27.9778 10.8537 28.0012 10.927 28.0012 11.0022C28.0012 11.0775 27.9778 11.1508 27.9342 11.2121C27.8906 11.2734 27.8289 11.3196 27.7579 11.3442L26.8389 11.6432C26.5602 11.7365 26.307 11.8932 26.0991 12.1009C25.8913 12.3086 25.7344 12.5617 25.6409 12.8402L25.3419 13.7582C25.3168 13.8288 25.2704 13.8899 25.2092 13.9332C25.1479 13.9764 25.0748 13.9996 24.9999 13.9996C24.9249 13.9996 24.8518 13.9764 24.7906 13.9332C24.7294 13.8899 24.683 13.8288 24.6579 13.7582L24.3589 12.8402C24.2667 12.5604 24.1104 12.3059 23.9024 12.0972C23.6944 11.8885 23.4404 11.7313 23.1609 11.6382L22.2419 11.3402C22.1708 11.3156 22.1092 11.2694 22.0656 11.2081C22.022 11.1468 21.9985 11.0735 21.9985 10.9982C21.9985 10.923 22.022 10.8497 22.0656 10.7884C22.1092 10.7271 22.1708 10.6809 22.2419 10.6562L23.1609 10.3572C23.4362 10.2616 23.6858 10.1039 23.8904 9.89631C24.0951 9.68875 24.2492 9.4369 24.3409 9.16023L24.6399 8.24223C24.665 8.17163 24.7114 8.11052 24.7726 8.06731C24.8338 8.02411 24.9069 8.00091 24.9819 8.00091C25.0568 8.00091 25.1299 8.02411 25.1912 8.06731C25.2524 8.11052 25.2988 8.17163 25.3239 8.24223L25.6219 9.16023C25.7148 9.43929 25.8716 9.69281 26.0797 9.90061C26.2879 10.1084 26.5417 10.2658 26.8209 10.3582ZM14.3659 4.00023H4.75488C4.02554 4.00023 3.32606 4.28996 2.81034 4.80569C2.29461 5.32141 2.00488 6.02089 2.00488 6.75023V21.2502C2.00488 21.9794 2.29448 22.6787 2.80999 23.1944C3.32549 23.7101 4.02471 24 4.75388 24.0002H23.2499C23.9792 24.0002 24.6787 23.7105 25.1944 23.1948C25.7102 22.6791 25.9999 21.9796 25.9999 21.2502V14.5652C25.9366 14.6332 25.8666 14.6946 25.7899 14.7492C25.6047 14.8801 25.3898 14.963 25.1647 14.9904C24.9395 15.0178 24.7111 14.9889 24.4999 14.9062V21.2502C24.4999 21.9402 23.9399 22.5002 23.2499 22.5002H19.9999V18.5002C19.9999 17.9698 19.7892 17.4611 19.4141 17.086C19.039 16.7109 18.5303 16.5002 17.9999 16.5002H9.99988C9.46945 16.5002 8.96074 16.7109 8.58567 17.086C8.2106 17.4611 7.99988 17.9698 7.99988 18.5002V22.5002H4.75388C4.06388 22.5002 3.50388 21.9402 3.50388 21.2502V6.75023C3.50388 6.06023 4.06388 5.50023 4.75388 5.50023H14.0759C13.9935 5.24608 13.9768 4.97518 14.0275 4.71285C14.0782 4.45052 14.1947 4.20534 14.3659 4.00023ZM18.4999 18.5002V22.5002H9.49988V18.5002C9.49988 18.3676 9.55256 18.2404 9.64633 18.1467C9.7401 18.0529 9.86727 18.0002 9.99988 18.0002H17.9999C18.1325 18.0002 18.2597 18.0529 18.3534 18.1467C18.4472 18.2404 18.4999 18.3676 18.4999 18.5002ZM11.9999 11.5002C11.9999 10.9698 12.2106 10.4611 12.5857 10.086C12.9607 9.71095 13.4694 9.50023 13.9999 9.50023C14.5303 9.50023 15.039 9.71095 15.4141 10.086C15.7892 10.4611 15.9999 10.9698 15.9999 11.5002C15.9999 12.0307 15.7892 12.5394 15.4141 12.9144C15.039 13.2895 14.5303 13.5002 13.9999 13.5002C13.4694 13.5002 12.9607 13.2895 12.5857 12.9144C12.2106 12.5394 11.9999 12.0307 11.9999 11.5002ZM13.9999 8.00023C13.0716 8.00023 12.1814 8.36898 11.525 9.02536C10.8686 9.68174 10.4999 10.572 10.4999 11.5002C10.4999 12.4285 10.8686 13.3187 11.525 13.9751C12.1814 14.6315 13.0716 15.0002 13.9999 15.0002C14.9281 15.0002 15.8184 14.6315 16.4748 13.9751C17.1311 13.3187 17.4999 12.4285 17.4999 11.5002C17.4999 10.572 17.1311 9.68174 16.4748 9.02536C15.8184 8.36898 14.9281 8.00023 13.9999 8.00023Z"
        stroke="#303030"
        strokeWidth="2"
        mask="url(#path-1-inside-1_137_441)"
      />
    </g>
    <defs>
      <clipPath id="clip0_137_441">
        <rect width="28" height="28" fill="white" />
      </clipPath>
    </defs>
  </svg>`

export const SidebarCollapsedIcon = ` <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 3.5V20.5M3 9.4C3 7.16 3 6.04 3.436 5.184C3.81949 4.43139 4.43139 3.81949 5.184 3.436C6.04 3 7.16 3 9.4 3H14.6C16.84 3 17.96 3 18.816 3.436C19.5686 3.81949 20.1805 4.43139 20.564 5.184C21 6.04 21 7.16 21 9.4V14.6C21 16.84 21 17.96 20.564 18.816C20.1805 19.5686 19.5686 20.1805 18.816 20.564C17.96 21 16.84 21 14.6 21H9.4C7.16 21 6.04 21 5.184 20.564C4.43139 20.1805 3.81949 19.5686 3.436 18.816C3 17.96 3 16.84 3 14.6V9.4Z"
        stroke={color || "#303030"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>`

export const SparkleIcon = `<svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_50_1345)">
        <path
          d="M2.49901 11C2.60517 10.9999 2.70861 11.0335 2.79437 11.0961C2.88013 11.1587 2.94374 11.2469 2.97601 11.348L3.23201 12.145C3.28147 12.2918 3.36401 12.4252 3.47325 12.535C3.58249 12.6447 3.7155 12.7279 3.86201 12.778L4.65101 13.026C4.75257 13.0578 4.84133 13.1212 4.90438 13.2069C4.96744 13.2926 5.0015 13.3963 5.00161 13.5027C5.00172 13.6091 4.96788 13.7128 4.905 13.7986C4.84213 13.8845 4.7535 13.948 4.65201 13.98L3.86201 14.232C3.71513 14.2815 3.58176 14.3644 3.47243 14.4743C3.36309 14.5842 3.28079 14.7179 3.23201 14.865L2.98001 15.652C2.94799 15.7524 2.8851 15.84 2.80033 15.9025C2.71555 15.965 2.61322 15.9991 2.50791 16C2.40261 16.0008 2.29971 15.9685 2.2139 15.9074C2.12808 15.8464 2.06373 15.7598 2.03001 15.66L1.76401 14.87C1.71339 14.7223 1.62983 14.588 1.51964 14.4773C1.40946 14.3665 1.27554 14.2824 1.12801 14.231L0.347015 13.979C0.246911 13.9464 0.159676 13.8829 0.0977477 13.7978C0.0358193 13.7126 0.00235902 13.6101 0.00213734 13.5048C0.00191566 13.3995 0.0349439 13.2968 0.0965131 13.2114C0.158082 13.1259 0.245049 13.0621 0.345015 13.029L1.13901 12.769C1.28637 12.7189 1.4203 12.6357 1.53054 12.5258C1.64078 12.4159 1.72441 12.2822 1.77501 12.135L2.02301 11.349C2.05511 11.2477 2.11866 11.1593 2.20443 11.0965C2.2902 11.0338 2.39274 11 2.49901 11ZM1.00001 7.51304C0.999537 7.30165 1.06606 7.09554 1.19003 6.92432C1.314 6.7531 1.48904 6.62557 1.69001 6.56004L4.27301 5.71604C4.84424 5.52194 5.36342 5.19957 5.79071 4.77366C6.218 4.34775 6.54206 3.82963 6.73801 3.25904L7.54601 0.699037C7.6099 0.496613 7.73654 0.319768 7.9076 0.194096C8.07867 0.0684252 8.28529 0.000447671 8.49755 2.20345e-06C8.70982 -0.000443264 8.91672 0.0666665 9.08831 0.191619C9.2599 0.316571 9.38728 0.492883 9.45201 0.695037L10.284 3.29304C10.4754 3.86293 10.7959 4.38086 11.2206 4.80638C11.6452 5.2319 12.1625 5.55349 12.732 5.74604L15.301 6.55704C15.5034 6.62092 15.6803 6.74756 15.806 6.91863C15.9316 7.08969 15.9996 7.29631 16 7.50857C16.0005 7.72084 15.9334 7.92774 15.8084 8.09933C15.6835 8.27092 15.5072 8.3983 15.305 8.46304L12.733 9.28604C12.1632 9.47855 11.6457 9.80023 11.2208 10.2259C10.796 10.6517 10.4754 11.1699 10.284 11.74L9.46402 14.305C9.39974 14.5056 9.27379 14.6808 9.10411 14.8055C8.93443 14.9303 8.72969 14.9983 8.51908 14.9998C8.30848 15.0014 8.10276 14.9364 7.93126 14.8142C7.75976 14.6919 7.63123 14.5186 7.56401 14.319L6.69801 11.752V11.75C6.50261 11.1792 6.17969 10.6605 5.75378 10.2332C5.32787 9.8059 4.81017 9.48129 4.24001 9.28404L1.69301 8.46304C1.49197 8.39818 1.31662 8.27133 1.19212 8.10066C1.06763 7.93 1.00038 7.72429 1.00001 7.51304Z"
          fill="#303030"
        />
      </g>
      <defs>
        <clipPath id="clip0_50_1345">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>`

export const SubmissionsIcon = ` <svg
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.769 1.9585H15.231C16.6379 1.9585 17.7692 1.95942 18.6802 2.04736L19.0571 2.09033H19.0581C19.9899 2.21457 20.7176 2.46608 21.2944 2.98584L21.4077 3.09326C21.7044 3.39118 21.9186 3.72767 22.0767 4.10889L22.189 4.37842L22.4771 4.41357C23.4466 4.53425 24.1983 4.78339 24.7905 5.31592L24.9077 5.42627C25.4271 5.94578 25.7019 6.59376 25.853 7.41455L25.9106 7.77686C26.0404 8.7476 26.0415 9.99577 26.0415 11.603V16.3979C26.0415 17.8046 26.0408 18.9359 25.9536 19.8472L25.9106 20.2241C25.7841 21.1571 25.5348 21.8841 25.0151 22.4604L24.9077 22.5737C24.2983 23.1831 23.5127 23.4575 22.4771 23.5874L22.189 23.6235L22.0776 23.8921C21.9391 24.2254 21.7567 24.5249 21.5142 24.7944L21.4067 24.9077C20.8873 25.4269 20.2399 25.7019 19.4194 25.853L19.0571 25.9106C18.0863 26.0405 16.8384 26.0415 15.231 26.0415H12.769C11.3624 26.0415 10.2311 26.0408 9.31982 25.9536L8.94287 25.9106C7.94825 25.7758 7.18764 25.5014 6.59424 24.9087H6.59521C6.30735 24.618 6.07907 24.2738 5.92432 23.895L5.81396 23.6235L5.52393 23.5874L5.14697 23.5317C4.35576 23.3944 3.7228 23.1458 3.20947 22.6841L3.09326 22.5737C2.5738 22.0543 2.29834 21.4069 2.14795 20.5864L2.09033 20.2241C1.95937 19.2536 1.9585 18.0057 1.9585 16.3979V11.603C1.9585 10.1963 1.95945 9.06477 2.04736 8.15381L2.09033 7.77588C2.21456 6.84416 2.46619 6.1164 2.98584 5.53955L3.09326 5.42627C3.70264 4.81698 4.48768 4.54229 5.52295 4.41357L5.81396 4.37744L5.92432 4.10693C6.07862 3.7285 6.30606 3.384 6.59326 3.09326C7.11272 2.5738 7.76008 2.29834 8.58057 2.14795L8.94287 2.09033C9.91336 1.95937 11.1613 1.9585 12.769 1.9585ZM12.8335 2.7085C11.3811 2.7085 10.2743 2.70965 9.40283 2.79248L9.04346 2.8335H9.04248C8.24407 2.94121 7.70451 3.13653 7.29346 3.47119L7.12451 3.62354H7.12354C6.74592 4.00115 6.51775 4.48938 6.38428 5.21729L6.3335 5.54346V5.54443C6.21019 6.46391 6.2085 7.67252 6.2085 9.3335V18.6665C6.2085 20.119 6.20965 21.2252 6.29248 22.0972L6.3335 22.4575C6.44123 23.2563 6.63631 23.7964 6.97119 24.2075L7.12354 24.3755V24.3765C7.50114 24.7541 7.98942 24.9822 8.71729 25.1157L9.04346 25.1675H9.04443C9.96502 25.2908 11.1736 25.2915 12.8335 25.2915H15.1665C16.6193 25.2915 17.7266 25.2904 18.5981 25.2075L18.9575 25.1675C19.7561 25.0598 20.2964 24.8645 20.7075 24.5298L20.8755 24.3774L20.8765 24.3765C21.254 23.999 21.4822 23.5112 21.6157 22.7837L21.6675 22.4565V22.4556C21.7907 21.5339 21.7915 20.3263 21.7915 18.6665V9.3335C21.7915 7.88108 21.7904 6.7743 21.7075 5.90283L21.6675 5.54346V5.54248C21.5598 4.74423 21.3643 4.20449 21.0298 3.79346L20.8774 3.62451L20.8765 3.62354L20.7075 3.47119C20.3546 3.18324 19.9073 2.9987 19.2837 2.88428L18.9565 2.8335H18.9556C18.035 2.71023 16.8262 2.7085 15.1665 2.7085H12.8335ZM4.93115 5.27783C4.35464 5.41774 3.9472 5.63392 3.62451 5.95752C3.24641 6.33426 3.01776 6.82252 2.88428 7.54932L2.8335 7.87646C2.71022 8.79922 2.7085 10.0066 2.7085 11.6665V16.3335C2.7085 17.7859 2.70965 18.8927 2.79248 19.7642L2.8335 20.1235V20.1245C2.94121 20.9229 3.13653 21.4625 3.47119 21.8735L3.62354 22.0425V22.0435C3.94633 22.3662 4.35417 22.583 4.93311 22.7231L5.6167 22.8882L5.54834 22.188C5.45909 21.2703 5.4585 20.1383 5.4585 18.731V9.26904C5.4585 7.86292 5.45917 6.72914 5.54736 5.81104L5.61475 5.11182L4.93115 5.27783ZM22.4517 5.81201C22.5409 6.72974 22.5415 7.86284 22.5415 9.26904V18.731C22.5415 20.1372 22.5408 21.2708 22.4526 22.189L22.3862 22.8882L23.0688 22.7231C23.6465 22.583 24.0538 22.366 24.3765 22.0435C24.7541 21.6658 24.9822 21.1776 25.1157 20.4497L25.1675 20.1235V20.1226C25.2908 19.202 25.2915 17.9943 25.2915 16.3345V11.6685C25.2915 10.2151 25.2903 9.10877 25.2075 8.23682L25.1675 7.87646C25.0598 7.07801 24.8644 6.53754 24.5298 6.12646L24.3774 5.95752H24.3765C24.0537 5.63473 23.6458 5.41794 23.0669 5.27783L22.3843 5.11182L22.4517 5.81201ZM10.5005 19.4585H14.0005C14.0751 19.4586 14.1473 19.4811 14.2085 19.522L14.2651 19.5679C14.3355 19.6382 14.3755 19.734 14.3755 19.8335C14.3755 19.9081 14.3528 19.9803 14.312 20.0415L14.2651 20.0991C14.1949 20.1692 14.0997 20.2084 14.0005 20.2085H10.5005C10.4259 20.2085 10.3536 20.1858 10.2925 20.145L10.2349 20.0991C10.1645 20.0288 10.1255 19.933 10.1255 19.8335C10.1255 19.7589 10.1473 19.6866 10.188 19.6255L10.2349 19.5679C10.3052 19.4976 10.4011 19.4585 10.5005 19.4585ZM10.5005 14.7915H17.5005C17.5751 14.7916 17.6474 14.8141 17.7085 14.855L17.7651 14.9019C17.8354 14.9721 17.8754 15.0672 17.8755 15.1665C17.8755 15.241 17.8527 15.3134 17.812 15.3745L17.7651 15.4321C17.6949 15.5023 17.5998 15.5414 17.5005 15.5415H10.5005C10.401 15.5415 10.3052 15.5025 10.2349 15.4321C10.1645 15.3618 10.1255 15.266 10.1255 15.1665C10.1256 15.092 10.1472 15.0196 10.188 14.9585L10.2349 14.9019C10.3052 14.8315 10.401 14.7915 10.5005 14.7915ZM10.5005 10.1255H17.5005C17.575 10.1256 17.6474 10.1472 17.7085 10.188L17.7651 10.2349C17.8355 10.3052 17.8755 10.401 17.8755 10.5005C17.8754 10.5751 17.8528 10.6474 17.812 10.7085L17.7651 10.7651C17.6949 10.8354 17.5998 10.8754 17.5005 10.8755H10.5005C10.426 10.8755 10.3536 10.8527 10.2925 10.812L10.2349 10.7651C10.1647 10.6949 10.1256 10.5998 10.1255 10.5005C10.1255 10.426 10.1473 10.3536 10.188 10.2925L10.2349 10.2349C10.3052 10.1645 10.401 10.1255 10.5005 10.1255Z"
        stroke="#303030"
      />
    </svg>`

export const CollapsedIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9 3.5V20.5M3 9.4C3 7.16 3 6.04 3.436 5.184C3.81949 4.43139 4.43139 3.81949 5.184 3.436C6.04 3 7.16 3 9.4 3H14.6C16.84 3 17.96 3 18.816 3.436C19.5686 3.81949 20.1805 4.43139 20.564 5.184C21 6.04 21 7.16 21 9.4V14.6C21 16.84 21 17.96 20.564 18.816C20.1805 19.5686 19.5686 20.1805 18.816 20.564C17.96 21 16.84 21 14.6 21H9.4C7.16 21 6.04 21 5.184 20.564C4.43139 20.1805 3.81949 19.5686 3.436 18.816C3 17.96 3 16.84 3 14.6V9.4Z" stroke="#6F6F6F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg>`

export const UploadIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 7.91V16C6 17.5913 6.63214 19.1174 7.75736 20.2426C8.88258 21.3679 10.4087 22 12 22C13.5913 22 15.1174 21.3679 16.2426 20.2426C17.3679 19.1174 18 17.5913 18 16V6C18 4.93913 17.5786 3.92172 16.8284 3.17157C16.0783 2.42143 15.0609 2 14 2C12.9391 2 11.9217 2.42143 11.1716 3.17157C10.4214 3.92172 10 4.93913 10 6V15.182C10 15.4446 10.0517 15.7047 10.1522 15.9474C10.2528 16.19 10.4001 16.4105 10.5858 16.5962C10.7715 16.7819 10.992 16.9292 11.2346 17.0298C11.4773 17.1303 11.7374 17.182 12 17.182C12.5304 17.182 13.0391 16.9713 13.4142 16.5962C13.7893 16.2211 14 15.7124 14 15.182V8" stroke="#303030" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg>`

/**
 * Ask BI 的全局状态管理
 */
import { atom } from 'jotai'
import { nanoid } from 'nanoid'
import axios, { AxiosError, CancelTokenSource } from 'axios'
import { produce } from 'immer'
import { atomWithStorage } from 'jotai/utils'
import { throttle } from 'lodash'
import { UploadFile } from 'antd'
import {
  Chat,
  ChatRequestProps,
  ChatStatus,
  ChatStatusType,
  CustomUploadFile,
  FlowSessionType,
  FlowType,
  JsonContentItem,
  Llm,
  MessageInputEditorRef,
  STRUCTURED_MESSAGE_DATA_TYPE,
  ThemeType,
  UserInfoType,
} from '@shared/common-types'
import { IS_DARK } from 'src/shared/constants'
import { scrollToBottom } from 'src/client/utils'
import { aiPlatformPageUrls, chatAIApiUrls } from 'src/shared/url-map'
export const isSubmittingAtom = atom<boolean>(false)
export const currentLoginUserAtom = atomWithStorage<UserInfoType | null>('loginUserInfoData', null)
export const loginAppIdAtom = atomWithStorage<string>('loginAppIdAtom', '')

export const isSidebarOpenAtom = atom<boolean>(true)

const helloText =
  '👋  Hello! I am an AI assistant, glad to serve you. What type of business license do you want to apply for?'
export const pageHeightAtom = atom<string | number>(window.innerHeight)

export const flowAtom = atom<FlowType | null>(null)
export const flowsDataAtom = atom<FlowType[]>([])
export const flowSessionAtom = atom<FlowSessionType | null>(null)
export const currentSessionIdAtom = atom<string>()
export const selectFileAtom = atom<CustomUploadFile[]>([])

export const initChatsAtom = atom(null, async (get, set, defaultText?: string) => {
  const item: Chat = {
    id: nanoid(),
    isSystemPrompt: true,
    askTime: new Date(),
    ask: null as any,
    ans: [
      {
        role: 'assistant',
        status: 'success',
        content: [
          {
            type: 'hello-text',
            text: defaultText || helloText,
          },
        ],
      },
    ],
  }
  set(chatsAtom, [item])
})

// 创建写入 atom
export const appendChatAtom = atom(null, (get, set, newChat: Chat) => {
  const prev = get(chatsAtom)
  set(chatsAtom, [...prev, newChat])
})

export const updateChatAnsAtom = atom(null, (get, set, { id, newAns }: { id: string; newAns: Chat['ans'] }) => {
  const prev = get(chatsAtom)
  const updated = prev.map((chat) =>
    chat.id === id
      ? {
          ...chat,
          ans: newAns,
        }
      : chat,
  )
  set(chatsAtom, updated)
  setTimeout(scrollToBottom, 100)
})

export const chatsAtom = atom<Chat[]>([])

export const conversationIdAtom = atom<string | null>(null)
export const cancelTokenSourceAtom = atom<CancelTokenSource>(axios.CancelToken.source())

/** 当前推荐问题的suggestion数据 */
export const currentSuggestionAtom = atom<{
  data: string[]
  error: Error | null
  loading: boolean
} | null>(null)

export const isShowLoginModalAtom = atom<boolean>(false)

export const userInfoAtom = atomWithStorage<any>('userInfo', null)

export const envAtom = atom(async () => {
  // const response = await axios.get<APIResponse<Record<string, string>>>(
  //   askBIApiUrls.env.list
  // );
  // return response.data?.data;
})

export const showAskHistoryListAtom = atom<boolean>(false)

/** 模型列表 */
export const llmListAtom = atom<Llm[]>([])

/** 颜色的默认值，就是先检查 localStorage，然后检查 prefers-color-scheme */
const defaultTheme = IS_DARK ? 'dark' : 'light'
export const themeAtom = atom<ThemeType>(defaultTheme)

/** 存对话输入框相关方法 */
export const messageInputEditorRefAtom = atom<MessageInputEditorRef>({
  setHtml: () => {},
})

/** 最近选中的非码值的数据 */
export const latestSelectedMessageAtom = atom<JsonContentItem>({})

export const isShowSubDimensionAtom = atom<boolean>(false)

let intervalId: NodeJS.Timeout | null = null
export const agentOriginStructuredMessageAtom = atom<JsonContentItem[]>([])
// export const agentStructuredMessageAtom = atom<string | JsonContentItem[]>('')
export const agentStructuredMessageAtom = atom<JsonContentItem[], [JsonContentItem[] | string], void>(
  (get) => get(agentOriginStructuredMessageAtom),
  (get, set, data) => {
    let result: JsonContentItem[]
    if (!data || data.length === 0) {
      set(agentOriginStructuredMessageAtom, [])
      const { setHtml } = get(messageInputEditorRefAtom)
      setHtml('')
      return
    }
    if (typeof data === 'string') {
      result = [
        {
          type: STRUCTURED_MESSAGE_DATA_TYPE.TEXT,
          'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT,
          'data-content': data,
        },
      ]
    } else {
      result = data
    }
    set(agentOriginStructuredMessageAtom, result)
  },
)
// 根据json派生的纯文本提问内容
export const agentMessageAtom = atom<string>((get) => {
  const structuredMessage = get(agentStructuredMessageAtom)

  let result = ''
  structuredMessage.map((item) => {
    if (item['data-content']) {
      result += item['data-content']
    }
  })
  return result
})

export const updateMessageAndHtmlAtom = atom<null, [JsonContentItem[] | JsonContentItem | string, boolean?], void>(
  null,
  (get, set, data, updateCursor = true) => {
    const { updateCursorPosition, setHtml } = get(messageInputEditorRefAtom)
    let result: JsonContentItem[] = []

    const structuredMessage = get(agentStructuredMessageAtom)
    if (!data || data === '') {
      set(agentStructuredMessageAtom, data)
    } else if (typeof data === 'string') {
      const result = [{ 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT, 'data-content': data }]
      set(agentStructuredMessageAtom, result)
      setHtml?.(result)
    } else if (Array.isArray(data)) {
      // 如果是数组,则认为是完整替换原数据
      set(agentStructuredMessageAtom, data)
      setHtml?.(data)
    } else {
      result = [...structuredMessage, data]
      set(agentStructuredMessageAtom, result)
      setHtml?.(result)
    }
    if (updateCursor) {
      updateCursorPosition?.()
    }
  },
)

/**
 * 发起 Chat 请求
 */
export const makeAgentRequestAtom = atom(
  null,
  async (
    get,
    set,
    {
      isMiniMode,
      navigate,
      newMessage,
      sessionId,
    }: {
      isMiniMode: boolean
      navigate?: (path: string) => void
      newMessage?: string
      sessionId: string
    },
  ) => {
    const message = get(agentMessageAtom)
    const isSubmitting = get(isSubmittingAtom)
    const jsonMessage = get(agentOriginStructuredMessageAtom)
    const msg = newMessage || message
    const jsonContent = jsonMessage.length > 0 ? JSON.stringify(jsonMessage) : ''
    const cancelTokenSource = get(cancelTokenSourceAtom)
    const selectFileList = get(selectFileAtom)
    if (selectFileList.length === 0 && !msg) {
      return
    }

    if (isSubmitting) {
      return
    }

    set(isSubmittingAtom, true)
    set(updateMessageAndHtmlAtom, '', false)

    const chatId = nanoid()

    // 如果是 mini mode，点击 chat box 时，跳转到 chart page
    if (isMiniMode && navigate) {
      navigate(aiPlatformPageUrls.chatNew)
    }

    const assistantText = '正在处理中，请稍后...'

    // 设置Loading内容
    set(chatsAtom, (prevChats) => [
      ...prevChats,
      {
        id: chatId,
        askTime: new Date(),
        ask: {
          role: 'user',
          content: msg,
          jsonContent,
          parentId: null,
          fileList: selectFileList,
        },
        ans: [
          {
            role: 'assistant' as const,
            content: [{ type: 'text', text: assistantText } as const],
            status: ChatStatus.pending,
          },
        ],
      },
    ])

    setTimeout(scrollToBottom, 100)

    async function askApplyCoffeeChatAI(msg: string, sessionId: string, chatId: string) {
      set(selectFileAtom, [])
      const requestData: ChatRequestProps = {
        text: msg,
        session_id: sessionId,
        chatId,
      }

      const response = await fetch(chatAIApiUrls.chat.processMessage, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      })

      if (!response.body) {
        set(isSubmittingAtom, false)
        return
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder('utf-8')

      let buffer = ''
      let accumulatedText = ''

      set(isSubmittingAtom, true)

      const updateChat = throttle((newText: string) => {
        set(
          chatsAtom,
          produce((draft: Chat[]) => {
            const chat = draft.find((c) => c.id === chatId)
            if (chat) {
              chat.ans = [
                {
                  role: 'assistant',
                  status: 'success',
                  content: [{ type: 'apply-result', text: newText }],
                },
              ]
            }
          }),
        )
        // 在更新 chatsAtom 后延迟调用 scrollToBottom，确保 DOM 已更新
        setTimeout(scrollToBottom, 100)
      }, 100)

      try {
        // eslint-disable-next-line no-constant-condition
        while (true) {
          const { done, value } = await reader.read()
          if (done) {
            if (buffer.trim()) {
              accumulatedText += buffer
              updateChat(accumulatedText)
            }
            set(isSubmittingAtom, false)
            break
          }

          buffer += decoder.decode(value, { stream: true })
          const events = buffer.split('\n\n') // 按 SSE 事件分隔
          buffer = events.pop() || '' // 保留不完整的部分

          for (const event of events) {
            if (!event.trim().startsWith('data:')) continue

            const content = event.replace(/^data:\s*/, '').trim()
            if (content === '[DONE]') {
              set(isSubmittingAtom, false)
              break
            }

            // 拼接时保留原始换行符，不额外添加
            accumulatedText += content + '\n\n'
            updateChat(accumulatedText)
          }
        }
      } catch (error) {
        console.error('Stream reading error:', error)
        set(isSubmittingAtom, false)
      } finally {
        reader.releaseLock()
      }
    }

    /** 处理错误情况下的 chat 更新状态 */
    function handleErrorStatusChat(chatIdToUpdate: string, status: ChatStatusType, message: string) {
      set(
        chatsAtom,
        produce((draft) => {
          const theChat = draft.find((item) => item.id === chatIdToUpdate)
          if (!theChat) {
            return
          }
          theChat.ans = [
            {
              role: 'assistant' as const,
              content: [{ type: 'text', text: message } as const],
              sceneId: theChat.ans[0].sceneId,
              status: status,
              ansTime: new Date(),
            },
          ]
        }),
      )
    }

    const promiseArray = []

    const fileNames = (selectFileList || []).map((file) => file.name).join('、')
    const suffix = fileNames ? `${fileNames}（已上传）` : ''
    promiseArray.push(askApplyCoffeeChatAI(`${msg}${suffix}`, sessionId, chatId))

    Promise.all(promiseArray)
      .then(() => {
        console.info('All requests have been done.')
      })
      .catch((error) => {
        console.error('请求中出现错误', error)
        intervalId && clearInterval(intervalId)
        intervalId = null
        set(isSubmittingAtom, false)
        if (axios.isCancel(error)) {
          console.info('The request was cancelled manually.', error.message)
          set(cancelTokenSourceAtom, axios.CancelToken.source())
          handleErrorStatusChat(chatId, ChatStatus.failure, (error as AxiosError).message)
        } else {
          handleErrorStatusChat(chatId, ChatStatus.failure, '出错了，请联系管理员。' + (error as AxiosError).message)
        }
      })
  },
)

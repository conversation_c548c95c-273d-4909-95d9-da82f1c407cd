.ask-bi-home-page {
  .home-config .ant-btn-primary {
    padding: 12px 50px;
    border-radius: 31px;
    background-color: #6a58ec;
  }

  .home-config .ant-btn-primary:hover {
    background-color: #5d4dda !important;
  }

  .suggestionsItem .ant-btn-default {
    padding: 12px 16px;
    border: 1px solid rgba(255 255 255 / 0.44);
  }

  .suggestionsItem .ant-btn-default:hover {
    border: 1px solid rgba(255 255 255 / 0.44) !important;
    background: rgb(255 255 255 / 0.24) !important;
    color: #fff !important;
  }

  .animationBox {
    animation: move 10s linear infinite;
    animation-duration: 50s;
    -webkit-box-flex: 0;
  }
}

@keyframes fade-slide-in {
  0% {
    opacity: 0;
    transform: translateY(12px);
  }

  60% {
    opacity: 1;
    transform: translateY(-2px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-slide-in {
  animation: fade-slide-in 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) 0.2s both;
}

@keyframes chat-box-fade-slide-in {
  0% {
    opacity: 0;
    transform: translateY(12px);
  }

  60% {
    opacity: 1;
    transform: translateY(-2px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-box-fade-slide-in {
  animation: chat-box-fade-slide-in 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) 0.6s both; /* 1s delay */
}
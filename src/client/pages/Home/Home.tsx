/**
 * @description Ask BI 的首页
 */
import React, { useRef } from 'react'
import './Home.css'
import ChatBox from '../Chat/ChatBox'

function HomePage() {
  const chatBoxRef = useRef<{
    handleSuggestion: (e: React.FormEvent, message: string) => void
  }>(null)

  const renderConfiguredUI = (
    <div className="home flex h-full flex-col items-center justify-center">
      <div className="home-content flex flex-col items-center justify-center">
        <div className="fade-slide-in mb-8 text-center text-5xl font-bold leading-[120%] text-[#1C1C1C]">
          Hi, how would you like to <br /> begin your session?
        </div>
        <div className="chat-box-fade-slide-in">
          <ChatBox isMiniMode={true} ref={chatBoxRef} />
        </div>
      </div>
    </div>
  )

  return <main className="ask-bi-home-page h-full dark:bg-slate-800">{renderConfiguredUI}</main>
}

export default HomePage

/**
 * @description Chart 的 ChatBox 组件，用于渲染图表类的聊天组件。包含：问题输入框，会话列表，推荐问题等
 */
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import clsx from 'clsx'
import { useAtomValue, useSetAtom } from 'jotai'
import { message } from 'antd'
import { IS_H5, messageSafeAreaClass } from 'src/shared/constants'
import MessageInput from 'src/client/components/MessageInput'
import ChatHistoryItem from 'src/client/components/ChatHistoryItem'
import {
  chatsAtom,
  isSubmittingAtom,
  themeAtom,
  makeAgentRequestAtom,
  initChatsAtom,
  agentStructuredMessageAtom,
  flowSessionAtom,
  currentSessionIdAtom,
} from '../AIAtoms'
import ChatBoxWrapper from './ChatBoxWrapper'

interface Props {
  /** 被首页嵌入时为 true，其他情况为 false */
  isMiniMode: boolean
  className?: string
}

function ChatBox(
  props: Props,
  ref: React.Ref<{
    handleSuggestion: (e: React.FormEvent, message: string) => void
  }>,
) {
  const navigate = useNavigate()

  const setMessage = useSetAtom(agentStructuredMessageAtom)
  // 当前正在基于[currentFollowUpQuestion]追问
  const initChats = useSetAtom(initChatsAtom)
  const chats = useAtomValue(chatsAtom)
  const theme = useAtomValue(themeAtom)
  const flowSession = useAtomValue(flowSessionAtom)
  // 表示是否正在提交，如果正在提交，置灰提交按钮，禁止下一次提交，但用户仍能输入
  const isSubmitting = useAtomValue(isSubmittingAtom)

  // 提交 agent 融合请求
  const makeAgentRequest = useSetAtom(makeAgentRequestAtom)

  const inputRef = useRef<{ focus: () => void; blur: () => void }>(null)

  useEffect(() => {
    chats.length === 0 && flowSession && initChats(flowSession?.prompt || '')
  }, [chats, flowSession, initChats])

  const handleSubmit = useCallback(
    (newMessage?: string) => {
      // if (!flowSession?.session_id) {
      //   message.error('Failed to obtain session ID, please refresh the page and try again')
      //   return
      // }
      makeAgentRequest({
        navigate,
        isMiniMode: props.isMiniMode,
        newMessage: newMessage,
        sessionId: flowSession?.session_id as string,
      })
      setTimeout(() => {
        // 回车后输入框失焦, 为了隐藏指标弹窗
        // 加了定时器才能清空输入框里的内容并失焦
        inputRef.current?.blur()
      }, 0)
    },
    [flowSession?.session_id, makeAgentRequest, navigate, props.isMiniMode],
  )

  const pressEnterToSubmit = useCallback(
    (e: React.KeyboardEvent<Element>) => {
      if (e.key === 'Enter') {
        e.preventDefault()
        handleSubmit()
      }
    },
    [handleSubmit],
  )

  useImperativeHandle(ref, () => ({
    handleSuggestion: (e: React.FormEvent, message: string) => {
      handleSuggestionClick(e, message)
    },
  }))

  const handleSuggestionClick = useCallback(
    (_e: React.FormEvent, message: string) => {
      // inputRef.current?.blur()
      setMessage(message)
      handleSubmit(message)
    },
    [handleSubmit, setMessage],
  )
  const renderMessageInput = (
    <div className={clsx('w-full overscroll-contain md:max-w-4xl')}>
      <MessageInput
        isFirstMessage={chats.length < 2 /** 第一个为打招呼信息，所以忽略掉 */}
        enableLlmToggle={!__DISABLE_LLM_TOGGLE__}
        isMiniMode={props.isMiniMode}
        isSubmitting={isSubmitting}
        pressEnterToSubmit={(e) => {
          pressEnterToSubmit(e)
        }}
        onSubmit={(message?: string | undefined) => {
          handleSubmit(message)
        }}
        ref={inputRef}
      />
    </div>
  )

  const renderChatHistory = (
    <div
      id="chat-history-box"
      className="chat-history flex w-full flex-1 flex-col gap-4 overflow-y-auto overflow-x-hidden px-1 pb-2 pt-1 md:px-2 md:pb-4"
    >
      {chats.map((chat, index) => (
        <ChatHistoryItem key={index} chat={chat} theme={theme} />
      ))}
    </div>
  )

  if (props.isMiniMode) {
    return (
      <div className="chat-box shadow-blue-40 w-screen">
        <ChatBoxWrapper>
          <div id="chat-toolbar" className={clsx('query', messageSafeAreaClass, { 'bg-[#F5F5F7]': IS_H5 })}>
            <div className="flex flex-col items-center justify-center pb-6">{renderMessageInput}</div>
          </div>
        </ChatBoxWrapper>
      </div>
    )
  }

  return (
    <div className={clsx('chat-box flex h-full max-w-4xl flex-col text-sm dark:bg-slate-900', props.className)}>
      {renderChatHistory}
      <ChatBoxWrapper>
        <div id="chat-toolbar" className={clsx(messageSafeAreaClass, 'relative', { 'bg-[#F5F5F7]': IS_H5 })}>
          {/* {renderStopGenerate} */}
          <div className={clsx('message-input-wrapper bg-[#F5F5F7] pb-4 md:bg-inherit md:pb-1')}>
            {renderMessageInput}
          </div>
        </div>
      </ChatBoxWrapper>
    </div>
  )
}

export default forwardRef(ChatBox)

import React, { ReactElement, useLayoutEffect, useRef } from 'react'
import clsx from 'clsx'
import { isIOS } from 'src/client/utils'
import { IS_H5 } from 'src/shared/constants'

export default function ChatBoxWrapper(props: { children: ReactElement }) {
  const { children } = props
  // const [height, setHeight] = useState(0)
  const wrapperRef = useRef<HTMLDivElement | null>(null)

  // todo 暂时注释, 后面测试没问题再删除
  // useLayoutEffect(() => {
  //   const handleSetHeight = () => {
  //     const chatElement = document.getElementById('chat-toolbar')

  //     if (chatElement) {
  //       const height = chatElement.offsetHeight
  //       setHeight(height)
  //     }
  //   }
  //   let targetElement = document.getElementById('chat-toolbar')

  //   const observer = new MutationObserver(handleSetHeight)
  //   if (targetElement) {
  //     observer.observe(targetElement, {
  //       attributes: true,
  //       childList: true,
  //       subtree: true,
  //     })
  //   }

  //   return () => {
  //     targetElement = null
  //     observer.disconnect()
  //   }
  // }, [])

  useLayoutEffect(() => {
    const wrapper = wrapperRef.current
    const handler = (e: TouchEvent) => {
      const chatDatasetPopover = document.querySelector(
        '.chat-box-wrapper .chat-dataset-popover .chat-dataset-popover-content.ant-popover-open',
      )
      // 判断一下防止在场景弹窗内无法滚动
      if (e.target !== wrapper && !chatDatasetPopover?.contains(e.target as Node)) {
        e.preventDefault()
      }
    }
    if (wrapper && isIOS) {
      wrapper.addEventListener('touchmove', handler, { passive: false })
    }
    return () => {
      if (wrapper && isIOS) {
        wrapper.removeEventListener('touchmove', handler)
      }
    }
  }, [])

  return (
    <div className="chat-box-wrapper overscroll-contain" ref={wrapperRef} style={{ zIndex: 11 }}>
      <div className={clsx({ 'left-0 right-0': IS_H5 })}>{children}</div>
    </div>
  )
}

/**
 * @description 聊天页面
 */
import React, { useState } from 'react'
import clsx from 'clsx'
import ChatBox from './ChatBox'

function ChatPage({ className }: { className?: string }) {
  const [isChatBoxMiniMode, _setIsChatBoxMiniMode] = useState<boolean>(false)
  return (
    <div className={clsx('chat-page flex h-[calc(100vh-100px)] flex-grow place-items-stretch', className)}>
      <div className="chat-page-inner mx-auto flex flex-grow flex-col md:max-w-[1000px] md:px-6">
        <ChatBox key="chat" isMiniMode={isChatBoxMiniMode} />
      </div>
    </div>
  )
}

export default ChatPage

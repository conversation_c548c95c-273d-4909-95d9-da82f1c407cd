/**
 * 存储chat相关的一些工具函数
 */
import { AnsChatItem, AssistantChatItem, ChatResponse, ChatResponseError, ChatStatus } from '@shared/common-types'

/** 将成功的response转化为chatAns */
export function readyResponseToChatAns(
  response: Exclude<ChatResponse, ChatResponseError>,
  chatId: string,
): AnsChatItem {
  const taskType = response.taskType
  const commonResponse = {
    chatId,
    status: ChatStatus.success,
    ansTime: new Date(),
  }
  switch (taskType) {
    case 'apply-result': {
      return {
        role: 'assistant',
        content: [
          {
            type: 'apply-result',
            text: response.text,
          },
        ],
        ...commonResponse,
      }
    }
    default:
      return {
        role: 'assistant',
        content: [
          {
            type: 'text',
            text: '未知错误，请稍后再试',
          },
        ],
        ...commonResponse,
      }
  }
}

/**
 * 将失败的response转化为chatAns
 * suggestions 可以为空，空则不推荐问题
 */
export function unreadyResponseToChatAns(response: ChatResponseError, sceneId: string): AnsChatItem {
  const unReadyAnsContent: AssistantChatItem[] = [
    {
      type: 'chat-error',
      ...response,
    },
  ]
  return {
    role: 'assistant',
    content: unReadyAnsContent,
    sceneId,
    status: ChatStatus.success,
    ansTime: new Date(),
  }
}

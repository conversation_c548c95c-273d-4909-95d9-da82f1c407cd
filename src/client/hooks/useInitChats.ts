import { message } from 'antd'
import axios from 'axios'
import { useAtom, useSet<PERSON>tom } from 'jotai'
import { useRequest } from 'ahooks'
import { chatAIApiUrls } from 'src/shared/url-map'
import { flowAtom, flowsDataAtom, flowSessionAtom, currentSessionIdAtom, initChatsAtom } from '../pages/AIAtoms'

export function useInitChats() {
  const [currentFlow, setCurrentFlow] = useAtom(flowAtom)
  const [flowsData, setFlowsData] = useAtom(flowsDataAtom)
  const setFlowSession = useSetAtom(flowSessionAtom)
  const setCurrentSessionId = useSetAtom(currentSessionIdAtom)
  const initChats = useSetAtom(initChatsAtom)

  // 加载流程
  const { run: getFlows } = useRequest(
    async () => {
      const response = await axios.get(chatAIApiUrls.chat.flows)
      const result = response.data?.data || []
      setFlowsData(result)
      if (result.length === 0) {
        message.error('可用流程列表为空，请联系管理员！')
        return
      } else {
        setFlowsData(result)
        setCurrentFlow(response.data?.data[0])
        initChatSessions(response.data?.data[0].flow_name)
      }
    },
    { manual: true },
  )

  // 加载会话
  const initChatSessions = async (flowName?: string) => {
    if (!currentFlow?.flow_name && !flowName) {
      getFlows()
    }
    const response = await axios.post(chatAIApiUrls.chat.sessions, { flow_name: flowName || currentFlow?.flow_name })
    const session = response.data?.data
    setFlowSession(session)
    setCurrentSessionId(session?.session_id)
    initChats(session?.prompt)
  }

  const clearHistory = () => {
    initChats()
  }

  return {
    flowsData,
    getFlows,
    initChatSessions,
    clearHistory,
  }
}

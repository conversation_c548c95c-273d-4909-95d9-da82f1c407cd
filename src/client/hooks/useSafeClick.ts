import { useLayoutEffect } from 'react'

export const useSafeClick = (safeAreaClass: string, callback: Function) => {
  useLayoutEffect(() => {
    const handleSafeClick = (event: MouseEvent) => {
      // 点击弹窗如果在安全区域内则不关闭弹窗, 将弹窗关闭与输入框失焦操作解耦
      const safeAreas = Array.from(document.querySelectorAll(`.${safeAreaClass}`))
      const isInSafeArea = safeAreas.some((safeArea) => {
        const path = event.composedPath()
        // 判断点击目标是否在安全区域内
        if (safeArea && path.includes(safeArea)) {
          // 点击在安全区域内
          return true
        } else {
          // 点击在安全区域外
          return false
        }
      })

      !isInSafeArea && callback?.()
    }

    document.addEventListener('click', handleSafeClick)

    return () => {
      document.removeEventListener('click', handleSafeClick)
    }
  }, [safeAreaClass, callback])
}

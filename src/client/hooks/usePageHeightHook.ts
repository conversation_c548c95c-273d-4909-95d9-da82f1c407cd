import { useAtom } from 'jotai/react'
import { useCallback, useEffect, useLayoutEffect } from 'react'
import { isAutofocus, isIOS } from '../utils'
import { pageHeightAtom } from '../pages/AIAtoms'

export function usePageHeight() {
  const [pageHeight, setPageHeight] = useAtom(pageHeightAtom)

  const getInnerHeight = useCallback(() => {
    const heightNum = Number(window.innerHeight)
    const height = heightNum ? heightNum + 'px' : '100vh'
    return height
  }, [])

  useLayoutEffect(() => {
    setPageHeight(getInnerHeight())
  }, [setPageHeight, getInnerHeight])

  useEffect(() => {
    // 页面resize时, 重新设置高度
    function syncChangeHeight() {
      const height = getInnerHeight()
      setPageHeight(height)
    }
    // 页面resize时, 重新设置高度
    function iosChangeHeight() {
      setTimeout(() => {
        syncChangeHeight()
      }, 100)
    }

    if (isIOS) {
      window.addEventListener('focusin', iosChangeHeight)
      window.addEventListener('focusout', iosChangeHeight)
    } else {
      window.addEventListener('resize', syncChangeHeight)
    }
    return () => {
      if (isIOS) {
        window.removeEventListener('focusin', iosChangeHeight)
        window.removeEventListener('focusout', iosChangeHeight)
      } else {
        window.removeEventListener('resize', syncChangeHeight)
      }
    }
  }, [setPageHeight, getInnerHeight])

  if (isIOS && isAutofocus()) {
    // 宝武app高度有问题, ios需要额外减去一定高度才行, 后续其他场景不适配再加针对宝武的判断条件
    return ['calc(100vh - 98px)', setPageHeight]
  } else {
    return [pageHeight, setPageHeight]
  }
}

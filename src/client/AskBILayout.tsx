import React, { useEffect } from 'react'
import { Layout, ConfigProvider, App } from 'antd'
import { Outlet } from 'react-router-dom'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import clsx from 'clsx'
import zhCN from 'antd/lib/locale/zh_CN'
import { IS_H5 } from '@shared/constants'
import { getAntdConfigProviderTheme } from './utils'
import { usePageHeight } from './hooks/usePageHeightHook'
import { isSidebarOpenAtom, themeAtom } from './pages/AIAtoms'
import Navbar from './components/Navbar'
import Sidebar from './components/Sidebar'

export function AskBILayout() {
  const [theme] = useAtom(themeAtom)
  const isSidebarOpen = useAtomValue(isSidebarOpenAtom)
  const [pageHeight] = usePageHeight()

  return (
    <ConfigProvider locale={zhCN} theme={getAntdConfigProviderTheme(theme)}>
      <App>
        <Layout
          className={clsx('layout-root text-black dark:bg-slate-900 dark:text-slate-100')}
          style={IS_H5 ? { height: `min(${pageHeight}, 100vh)` } : { height: '100vh' }}
        >
          <Sidebar />
          <div
            className={`flex-1 overflow-y-auto transition-all duration-300 ${isSidebarOpen ? 'ml-[244px]' : 'ml-0'}`}
          >
            <Navbar />

            <main
              className={`main-content py-6 ${isSidebarOpen ? 'pl-6' : ''} bg-white`}
              style={{ height: 'calc(100% - 64px)' }}
            >
              <Outlet />
            </main>
          </div>
        </Layout>
      </App>
    </ConfigProvider>
  )
}

export default AskBILayout

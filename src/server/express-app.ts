import path from 'path'
import express from 'express'
import session from 'express-session'
import bodyParser from 'body-parser'
import cors from 'cors'
import cookieParser from 'cookie-parser'
import { APIResponse } from 'src/shared/common-types'
import { setAxiosDefaults } from 'src/shared/config'
import chats from './coffee/chat/api'
import { createStaticCompression, setAxiosUserId } from './utils'

declare module 'express-session' {
  interface SessionData {
    username?: string // 登录成功后，会在 session 中添加 username 属性
    extra?: {
      tianhongToken?: string
    }
  }
}

const app = express.Router()

app.use(bodyParser.json({ limit: '512mb' }))
app.use(express.urlencoded({ extended: true }))
app.use(cors())
app.use(cookieParser())
app.use(createStaticCompression())

// 配置 express-session
app.use(
  session({
    secret: 'askbi-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: {
      httpOnly: true,
      maxAge: 3 * 24 * 60 * 60 * 1000, // 默认过期时间 3天
    },
    // cookie: { secure: true }, // TODO: 生产环境应该使用 HTTPS
  }),
)

// app.use(verifyLogin)

type ResponseWithBody = express.Response & { responseBody?: APIResponse<any> }

// 记录请求信息的中间件
app.use(async (req, res: ResponseWithBody, next) => {
  try {
    const username = req.session.username || ''
    const traceId = req.header('traceId') as string
    // FIXME: 有风险，因为 node.js 是单线程的，如果同时有多个请求，会覆盖 username
    username && setAxiosUserId(username)
    traceId && setAxiosDefaults(traceId)
    const originalJson = res.json
    res.json = function (body) {
      res.responseBody = body
      return originalJson.call(this, body)
    }

    next()
  } catch (error) {
    console.error('记录日志出错', error)
    next()
  }
})

app.use('/api/chats', chats)

app.use('/', express.static(path.join(__dirname, '../../dist-client/')))
app.use(bodyParser.text({ type: 'text/event-stream' }))

app.get('/*', (_req, res) => {
  res.sendFile(path.join(__dirname, '../../dist-client', 'index.html'))
})

export default app

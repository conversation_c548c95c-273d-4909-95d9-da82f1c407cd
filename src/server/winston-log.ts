import winston from 'winston'
import DailyRotateFile from 'winston-daily-rotate-file'
const { combine, timestamp } = winston.format

const defaultOptions = {
  datePattern: 'YYYY-MM-DD',
  zippedArchive: false, // 禁用压缩归档。
  maxSize: '500m',
  maxFiles: '7d', // 表示保留最近 7 天的日志文件，自动删除更早的日志文件。
}

// 创建Winston日志记录器  winston
export const logger = winston.createLogger({
  format: combine(timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston.format.json()),
  defaultMeta: { service: 'user-service' },
  transports: [
    new DailyRotateFile({
      filename: 'logs/%DATE%.log',
      level: 'info',
      ...defaultOptions,
    }),
  ],
})

// AskBot业务日志整理
export function saveAskBotBusinessLogs(
  startTime: number,
  username: string,
  traceId: string,
  host: string,
  serviceType: string,
  resultCode: number,
  moduleType: string,
  reqBody: any,
  output: any,
  debug: any,
) {
  const result = {
    timestamp: new Date().toISOString(),
    user_id: username,
    request_id: traceId,
    host,
    service_type: serviceType || 'web_service',
    duration: Date.now() - startTime,
    result_code: resultCode,
    module_type: moduleType,
    input: reqBody,
    output,
    debug,
    url: debug?.url ?? 'empty_need_to_handle',
  }
  logger.info(result)
}

/** 记录node请求信息到文件 因为result信息较多不确定类型，所以在此用any */
export function saveRequestLog(username: string, traceId: string, result: any) {
  logger.info({ username, traceId, message: result })
}

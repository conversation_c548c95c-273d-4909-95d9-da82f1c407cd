/**
 * @description server 侧的工具函数集合
 */
import path from 'path'
import fs from 'fs'
import axios from 'axios'
import { RequestHandler } from 'express'
import { NodeSSH } from 'node-ssh'
import sharp from 'sharp'
import { Llm, UserPermissionDatasource, UserPermissionLlmType, UserPermissionProject } from '@shared/common-types'
import { PROCESS_ENV } from 'src/server/server-constants'
const ssh = new NodeSSH()

export function safeJSONParse(JSONStr: string) {
  try {
    return JSON.parse(JSONStr)
  } catch (err) {
    return null
  }
}
/**
 * 去除掉开头和末尾的 引号
 */
function removeQuotesFromStartAndEnd(text: string): string {
  if (text.startsWith('"') || text.startsWith("'")) {
    text = text.slice(1)
  }
  if (text.endsWith('"') || text.endsWith("'")) {
    text = text.slice(0, -1)
  }
  return text
}

/**
 * 去除 sql 中的换行符号，合并多个空格为1个空格
 */
function removeNewlinesAndMergeSpaces(sql: string): string {
  return sql
    .replace(/\n|\\n|\\/g, ' ') // 去掉换行符
    .replace(/ +/g, ' ') // 多个空格合并成一个
    .trim()
}

/**
 * 抽取sql语句 并去掉换行符
 */
export function extractFormattedSQL(text: string): string | null {
  // 匹配到 markdown 格式的 sql 语句，sql 中必须有 select 才算
  const sqlMarkDownRegex = /```(sql)?\s*?select([\s\S]*?)```/im

  const markdownMatch = text.match(sqlMarkDownRegex)
  if (markdownMatch && markdownMatch.length >= 3) {
    return removeNewlinesAndMergeSpaces('SELECT ' + markdownMatch[2])
  }

  // 如果返回的是纯sql，根据正则进行匹配，从【select】开始 到【;】结束
  const sqlRegex = /^((?:\s*)?(SELECT|select)[\s\S]*?)(?=(?:;|$))/g
  const textWithoutQuotes = removeQuotesFromStartAndEnd(text)
  const commonSqlMatch = textWithoutQuotes.match(sqlRegex)
  if (commonSqlMatch) {
    return removeNewlinesAndMergeSpaces(commonSqlMatch[0])
  }

  // 如果返回 JSON 格式，里面有 sql，也做支持
  try {
    const data = JSON.parse(text)
    if (data.sql) {
      return removeNewlinesAndMergeSpaces(data.sql)
    }
  } catch (e) {
    return null // 非 json，不做处理
  }
  return null
}

/**
 * 所有可用的大模型列表，为了数据安全，只有 server 端可以访问
 * @type 传递到后端的 llm-type
 * @name dropdown中每一项的label
 * @abbrName 选中模型后，展示在Input框中的label
 */
export const ALL_LLMS: Llm[] = [
  {
    type: 'wanx-v1',
    name: 'WX-LLM',
    abbrName: 'WX-LLM',
    tokenLimit: 4096,
    logo: 'WX-LLM',
    disable: false,
  },
  {
    type: 'cogview-3-plus',
    name: 'ZP-LLM',
    abbrName: 'ZP-LLM',
    tokenLimit: 16000,
    logo: 'ZP- LLM',
    disable: false,
  },

  // {
  //   type: 'sd-llm',
  //   name: 'SD-LLM',
  //   abbrName: 'SD-LLM',
  //   tokenLimit: 16384,
  //   logo: 'SD-LLM',
  //   disable: false,
  // },
]

// 从node中req中拿到username后设置到axios上,设置之前先获取username，判断，避免重复设置
export function setAxiosUserId(username: string) {
  axios.defaults.headers.common['userId'] = username
}

/**
 * 从字符串中提取出 JSON，用于从 LLM 结果中提取 JSON，是否返回 null。
 * 注意，只能提取出 {}，不能提取出 []
 * 搜索字符串，找到第一个 { 符号和对应的闭合 } 符号，以此来提取 JSON 对象。
 */
export function extractJsonFromString<T = any>(str: string) {
  // 首先匹配 ```json {} ``` 格式的 JSON
  const jsonRegex = /```json\s*([\s\S]*?)```/i
  const match = jsonRegex.exec(str)

  if (match) {
    try {
      return JSON.parse(match[1])
    } catch (error) {
      console.error(`Unexpected parsing JSON: ${str}, error: ${(error as Error).message}`)
      return null
    }
  }

  // 如果没有匹配到 ```json {} ``` 格式的 JSON，再尝试匹配普通的 JSON
  const stack: string[] = []
  let inString = false
  let escape = false
  let jsonStr = ''
  let start = -1

  for (let i = 0; i < str.length; i++) {
    const char = str[i]

    if (inString) {
      if (escape) {
        escape = false
      } else if (char === '\\') {
        escape = true
      } else if (char === '"') {
        inString = false
      }
    } else {
      if (char === '"') {
        inString = true
      } else if (char === '{') {
        stack.push('{')
        if (start === -1) start = i
      } else if (char === '}') {
        stack.pop()
        if (stack.length === 0) {
          jsonStr = str.substring(start, i + 1)
          break
        }
      }
    }
  }

  if (jsonStr) {
    // 尝试解析 JSON，如果失败则返回 null
    try {
      return JSON.parse(jsonStr) as T
    } catch (error) {
      console.error('解析 JSON 时出错：', error)
      return null
    }
  } else {
    return null
  }
}

interface RoleBase {
  id: string
  name: string
}

interface RoleWithDatasource extends RoleBase {
  rolePermissionDatasources: {
    id: string
    roleId: string
    datasourceId: string
  }[]
}

interface RoleWithLlm extends RoleBase {
  rolePermissionLlms: {
    id: string
    roleId: string
    llmType: string
  }[]
}

interface RoleWithProject extends RoleBase {
  rolePermissionProjects: {
    id: string
    roleId: string
    semanticProjectId: string
  }[]
}

/**
 * 去掉url中的ip和端口，用于在后端代理
 * @param url 文件url
 * @returns
 */
export function removeDomainFromUrl(url: string) {
  const pathMatch = url.match(/:\/\/[^/]*(\/.*)/)
  return pathMatch ? pathMatch[1] : ''
}

/**
 * 通用路径处理函数，从项目根路径开始引入
 * @param p 路径集合
 */
export function resolve(...p: string[]) {
  return path.resolve(__dirname, '..', '..', ...p)
}

/**
 * client打包产物路径，方便后续的路径拼接
 */
export const PATH_DIST_CLIENT = resolve('dist-client')

/**
 * 静态压缩中间件
 */
export function createStaticCompression() {
  const needStaticCompressionSet = new Set(['.js', '.css'])
  // 只对js和css进行压缩文件返回
  function needStaticCompression(filePath: string) {
    return needStaticCompressionSet.has(path.extname(filePath))
  }
  // 通过后缀获得content-type
  function getContentType(filePath: string) {
    switch (path.extname(filePath)) {
      case '.js':
        return 'application/javascript'
      case '.css':
        return 'text/css'
      default:
        return null
    }
  }
  const requestHandler: RequestHandler = (req, res, next) => {
    const filePath = resolve(PATH_DIST_CLIENT, '.' + req.url)
    // 用来判断chrome是否支持某个压缩
    const acceptEncoding = req.headers['accept-encoding']
    const contentType = getContentType(filePath)
    // 提前校验基础信息是否符合
    if (needStaticCompression(filePath) && contentType && fs.existsSync(filePath)) {
      if (acceptEncoding?.includes('br') && fs.existsSync(filePath + '.br')) {
        // 检测是否可以br压缩
        res.setHeader('Content-Encoding', 'br')
        res.setHeader('Content-Type', contentType)
        fs.createReadStream(filePath + '.br').pipe(res)
      } else if (acceptEncoding?.includes('gzip') && fs.existsSync(filePath + '.gz')) {
        // 检测是否可以gzip压缩
        res.setHeader('Content-Encoding', 'gzip')
        res.setHeader('Content-Type', contentType)
        fs.createReadStream(filePath + '.gz').pipe(res)
      } else {
        next()
      }
    } else {
      next()
    }
  }
  return requestHandler
}

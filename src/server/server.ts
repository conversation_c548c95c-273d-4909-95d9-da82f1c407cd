/**
 * @description server entry
 * Setup dotenv at the beginning, so that you can use it outside of function
 */
import moduleAlias from 'module-alias'

// 注册别名，这里只需要考虑 server 使用的别名，完整的参考 vite.config.ts
moduleAlias.addAliases({
  src: __dirname + '/../',
  '@shared': __dirname + '/../shared',
  '@server/utils': __dirname + '/../server/utils',
})

import express, { Express } from 'express'
import { PROCESS_ENV } from 'src/server/server-constants'
import { aiPlatformPageUrls } from 'src/shared/url-map'
import '../shared/config'
import app from './express-app'

const port = PROCESS_ENV.PORT

// 在外部包裹一层express，可以在内部逻辑基本不需要修改的情况下，快速增加BASE_URL
const server: Express = express()
server.use(PROCESS_ENV.BASE_URL, app)
server.use('/*', (_, res) => {
  return res.redirect(aiPlatformPageUrls.login)
})

server.listen(port, '0.0.0.0', () => {
  console.info(`✅[bank of China]: Server is running at http://0.0.0.0:${port}`)
})

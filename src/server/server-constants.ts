import { join } from 'path'
import * as dotenv from 'dotenv'

// 只在测试环境中加载 .env.test 文件
if (process.env.NODE_ENV === 'test') {
  dotenv.config({ path: join(__dirname, '../../.env.test') })
} else {
  dotenv.config({
    // 有些环境会自动加载.env文件，比如启动pyenv的时候，vscode的终端会加载.env
    // 此时如果没有override，就会不会覆盖process.env上的变量，导致变量缓存
    // 增加override后，强制每次都读取.env文件上的变量对process.env做覆盖
    override: true,
  })
}

import { EnvError, cleanEnv, makeValidator, port, str } from 'envalid'

const strNoEmpty = makeValidator<string>((input: string) => {
  if (typeof input === 'string' && input.length > 0) return input
  throw new EnvError(`Not a string: "${input}"`)
})
export const PROCESS_ENV = cleanEnv(process.env, {
  PORT: port(),
  BASE_URL: str({ default: '' }),
  FLOW_API_URL: strNoEmpty(),
})

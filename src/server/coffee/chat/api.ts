/**
 * 问答的 API 接口定义
 */
import express, { Router, Request, Response } from 'express'
import axios from 'axios'
import multer from 'multer'
import FormData from 'form-data'
import { MAX_COUNT_UPLOAD_LIMIT } from 'src/shared/constants'
import { defaultResponseHeaders } from 'src/shared/sse'

const router: Router = express.Router()
const upload = multer()

router.get('/flows', async (req: Request, res: Response) => {
  console.info('get /api/flows: ', req.body)
  try {
    const result = await axios.get(process.env.FLOW_API_URL + '/api/flows')
    return res.json({ code: 0, data: result.data.flows })
  } catch (error: any) {
    return res.json({ code: 500, message: error.message })
  }
})

router.post('/sessions', async (req: Request, res: Response) => {
  console.info('post /sessions: ', req.body)
  try {
    const result = await axios.post(process.env.FLOW_API_URL + '/sessions', req.body)
    return res.json({ code: 0, data: result.data })
  } catch (error: any) {
    return res.json({ code: 500, message: error.message })
  }
})

router.post('/processMessage', async (req: Request, res: Response) => {
  console.info('post /processMessage: ', req.body)

  try {
    for (const [k, v] of Object.entries(defaultResponseHeaders)) {
      res.setHeader(k, v)
    }
    res.flushHeaders()
    const response = await axios.post(`${process.env.FLOW_API_URL}/v1/process_message`, req.body, {
      responseType: 'stream',
    })

    let buffer = ''
    response.data.on('data', (chunk: Buffer) => {
      const chunkStr = chunk.toString('utf8')
      buffer += chunkStr

      // 按 SSE 事件分隔符（\n\n）分割
      const paragraphs = buffer.split('\n\n')
      buffer = paragraphs.pop() || '' // 保留不完整的部分

      for (const paragraph of paragraphs) {
        if (paragraph.trim()) {
          res.write(`data: ${paragraph}\n\n`) // 发送完整的段落
        }
      }
    })

    response.data.on('end', () => {
      if (buffer.trim()) {
        res.write(`data: ${buffer}\n\n`) // 🔥 强制把没发出去的短内容发送
      }
      res.write('data: [DONE]\n\n')
      res.end()
    })

    response.data.on('error', (err: Error) => {
      console.error('转发流出错', err)
      res.end()
    })
  } catch (error: any) {
    console.error('【AskDoc】-query-document-error', error)
    return res.status(200).json({
      code: 0,
      msg: error?.message,
      data: null,
    })
  }
})

router.get('/sessions', async (req: Request, res: Response) => {
  console.info('get /api/sessions: ', req.query)
  try {
    const result = await axios.get(process.env.FLOW_API_URL + '/sessions', { params: req.query })
    return res.json({ code: 0, data: result.data })
  } catch (error: any) {
    return res.json({ code: 500, message: error.message })
  }
})

router.get('/sessions/:sessionId', async (req: Request, res: Response) => {
  console.info('get /sessions/detail: ', req.params)
  const sessionId = req.params.sessionId
  try {
    const result = await axios.get(`${process.env.FLOW_API_URL}/sessions/${sessionId}`)
    return res.json({ code: 0, data: result.data })
  } catch (error: any) {
    return res.json({ code: 500, message: error.message })
  }
})

router.delete('/sessions/:sessionId', async (req: Request, res: Response) => {
  console.info('delete /sessions/detail: ', req.params)
  const sessionId = req.params.sessionId
  try {
    const result = await axios.delete(`${process.env.FLOW_API_URL}/sessions/${sessionId}`)
    console.info('delete result.data', result.data)
    return res.json({ code: 0, data: result.data })
  } catch (error: any) {
    console.error('error', error)
    return res.json({ code: 500, message: error.message })
  }
})

/**
 * 删除文件
 */
router.delete('/delete-file/:sessionId/:fileId', async (req: Request, res: Response) => {
  const { sessionId, fileId } = req.params
  console.info('delete /sessions/:sessionId/files/:fileId', sessionId, fileId)

  try {
    const result = await axios.delete(`${process.env.FLOW_API_URL}/sessions/${sessionId}/files/${fileId}`)
    console.info('delete result.data', result.data)
    return res.json({ code: 0, data: result.data })
  } catch (error: any) {
    console.error('error', error)
    return res.json({ code: 500, message: error.message })
  }
})

/**
 *  新的上传文件接口
 */
router.post('/upload-file', upload.array('file', MAX_COUNT_UPLOAD_LIMIT), async (req: Request, res: Response) => {
  const { sessionId, fileName } = req.body
  const file = (req.files as Express.Multer.File[])[0]

  // const originalnameUTF8 = Buffer.from(file.originalname, 'latin1').toString('utf8')

  const reqUrl = `${process.env.FLOW_API_URL}/sessions/${sessionId}/files`

  if (!sessionId) {
    return res.status(400).json({ code: 400, msg: 'sessionId为空' })
  }

  if (!file) {
    return res.status(400).json({ code: 400, msg: '未接收到文件' })
  }

  const form = new FormData()
  form.append('file', file.buffer, { filename: fileName })
  form.append('mimeType', file.mimetype)

  try {
    const result = await axios.post(reqUrl, form, {
      timeout: 2 * 60 * 1000, // 可能会有大文件上传， 所以设置2分钟
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })

    return res.json({ code: 0, data: result.data })
  } catch (error: any) {
    return res.json({ code: 500, message: error.message })
  }
})

export default router

/**
 * A simple in-memory cache
 */
type CacheContent<T> = {
  value: T
  expiry: number
}

const CACHE_STORE: Record<string, CacheContent<any>> = {}

function set<T>(key: string, value: T, ttl: number): void {
  const expiry = Date.now() + ttl
  CACHE_STORE[key] = { value, expiry }
  setTimeout(() => {
    delete CACHE_STORE[key]
  }, ttl)
}

function get<T>(key: string): T | null {
  const item = CACHE_STORE[key]
  if (item && Date.now() < item.expiry) {
    return item.value
  }
  // If the item is expired or doesn't exist, return null
  delete CACHE_STORE[key] // Cleanup expired item
  return null
}

function del(key: string): void {
  delete CACHE_STORE[key]
}

function clear(): void {
  Object.keys(CACHE_STORE).forEach((key) => delete CACHE_STORE[key])
}

export default {
  set,
  get,
  del,
  clear,
}
